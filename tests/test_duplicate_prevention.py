#!/usr/bin/env python3
"""
测试防重复机制
验证：
1. 相同特征的信号不会被重复保存
2. 防重复机制返回已存在的trade_id
3. 时间窗口外的相似信号可以正常保存
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def test_duplicate_prevention():
    """测试防重复机制"""
    logger.info("=== 测试防重复机制 ===")
    
    try:
        # 创建测试信号
        base_signal = {
            "signal_timestamp": datetime.utcnow().isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 99999.99,  # 使用特殊价格避免与真实交易冲突
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 50.0,
            "decision_details": {"test": "duplicate_prevention"}
        }
        
        # 1. 保存第一个信号
        logger.info("1. 保存第一个测试信号...")
        trade_id_1 = db.save_trade_signal(base_signal)
        logger.info(f"第一个信号保存成功，ID: {trade_id_1}")
        
        # 2. 尝试保存完全相同的信号
        logger.info("2. 尝试保存完全相同的信号...")
        trade_id_2 = db.save_trade_signal(base_signal)
        logger.info(f"第二个信号返回的ID: {trade_id_2}")
        
        if trade_id_1 == trade_id_2:
            logger.info("✅ 防重复机制生效，返回了相同的trade_id")
        else:
            logger.warning("❌ 防重复机制失效，创建了新的trade_id")
        
        # 3. 测试时间窗口外的相似信号
        logger.info("3. 测试时间窗口外的相似信号...")
        
        # 创建10秒前的信号
        old_time = datetime.utcnow() - timedelta(seconds=10)
        old_signal = base_signal.copy()
        old_signal["signal_timestamp"] = old_time.isoformat()
        
        trade_id_3 = db.save_trade_signal(old_signal)
        logger.info(f"时间窗口外信号的ID: {trade_id_3}")
        
        if trade_id_3 != trade_id_1:
            logger.info("✅ 时间窗口外的信号可以正常保存")
        else:
            logger.warning("❌ 时间窗口外的信号被错误阻止")
        
        # 4. 测试不同价格的信号
        logger.info("4. 测试不同价格的信号...")
        
        different_price_signal = base_signal.copy()
        different_price_signal["entry_price"] = 88888.88
        
        trade_id_4 = db.save_trade_signal(different_price_signal)
        logger.info(f"不同价格信号的ID: {trade_id_4}")
        
        if trade_id_4 != trade_id_1:
            logger.info("✅ 不同价格的信号可以正常保存")
        else:
            logger.warning("❌ 不同价格的信号被错误阻止")
        
        # 5. 验证数据库中的记录
        logger.info("5. 验证数据库中的记录...")
        
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            # 查找所有测试信号
            test_trades = session.query(TradeHistory).filter(
                TradeHistory.entry_price.in_([99999.99, 88888.88])
            ).all()
            
            logger.info(f"数据库中找到 {len(test_trades)} 条测试记录:")
            for trade in test_trades:
                logger.info(f"  ID: {trade.id}, 价格: {trade.entry_price}, 时间: {trade.signal_timestamp}")
        
        # 清理测试数据
        logger.info("6. 清理测试数据...")
        with db.get_session() as session:
            deleted_count = session.query(TradeHistory).filter(
                TradeHistory.entry_price.in_([99999.99, 88888.88])
            ).delete()
            session.commit()
            logger.info(f"清理了 {deleted_count} 条测试记录")
        
        logger.info("✅ 防重复机制测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_duplicate_prevention()
