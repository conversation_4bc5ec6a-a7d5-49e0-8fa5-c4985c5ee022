#!/usr/bin/env python3
"""
测试时区问题修复
验证simple_exit_manager能正确处理各种时间格式
"""

import asyncio
import sys
from datetime import datetime, timedelta, timezone
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import SimpleExitManager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_timezone_handling():
    """测试各种时间格式的处理"""
    
    logger.info("=" * 60)
    logger.info("Testing Timezone Handling")
    logger.info("=" * 60)
    
    # 创建管理器实例
    manager = SimpleExitManager()
    
    # 测试各种时间格式
    test_cases = [
        {
            "name": "ISO with Z",
            "timestamp": "2025-01-15T10:30:00Z",
            "trade_id": 1
        },
        {
            "name": "ISO with timezone",
            "timestamp": "2025-01-15T10:30:00+00:00",
            "trade_id": 2
        },
        {
            "name": "ISO without timezone",
            "timestamp": "2025-01-15T10:30:00",
            "trade_id": 3
        },
        {
            "name": "Datetime naive",
            "timestamp": datetime(2025, 1, 15, 10, 30, 0),
            "trade_id": 4
        },
        {
            "name": "Datetime aware",
            "timestamp": datetime(2025, 1, 15, 10, 30, 0, tzinfo=timezone.utc),
            "trade_id": 5
        },
        {
            "name": "Current time",
            "timestamp": datetime.utcnow(),
            "trade_id": 6
        }
    ]
    
    for test_case in test_cases:
        try:
            trade_data = {
                "id": test_case["trade_id"],
                "entry_price": 50000.0,
                "direction": "LONG",
                "symbol": "BTCUSDT",
                "suggested_bet": 10.0,
                "signal_timestamp": test_case["timestamp"]
            }
            
            # 添加到管理器
            manager.add_position(test_case["trade_id"], trade_data)
            
            # 获取添加的信息
            exit_info = manager._pending_exits.get(test_case["trade_id"])
            if exit_info:
                signal_time = exit_info["signal_time"]
                planned_exit = exit_info["planned_exit_time"]
                
                # 验证时间是naive的
                is_naive = signal_time.tzinfo is None
                
                logger.info(f"Test: {test_case['name']}")
                logger.info(f"  Input: {test_case['timestamp']}")
                logger.info(f"  Signal time: {signal_time.isoformat()}")
                logger.info(f"  Exit time: {planned_exit.isoformat()}")
                logger.info(f"  Is naive: {is_naive}")
                logger.info(f"  ✅ PASSED" if is_naive else f"  ❌ FAILED - datetime is aware")
            else:
                logger.error(f"Test: {test_case['name']} - Failed to add position")
                
        except Exception as e:
            logger.error(f"Test: {test_case['name']} - Error: {e}")
    
    # 测试时间比较
    logger.info("\n" + "=" * 60)
    logger.info("Testing Time Comparison")
    logger.info("=" * 60)
    
    try:
        # 启动管理器
        await manager.start()
        
        # 添加一个即将到期的交易
        current_time = datetime.utcnow()
        test_trade = {
            "id": 999,
            "entry_price": 50000.0,
            "direction": "SHORT",
            "symbol": "BTCUSDT",
            "suggested_bet": 10.0,
            "signal_timestamp": current_time - timedelta(minutes=25)  # 25分钟前的信号
        }
        
        manager.add_position(999, test_trade)
        
        # 等待几秒，让检查循环运行
        logger.info("Waiting for check loop to run...")
        await asyncio.sleep(5)
        
        # 检查是否有错误
        status = manager.get_status()
        logger.info(f"Manager status: {status}")
        
        if status["is_running"]:
            logger.info("✅ Check loop is running without timezone errors")
        else:
            logger.error("❌ Check loop stopped unexpectedly")
        
        # 停止管理器
        await manager.stop()
        
    except Exception as e:
        logger.error(f"Error in comparison test: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n" + "=" * 60)
    logger.info("Timezone Test Completed")
    logger.info("=" * 60)


if __name__ == "__main__":
    asyncio.run(test_timezone_handling()) 