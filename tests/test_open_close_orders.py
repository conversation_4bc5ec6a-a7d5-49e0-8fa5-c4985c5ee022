#!/usr/bin/env python3
"""
Test script to verify open/close order execution logic
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quant.strategies.auto_trader import auto_trader
from quant.settlement_checker import settlement_checker
from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_open_close_orders():
    """Test that orders are properly opened and closed"""
    logger.info("=== Testing Open/Close Order Logic ===")
    
    # Create a mock signal for testing
    mock_signal = {
        "symbol": "BTCUSDT",
        "direction": "LONG",
        "confidence_score": 0.75,
        "suggested_bet": 10.0,  # $10 USDT
        "entry_price": 60000.0,
        "signal_timestamp": datetime.utcnow().isoformat() + "Z",
        "trigger_pattern": "test_pattern",
        "market_state": "test_state",
    }
    
    # Save trade to database first
    trade_id = db.save_trade_signal({
        "signal_timestamp": mock_signal["signal_timestamp"],
        "symbol": mock_signal["symbol"],
        "direction": mock_signal["direction"],
        "entry_price": mock_signal["entry_price"],
        "confidence_score": mock_signal["confidence_score"],
        "market_state": mock_signal["market_state"],
        "trigger_pattern": mock_signal["trigger_pattern"],
        "confirmed_indicators": "[]",
        "suggested_bet": mock_signal["suggested_bet"],
        "status": "PENDING",
        "decision_details": {},  # Add required field
    })
    
    logger.info(f"Created test trade with ID: {trade_id}")
    
    # Test 1: Open position
    logger.info("1. Testing open position execution...")
    result = await auto_trader.handle_new_signal(mock_signal)
    
    if result.success:
        logger.info(f"✅ Open position successful: {result.message}")
        logger.info(f"Details: {result.details}")
    else:
        logger.error(f"❌ Open position failed: {result.message}")
        return False
    
    # Test 2: Get the trade for settlement test
    trade = db.get_trade_by_signal_timestamp(mock_signal["symbol"], mock_signal["signal_timestamp"])
    if not trade:
        logger.error("❌ Could not retrieve trade for settlement test")
        return False
    
    logger.info(f"Retrieved trade for settlement: {trade['id']}")
    
    # Test 3: Wait a moment then test settlement (close position)
    logger.info("2. Testing close position execution...")
    
    # Manually trigger settlement for testing
    try:
        settlement_result = await settlement_checker._settle_trade(trade)
        
        if settlement_result and "exit_price" in settlement_result:
            logger.info(f"✅ Close position successful")
            logger.info(f"Exit price: ${settlement_result['exit_price']:.2f}")
            logger.info(f"P&L: ${settlement_result['pnl']:.4f}")
            logger.info(f"Status: {settlement_result['status']}")
            logger.info(f"Settlement method: {settlement_result.get('settlement_method', 'N/A')}")
            
            if settlement_result.get("close_order_response"):
                order_resp = settlement_result["close_order_response"]
                if order_resp.get("simulated"):
                    logger.info("📝 Close order was simulated (no real exchange connection)")
                else:
                    logger.info(f"📋 Close order executed on exchange: {order_resp}")
        else:
            logger.error(f"❌ Close position failed: {settlement_result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Settlement test failed: {e}")
        return False
    
    logger.info("=== Test completed successfully ===")
    return True


async def test_pending_trades_settlement():
    """Test the full settlement check process"""
    logger.info("=== Testing Pending Trades Settlement Process ===")
    
    try:
        # Get pending trades
        pending_trades = db.get_pending_trades()
        logger.info(f"Found {len(pending_trades)} pending trades")
        
        if pending_trades:
            logger.info("Testing concurrent settlement process...")
            settled = await settlement_checker.check_pending_settlements_concurrent()
            logger.info(f"Settled {len(settled)} trades in batch")
            
            for trade in settled:
                logger.info(f"Trade {trade.get('trade_id')}: {trade.get('status')} (P&L: ${trade.get('pnl', 0):.4f})")
        else:
            logger.info("No pending trades to settle")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Pending trades settlement test failed: {e}")
        return False


if __name__ == "__main__":
    async def main():
        logger.info("Starting order execution tests...")
        
        # Test 1: Basic open/close logic
        success1 = await test_open_close_orders()
        
        # Test 2: Pending trades settlement
        success2 = await test_pending_trades_settlement()
        
        if success1 and success2:
            logger.info("🎉 All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
            
    asyncio.run(main())