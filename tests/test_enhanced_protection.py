#!/usr/bin/env python3
"""
测试增强的保护机制
验证：
1. 测试交易的更严格保护（2分钟）
2. 真实交易的标准保护（1分钟）
3. 不同类型交易的区别处理
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_enhanced_protection():
    """测试增强的保护机制"""
    logger.info("=== 测试增强的保护机制 ===")
    
    try:
        current_time = datetime.utcnow()
        
        # 1. 测试真实交易（1分钟保护）
        logger.info("1. 测试真实交易保护...")
        real_trade = {
            "id": 6666,
            "entry_price": 120000.0,  # 真实价格
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 10.0,
            "signal_timestamp": current_time
        }
        
        simple_exit_manager.add_position(6666, real_trade)
        
        if 6666 in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[6666]
            min_hold = exit_info.get("min_hold_minutes", 1.0)
            is_test = exit_info.get("is_test_trade", False)
            
            logger.info(f"  最小持仓时间: {min_hold} 分钟")
            logger.info(f"  是否测试交易: {is_test}")
            
            if min_hold == 1.0 and not is_test:
                logger.info("  ✅ 真实交易使用标准保护（1分钟）")
            else:
                logger.warning("  ❌ 真实交易保护设置异常")
        
        # 2. 测试测试交易（2分钟保护）
        logger.info("2. 测试测试交易保护...")
        test_trade = {
            "id": 5555,
            "entry_price": 50000.0,  # 测试价格
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 20.0,
            "signal_timestamp": current_time
        }
        
        simple_exit_manager.add_position(5555, test_trade)
        
        if 5555 in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[5555]
            min_hold = exit_info.get("min_hold_minutes", 1.0)
            is_test = exit_info.get("is_test_trade", False)
            
            logger.info(f"  最小持仓时间: {min_hold} 分钟")
            logger.info(f"  是否测试交易: {is_test}")
            
            if min_hold == 2.0 and is_test:
                logger.info("  ✅ 测试交易使用增强保护（2分钟）")
            else:
                logger.warning("  ❌ 测试交易保护设置异常")
        
        # 3. 测试立即平仓检查
        logger.info("3. 测试立即平仓检查...")
        await simple_exit_manager._check_exits()
        
        # 检查两个交易是否都还在队列中
        real_still_pending = 6666 in simple_exit_manager._pending_exits
        test_still_pending = 5555 in simple_exit_manager._pending_exits
        
        logger.info(f"  真实交易仍在队列: {real_still_pending}")
        logger.info(f"  测试交易仍在队列: {test_still_pending}")
        
        if real_still_pending and test_still_pending:
            logger.info("  ✅ 两种交易都未被立即平仓，保护机制生效")
        else:
            logger.warning("  ❌ 有交易被立即平仓，保护机制失效")
        
        # 4. 模拟时间推进测试
        logger.info("4. 模拟时间推进测试...")
        
        # 模拟1.5分钟后的检查
        future_time = current_time + timedelta(minutes=1.5)
        
        # 手动修改当前时间进行测试
        original_utcnow = datetime.utcnow
        datetime.utcnow = lambda: future_time
        
        try:
            await simple_exit_manager._check_exits()
            
            real_still_pending = 6666 in simple_exit_manager._pending_exits
            test_still_pending = 5555 in simple_exit_manager._pending_exits
            
            logger.info(f"  1.5分钟后 - 真实交易仍在队列: {real_still_pending}")
            logger.info(f"  1.5分钟后 - 测试交易仍在队列: {test_still_pending}")
            
            # 真实交易应该可以平仓（超过1分钟），测试交易应该仍被保护（未超过2分钟）
            if not real_still_pending and test_still_pending:
                logger.info("  ✅ 差异化保护机制正常工作")
            else:
                logger.info("  ℹ️ 注意：由于未到实际平仓时间，交易可能仍在队列中")
        
        finally:
            # 恢复原始函数
            datetime.utcnow = original_utcnow
        
        # 清理测试数据
        logger.info("5. 清理测试数据...")
        for trade_id in [6666, 5555]:
            if trade_id in simple_exit_manager._pending_exits:
                del simple_exit_manager._pending_exits[trade_id]
                logger.info(f"  清理交易 {trade_id}")
        
        logger.info("✅ 增强保护机制测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_enhanced_protection())
