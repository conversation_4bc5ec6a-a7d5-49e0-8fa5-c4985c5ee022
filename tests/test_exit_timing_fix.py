#!/usr/bin/env python3
"""
测试 SimpleExitManager 时间处理修复
验证：
1. 时区处理是否正确
2. 最小持仓时间保护是否生效
3. 不再出现"秒开秒平"问题
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_timing_fix():
    """测试时间处理修复"""
    logger.info("=== 测试 SimpleExitManager 时间处理修复 ===")
    
    try:
        # 1. 测试时区处理
        logger.info("1. 测试时区处理...")
        
        current_time = datetime.utcnow()
        
        # 创建不同格式的时间戳
        test_cases = [
            {
                "name": "naive_datetime",
                "signal_timestamp": current_time,
                "description": "原生 datetime 对象"
            },
            {
                "name": "iso_string",
                "signal_timestamp": current_time.isoformat(),
                "description": "ISO 格式字符串"
            },
            {
                "name": "iso_with_z",
                "signal_timestamp": current_time.isoformat() + "Z",
                "description": "带 Z 的 ISO 格式"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            trade_id = 9000 + i
            trade_data = {
                "id": trade_id,
                "entry_price": 50000.0,
                "direction": "LONG",
                "symbol": "BTCUSDT",
                "suggested_bet": 10.0,
                "signal_timestamp": test_case["signal_timestamp"]
            }
            
            logger.info(f"测试用例 {i}: {test_case['description']}")
            logger.info(f"  输入时间: {test_case['signal_timestamp']}")
            
            try:
                simple_exit_manager.add_position(trade_id, trade_data)
                
                # 检查是否成功添加
                if trade_id in simple_exit_manager._pending_exits:
                    exit_info = simple_exit_manager._pending_exits[trade_id]
                    logger.info(f"  ✅ 成功添加，计划平仓时间: {exit_info['planned_exit_time']}")
                    
                    # 检查最小持仓时间保护
                    signal_time = exit_info['signal_time']
                    planned_exit = exit_info['planned_exit_time']
                    min_exit = exit_info['min_exit_time']
                    
                    min_duration = (min_exit - signal_time).total_seconds() / 60
                    actual_duration = (planned_exit - signal_time).total_seconds() / 60
                    
                    logger.info(f"  最小持仓时间: {min_duration:.2f} 分钟")
                    logger.info(f"  实际持仓时间: {actual_duration:.2f} 分钟")
                    
                    if actual_duration >= simple_exit_manager.min_hold_minutes:
                        logger.info(f"  ✅ 最小持仓时间保护生效")
                    else:
                        logger.warning(f"  ❌ 最小持仓时间保护失效")
                else:
                    logger.error(f"  ❌ 添加失败")
                    
            except Exception as e:
                logger.error(f"  ❌ 异常: {e}")
            
            logger.info("")
        
        # 2. 测试时间比较逻辑
        logger.info("2. 测试时间比较逻辑...")
        
        # 模拟检查过程
        logger.info("模拟执行 _check_exits()...")
        try:
            await simple_exit_manager._check_exits()
            logger.info("✅ _check_exits() 执行成功，无时区错误")
        except Exception as e:
            logger.error(f"❌ _check_exits() 执行失败: {e}")
        
        # 3. 显示当前状态
        logger.info("3. 当前状态...")
        status = simple_exit_manager.get_status()
        logger.info(f"待平仓交易数量: {status['pending_exits']}")
        
        # 清理测试数据
        logger.info("4. 清理测试数据...")
        for i in range(1, 4):
            trade_id = 9000 + i
            if trade_id in simple_exit_manager._pending_exits:
                del simple_exit_manager._pending_exits[trade_id]
                logger.info(f"清理交易 {trade_id}")
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_min_hold_protection():
    """测试最小持仓时间保护"""
    logger.info("=== 测试最小持仓时间保护 ===")
    
    try:
        # 创建一个应该立即平仓的交易（如果没有保护）
        current_time = datetime.utcnow()
        
        # 设置信号时间为当前时间
        trade_data = {
            "id": 9999,
            "entry_price": 50000.0,
            "direction": "LONG", 
            "symbol": "BTCUSDT",
            "suggested_bet": 10.0,
            "signal_timestamp": current_time
        }
        
        logger.info(f"当前时间: {current_time.isoformat()}")
        
        # 添加交易
        simple_exit_manager.add_position(9999, trade_data)
        
        if 9999 in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[9999]
            
            signal_time = exit_info['signal_time']
            planned_exit = exit_info['planned_exit_time']
            original_exit = exit_info['original_exit_time']
            min_exit = exit_info['min_exit_time']
            
            logger.info(f"信号时间: {signal_time.isoformat()}")
            logger.info(f"原始平仓时间: {original_exit.isoformat()}")
            logger.info(f"最小平仓时间: {min_exit.isoformat()}")
            logger.info(f"实际平仓时间: {planned_exit.isoformat()}")
            
            # 检查保护是否生效
            if planned_exit == min_exit and planned_exit > original_exit:
                logger.info("✅ 最小持仓时间保护生效")
            elif planned_exit == original_exit:
                logger.info("ℹ️ 原始平仓时间已满足最小持仓要求")
            else:
                logger.warning("❓ 平仓时间逻辑异常")
            
            # 立即检查是否会被平仓
            logger.info("立即执行平仓检查...")
            await simple_exit_manager._check_exits()
            
            # 检查交易是否还在队列中
            if 9999 in simple_exit_manager._pending_exits:
                logger.info("✅ 交易未被立即平仓，保护生效")
            else:
                logger.warning("❌ 交易被立即平仓，保护失效")
            
            # 清理
            if 9999 in simple_exit_manager._pending_exits:
                del simple_exit_manager._pending_exits[9999]
        
        logger.info("✅ 最小持仓时间保护测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    async def main():
        await test_timing_fix()
        print()
        await test_min_hold_protection()
    
    asyncio.run(main())
