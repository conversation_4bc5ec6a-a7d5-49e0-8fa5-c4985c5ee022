#!/usr/bin/env python3
"""
简单平仓流程模拟测试（开发模式，无真实下单）
目标：
- 验证 SimpleExitManager 的重试配置与异常日志是否生效
- 在模拟环境下触发一次完整的平仓流程（价格->下单->DB更新->通知）
- 打印关键结果，便于人工核对

运行方式：
python3 tests/test_exit_sim.py
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def main():
    # 设置开发环境标志（仅用于日志标识；binance_client 在未 initialize 时默认模拟）
    os.environ["ENVIRONMENT"] = "development"

    # 初始化数据库
    db.init_database()

    # 避免真实外呼，关闭钉钉
    notification_manager.webhook_url = ""

    # 配置重试参数更快出结果
    simple_exit_manager.exit_max_retries = 2
    simple_exit_manager.exit_retry_backoff_seconds = 0.2

    # 构建并保存一条待结算交易（状态 PENDING）
    now = datetime.utcnow()
    signal_ts = now.isoformat()
    signal = {
        "signal_timestamp": signal_ts,
        "symbol": "BTCUSDT",
        "direction": "LONG",
        "entry_price": 50000.0,
        "confidence_score": 0.75,
        "market_state": "TESTING",
        "trigger_pattern": "UNIT_TEST",
        "confirmed_indicators": ["rsi", "macd"],
        "suggested_bet": 20.0,
        "decision_details": {"calculation_details": {"weights_used": {}}},
        # 可选扩展字段
        "confidence_breakdown": {"trend_score": 0.7},
        "signal_strength": 2,
    }
    trade_id = db.save_trade_signal(signal)
    logger.info(f"Created mock pending trade_id={trade_id}")

    # 将该交易加入平仓管理器队列
    trade_data = {
        "id": trade_id,
        "entry_price": signal["entry_price"],
        "direction": signal["direction"],
        "symbol": signal["symbol"],
        "suggested_bet": signal["suggested_bet"],
        "signal_timestamp": signal_ts,
    }
    simple_exit_manager.add_position(trade_id, trade_data)

    # 将计划平仓时间提前到现在之前，确保触发
    simple_exit_manager._pending_exits[trade_id]["planned_exit_time"] = datetime.utcnow() - timedelta(seconds=1)

    # 直接调用执行函数（也可改为 await simple_exit_manager._check_exits()）
    exit_info = simple_exit_manager._pending_exits[trade_id]
    logger.info("Invoking _execute_exit directly for test...")
    success = await simple_exit_manager._execute_exit(trade_id, exit_info)
    logger.info(f"Execute exit returned: {success}")

    # 若使用 _check_exits，会自动从队列移除；这里只做手动清理
    if success:
        simple_exit_manager._pending_exits.pop(trade_id, None)

    # 查询数据库验证状态（最近 N 条交易）
    recent = db.get_optimized_trade_history(limit=5)
    logger.info({"recent_trades": recent})


if __name__ == "__main__":
    asyncio.run(main())

