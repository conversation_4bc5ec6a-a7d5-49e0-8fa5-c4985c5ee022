#!/usr/bin/env python3
"""
测试交易ID匹配错误修复效果
验证：
1. 严格的ID验证机制
2. 平仓锁机制
3. 数据库状态验证
4. 系统健康检查
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_id_validation_fix():
    """测试ID验证修复"""
    logger.info("=== 测试ID验证修复 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        simple_exit_manager._processing_locks.clear()
        
        # 1. 创建测试交易
        logger.info("1. 创建测试交易...")
        
        test_signal = {
            "signal_timestamp": datetime.utcnow().isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "id_validation_fix"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        # 2. 测试正确的ID匹配
        logger.info("2. 测试正确的ID匹配...")
        
        trade_data = {
            "id": db_id,
            "entry_price": 120000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": test_signal["signal_timestamp"]
        }
        
        exit_info = {
            "trade_data": trade_data,
            "signal_time": datetime.utcnow(),
            "planned_exit_time": datetime.utcnow() - timedelta(seconds=1),
            "status": "PENDING"
        }
        
        # 这应该成功（但会因为模拟交易所失败）
        result = await simple_exit_manager._execute_exit(db_id, exit_info)
        logger.info(f"正确ID匹配结果: {result}")
        
        # 3. 测试错误的ID匹配
        logger.info("3. 测试错误的ID匹配...")
        
        wrong_trade_data = {
            "id": db_id + 1000,  # 错误的ID
            "entry_price": 120000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": test_signal["signal_timestamp"]
        }
        
        wrong_exit_info = {
            "trade_data": wrong_trade_data,
            "signal_time": datetime.utcnow(),
            "planned_exit_time": datetime.utcnow() - timedelta(seconds=1),
            "status": "PENDING"
        }
        
        # 这应该失败
        result = await simple_exit_manager._execute_exit(db_id, wrong_exit_info)
        
        if not result:
            logger.info("✅ 错误ID匹配被正确拒绝")
        else:
            logger.warning("❌ 错误ID匹配未被拒绝")
        
        # 4. 测试数据库状态验证
        logger.info("4. 测试数据库状态验证...")
        
        # 先将交易状态改为WIN
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            if trade:
                trade.status = "WIN"
                session.commit()
        
        # 现在尝试平仓，应该失败
        result = await simple_exit_manager._execute_exit(db_id, exit_info)
        
        if not result:
            logger.info("✅ 非PENDING状态交易被正确拒绝")
        else:
            logger.warning("❌ 非PENDING状态交易未被拒绝")
        
        # 清理测试数据
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ ID验证修复测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_processing_lock_fix():
    """测试平仓锁机制修复"""
    logger.info("=== 测试平仓锁机制修复 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        simple_exit_manager._processing_locks.clear()
        
        # 1. 创建测试交易
        test_signal = {
            "signal_timestamp": datetime.utcnow().isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 121000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "lock_mechanism_fix"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        trade_data = {
            "id": db_id,
            "entry_price": 121000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": test_signal["signal_timestamp"]
        }
        
        exit_info = {
            "trade_data": trade_data,
            "signal_time": datetime.utcnow(),
            "planned_exit_time": datetime.utcnow() - timedelta(seconds=1),
            "status": "PENDING"
        }
        
        # 2. 测试并发处理保护
        logger.info("2. 测试并发处理保护...")
        
        # 手动加锁
        simple_exit_manager._processing_locks.add(db_id)
        
        # 尝试处理，应该被拒绝
        result = await simple_exit_manager._execute_exit(db_id, exit_info)
        
        if not result:
            logger.info("✅ 已锁定的交易被正确拒绝")
        else:
            logger.warning("❌ 已锁定的交易未被拒绝")
        
        # 解锁
        simple_exit_manager._processing_locks.discard(db_id)
        
        # 3. 测试正常处理后的自动解锁
        logger.info("3. 测试自动解锁机制...")
        
        # 检查锁状态
        initial_locks = len(simple_exit_manager._processing_locks)
        
        # 执行处理（会失败但应该自动解锁）
        await simple_exit_manager._execute_exit(db_id, exit_info)
        
        # 检查锁是否被清理
        final_locks = len(simple_exit_manager._processing_locks)
        
        if final_locks == initial_locks:
            logger.info("✅ 处理完成后锁被正确清理")
        else:
            logger.warning("❌ 处理完成后锁未被清理")
        
        # 清理测试数据
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 平仓锁机制修复测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_system_health_check():
    """测试系统健康检查功能"""
    logger.info("=== 测试系统健康检查功能 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        simple_exit_manager._processing_locks.clear()
        
        # 1. 测试空队列的健康检查
        logger.info("1. 测试空队列健康检查...")
        
        result = simple_exit_manager.validate_exit_queue_integrity()
        if result:
            logger.info("✅ 空队列健康检查通过")
        else:
            logger.warning("❌ 空队列健康检查失败")
        
        # 2. 创建正常的测试交易
        logger.info("2. 创建正常测试交易...")
        
        test_signal = {
            "signal_timestamp": datetime.utcnow().isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 122000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "health_check"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        
        # 添加到队列
        trade_data = {
            "id": db_id,
            "entry_price": 122000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": test_signal["signal_timestamp"]
        }
        
        simple_exit_manager._pending_exits[db_id] = {
            "trade_data": trade_data,
            "signal_time": datetime.utcnow(),
            "planned_exit_time": datetime.utcnow() + timedelta(minutes=5),
            "status": "PENDING"
        }
        
        # 测试健康检查
        result = simple_exit_manager.validate_exit_queue_integrity()
        if result:
            logger.info("✅ 正常交易健康检查通过")
        else:
            logger.warning("❌ 正常交易健康检查失败")
        
        # 3. 测试队列状态获取
        logger.info("3. 测试队列状态获取...")
        
        status = simple_exit_manager.get_queue_status()
        logger.info(f"队列状态: {status}")
        
        if status["total_trades"] == 1:
            logger.info("✅ 队列状态正确")
        else:
            logger.warning("❌ 队列状态不正确")
        
        # 清理测试数据
        simple_exit_manager._pending_exits.clear()
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 系统健康检查功能测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_id_validation_fix())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_processing_lock_fix())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_system_health_check())
