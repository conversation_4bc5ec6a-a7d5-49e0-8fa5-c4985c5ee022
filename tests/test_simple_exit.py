#!/usr/bin/env python3
"""
Test Simple Exit Manager
测试简化的平仓管理器功能
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger
from quant.binance_client import binance_client
from quant.database_manager import db

logger = get_logger(__name__)


async def test_simple_exit():
    """测试简化平仓管理器"""
    
    logger.info("=" * 60)
    logger.info("Testing Simple Exit Manager")
    logger.info("=" * 60)
    
    try:
        # 1. 初始化
        logger.info("1. Initializing components...")
        await binance_client.initialize()
        db.init_database()
        
        # 2. 启动平仓管理器
        logger.info("2. Starting simple exit manager...")
        await simple_exit_manager.start()
        
        # 3. 创建模拟交易
        current_time = datetime.utcnow()
        mock_trade = {
            "id": 99999,  # 测试ID
            "entry_price": 50000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 10.0,
            "signal_timestamp": current_time
        }
        
        # 4. 添加到平仓管理器
        logger.info(f"3. Adding mock trade to exit manager...")
        simple_exit_manager.add_position(mock_trade["id"], mock_trade)
        
        # 5. 显示状态
        status = simple_exit_manager.get_status()
        logger.info(f"4. Exit manager status:")
        logger.info(f"   - Running: {status['is_running']}")
        logger.info(f"   - Pending exits: {status['pending_exits']}")
        
        for pos in status["positions"]:
            logger.info(f"   - Trade {pos['trade_id']}: {pos['direction']} @ ${pos['entry_price']}")
            logger.info(f"     Exit time: {pos['planned_exit_time']}")
            logger.info(f"     Time to exit: {pos['time_to_exit_minutes']} minutes")
            logger.info(f"     Status: {pos['status']}")
        
        # 6. 计算K线结束时间
        minute = (current_time.minute // 30) * 30
        kline_start = current_time.replace(minute=minute, second=0, microsecond=0)
        kline_end = kline_start + timedelta(minutes=30)
        
        logger.info(f"5. K-line timing:")
        logger.info(f"   - Current time: {current_time.isoformat()}")
        logger.info(f"   - K-line start: {kline_start.isoformat()}")
        logger.info(f"   - K-line end: {kline_end.isoformat()}")
        logger.info(f"   - Exit before end: {simple_exit_manager.exit_before_kline_end_minutes} minutes")
        
        # 7. 等待几秒观察日志
        logger.info("6. Monitoring for 30 seconds...")
        await asyncio.sleep(30)
        
        # 8. 再次显示状态
        status = simple_exit_manager.get_status()
        logger.info(f"7. Updated status:")
        logger.info(f"   - Pending exits: {status['pending_exits']}")
        
        # 9. 测试强制平仓
        if status['pending_exits'] > 0:
            logger.info("8. Testing force exit...")
            await simple_exit_manager.force_exit_all()
            
            # 显示最终状态
            status = simple_exit_manager.get_status()
            logger.info(f"9. Final status after force exit:")
            logger.info(f"   - Pending exits: {status['pending_exits']}")
        
        # 10. 停止管理器
        logger.info("10. Stopping exit manager...")
        await simple_exit_manager.stop()
        
        logger.info("✅ Test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        await binance_client.close()


async def test_exit_timing():
    """测试平仓时间计算"""
    
    logger.info("=" * 60)
    logger.info("Testing Exit Timing Calculation")
    logger.info("=" * 60)
    
    # 测试不同的信号时间
    test_times = [
        datetime(2024, 1, 1, 10, 5, 0),   # 10:05 -> K线结束 10:30
        datetime(2024, 1, 1, 10, 25, 0),  # 10:25 -> K线结束 10:30
        datetime(2024, 1, 1, 10, 31, 0),  # 10:31 -> K线结束 11:00
        datetime(2024, 1, 1, 10, 59, 0),  # 10:59 -> K线结束 11:00
    ]
    
    for signal_time in test_times:
        # 计算K线结束时间
        minute = (signal_time.minute // 30) * 30
        kline_start = signal_time.replace(minute=minute, second=0, microsecond=0)
        kline_end = kline_start + timedelta(minutes=30)
        
        # 计算平仓时间（K线结束前2分钟）
        exit_time = kline_end - timedelta(minutes=2)
        
        logger.info(f"Signal at {signal_time.strftime('%H:%M:%S')}:")
        logger.info(f"  K-line: {kline_start.strftime('%H:%M')} - {kline_end.strftime('%H:%M')}")
        logger.info(f"  Exit at: {exit_time.strftime('%H:%M:%S')}")
        logger.info("")


if __name__ == "__main__":
    # 运行测试
    print("Select test:")
    print("1. Test simple exit manager")
    print("2. Test exit timing calculation")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_simple_exit())
    elif choice == "2":
        asyncio.run(test_exit_timing())
    else:
        print("Invalid choice") 