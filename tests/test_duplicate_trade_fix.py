#!/usr/bin/env python3
"""
测试重复交易修复
验证：
1. 信号只被保存一次到数据库
2. 不会产生重复的trade_id
3. auto_trader不会执行重复交易
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.simple_analysis_engine import analysis_engine
from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_duplicate_trade_fix():
    """测试重复交易修复"""
    logger.info("=== 测试重复交易修复 ===")
    
    try:
        # 记录测试前的交易数量
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            initial_count = session.query(TradeHistory).count()
            logger.info(f"测试前数据库中的交易记录数量: {initial_count}")
        
        # 1. 测试analysis_engine.analyze_market()不再自动保存
        logger.info("1. 测试analysis_engine不再自动保存信号...")
        
        signal = await analysis_engine.analyze_market()
        
        if signal:
            logger.info(f"生成信号: {signal['direction']}, 置信度: {signal['confidence_score']}")
            
            # 检查数据库中的记录数量是否增加
            with db.get_session() as session:
                after_analysis_count = session.query(TradeHistory).count()
                logger.info(f"analysis_engine执行后的交易记录数量: {after_analysis_count}")
                
                if after_analysis_count == initial_count:
                    logger.info("✅ analysis_engine不再自动保存信号到数据库")
                else:
                    logger.warning("❌ analysis_engine仍在自动保存信号")
            
            # 2. 模拟main.py中的保存逻辑
            logger.info("2. 测试统一保存逻辑...")
            
            if not signal.get("analysis_only"):
                trade_id = db.save_trade_signal(signal)
                signal["trade_id"] = trade_id
                logger.info(f"信号保存到数据库，ID: {trade_id}")
                
                # 检查最终的记录数量
                with db.get_session() as session:
                    final_count = session.query(TradeHistory).count()
                    logger.info(f"最终交易记录数量: {final_count}")
                    
                    if final_count == initial_count + 1:
                        logger.info("✅ 信号只被保存一次")
                    else:
                        logger.warning(f"❌ 信号被保存了 {final_count - initial_count} 次")
                
                # 3. 验证没有重复的信号
                logger.info("3. 检查是否存在重复信号...")
                
                signal_timestamp = signal.get("signal_timestamp")
                entry_price = signal.get("entry_price")
                direction = signal.get("direction")
                
                with db.get_session() as session:
                    duplicate_trades = session.query(TradeHistory).filter(
                        TradeHistory.signal_timestamp == signal_timestamp,
                        TradeHistory.entry_price == entry_price,
                        TradeHistory.direction == direction
                    ).all()
                    
                    logger.info(f"找到相同特征的交易记录: {len(duplicate_trades)} 个")
                    
                    if len(duplicate_trades) == 1:
                        logger.info("✅ 没有重复的交易记录")
                    else:
                        logger.warning(f"❌ 发现 {len(duplicate_trades)} 个重复的交易记录")
                        for trade in duplicate_trades:
                            logger.warning(f"  ID: {trade.id}, 创建时间: {trade.created_at}")
            
            else:
                logger.info("这是analysis_only信号，不会保存到数据库")
        
        else:
            logger.info("本次分析未生成信号")
        
        logger.info("✅ 重复交易修复测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_signal_generation_flow():
    """测试完整的信号生成流程"""
    logger.info("=== 测试完整信号生成流程 ===")
    
    try:
        # 模拟main.py中的market_analysis_and_trade_task逻辑
        logger.info("模拟market_analysis_and_trade_task流程...")
        
        # 记录初始状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            initial_count = session.query(TradeHistory).count()
        
        # 1. 分析市场并生成信号
        signal = await analysis_engine.analyze_market()
        
        if signal:
            # 2. 添加元数据
            signal["original_signal_id"] = f"{signal.get('signal_timestamp', '')}_{signal.get('direction', '')}"
            signal["analysis_latency_ms"] = 100  # 模拟值
            
            # 3. 保存信号（只保存一次）
            if not signal.get("analysis_only"):
                trade_id = db.save_trade_signal(signal)
                signal["trade_id"] = trade_id
                logger.info(f"信号保存成功，ID: {trade_id}")
            
            # 4. 验证结果
            with db.get_session() as session:
                final_count = session.query(TradeHistory).count()
                added_count = final_count - initial_count
                
                logger.info(f"新增交易记录数量: {added_count}")
                
                if added_count == 1:
                    logger.info("✅ 完整流程测试成功，信号只被保存一次")
                elif added_count == 0:
                    logger.info("✅ analysis_only信号未被保存，符合预期")
                else:
                    logger.warning(f"❌ 异常：新增了 {added_count} 条记录")
        
        else:
            logger.info("未生成信号，测试正常结束")
            
    except Exception as e:
        logger.error(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_duplicate_trade_fix())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_signal_generation_flow())
