#!/usr/bin/env python3
"""
测试交易ID匹配错误问题
重现场景：
1. 交易383在13:09开仓，计划在13:28平仓
2. 交易384在13:39开仓，但被错误地用383的ID进行平仓
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_id_matching_issue():
    """测试交易ID匹配问题"""
    logger.info("=== 测试交易ID匹配问题 ===")
    
    try:
        # 清理测试环境
        simple_exit_manager._pending_exits.clear()
        
        # 1. 创建第一个交易（模拟交易383）
        logger.info("1. 创建第一个交易（模拟383）...")
        
        signal_time_1 = datetime.utcnow()
        trade_data_1 = {
            "id": 9001,  # 使用测试ID
            "entry_price": 120434.4,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": signal_time_1.isoformat()
        }
        
        # 添加到SimpleExitManager
        simple_exit_manager.add_position(9001, trade_data_1)
        logger.info(f"交易9001添加到退出管理器，计划平仓时间: {simple_exit_manager._pending_exits[9001]['planned_exit_time']}")
        
        # 2. 等待一段时间，然后创建第二个交易（模拟交易384）
        logger.info("2. 等待30秒后创建第二个交易...")
        await asyncio.sleep(2)  # 缩短等待时间用于测试
        
        signal_time_2 = datetime.utcnow()
        trade_data_2 = {
            "id": 9002,  # 使用测试ID
            "entry_price": 120599.89,
            "direction": "LONG", 
            "symbol": "BTCUSDT",
            "suggested_bet": 5.0,
            "signal_timestamp": signal_time_2.isoformat()
        }
        
        # 添加到SimpleExitManager
        simple_exit_manager.add_position(9002, trade_data_2)
        logger.info(f"交易9002添加到退出管理器，计划平仓时间: {simple_exit_manager._pending_exits[9002]['planned_exit_time']}")
        
        # 3. 检查当前队列状态
        logger.info("3. 检查当前队列状态...")
        logger.info(f"队列中的交易: {list(simple_exit_manager._pending_exits.keys())}")
        
        for trade_id, exit_info in simple_exit_manager._pending_exits.items():
            logger.info(f"  交易{trade_id}: 计划平仓时间 {exit_info['planned_exit_time']}")
        
        # 4. 模拟时间推进，触发第一个交易的平仓时间
        logger.info("4. 模拟触发平仓检查...")
        
        # 将第一个交易的平仓时间设置为过去时间
        past_time = datetime.utcnow() - timedelta(seconds=1)
        simple_exit_manager._pending_exits[9001]["planned_exit_time"] = past_time
        
        logger.info(f"将交易9001的平仓时间设置为: {past_time}")
        
        # 5. 执行平仓检查
        logger.info("5. 执行平仓检查...")
        await simple_exit_manager._check_exits()
        
        # 6. 检查结果
        logger.info("6. 检查平仓结果...")
        remaining_trades = list(simple_exit_manager._pending_exits.keys())
        logger.info(f"剩余交易: {remaining_trades}")
        
        if 9001 not in remaining_trades and 9002 in remaining_trades:
            logger.info("✅ 正确：交易9001被平仓，交易9002仍在队列中")
        elif 9001 in remaining_trades and 9002 not in remaining_trades:
            logger.warning("❌ 错误：交易9002被错误平仓，交易9001仍在队列中")
        elif 9001 not in remaining_trades and 9002 not in remaining_trades:
            logger.warning("❌ 异常：两个交易都被平仓了")
        else:
            logger.info("两个交易都还在队列中")
        
        # 7. 分析get_pending_trades的行为
        logger.info("7. 分析数据库查询行为...")
        
        # 创建测试交易记录
        test_signal_1 = {
            "signal_timestamp": signal_time_1.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120434.4,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "id_matching_1"}
        }
        
        test_signal_2 = {
            "signal_timestamp": signal_time_2.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120599.89,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 5.0,
            "decision_details": {"test": "id_matching_2"}
        }
        
        # 保存到数据库
        db_id_1 = db.save_trade_signal(test_signal_1)
        db_id_2 = db.save_trade_signal(test_signal_2)
        
        logger.info(f"数据库中的交易ID: {db_id_1}, {db_id_2}")
        
        # 查询PENDING交易
        pending_trades = db.get_pending_trades()
        logger.info(f"数据库中的PENDING交易数量: {len(pending_trades)}")
        
        for trade in pending_trades[-2:]:  # 只看最后两个
            logger.info(f"  ID: {trade['id']}, 信号时间: {trade['signal_timestamp']}, 价格: {trade['entry_price']}")
        
        # 清理测试数据
        logger.info("8. 清理测试数据...")
        simple_exit_manager._pending_exits.clear()
        
        # 清理数据库测试记录
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            session.query(TradeHistory).filter(
                TradeHistory.id.in_([db_id_1, db_id_2])
            ).delete()
            session.commit()
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_seed_pending_behavior():
    """测试_seed_pending_from_db的行为"""
    logger.info("=== 测试_seed_pending_from_db行为 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        
        # 创建两个测试交易
        signal_time_1 = datetime.utcnow() - timedelta(minutes=5)
        signal_time_2 = datetime.utcnow() - timedelta(minutes=1)
        
        test_signal_1 = {
            "signal_timestamp": signal_time_1.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "seed_test_1"}
        }
        
        test_signal_2 = {
            "signal_timestamp": signal_time_2.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 121000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 5.0,
            "decision_details": {"test": "seed_test_2"}
        }
        
        # 保存到数据库
        db_id_1 = db.save_trade_signal(test_signal_1)
        db_id_2 = db.save_trade_signal(test_signal_2)
        
        logger.info(f"创建测试交易: {db_id_1}, {db_id_2}")
        
        # 执行seed操作
        await simple_exit_manager._seed_pending_from_db()
        
        # 检查结果
        seeded_trades = list(simple_exit_manager._pending_exits.keys())
        logger.info(f"从数据库回填的交易: {seeded_trades}")
        
        # 检查是否包含我们的测试交易
        if db_id_1 in seeded_trades and db_id_2 in seeded_trades:
            logger.info("✅ 两个测试交易都被正确回填")
        else:
            logger.warning(f"❌ 回填不完整: 期望 [{db_id_1}, {db_id_2}], 实际 {seeded_trades}")
        
        # 清理
        simple_exit_manager._pending_exits.clear()
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            session.query(TradeHistory).filter(
                TradeHistory.id.in_([db_id_1, db_id_2])
            ).delete()
            session.commit()
        
        logger.info("✅ seed测试完成")
        
    except Exception as e:
        logger.error(f"❌ seed测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_id_matching_issue())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_seed_pending_behavior())
