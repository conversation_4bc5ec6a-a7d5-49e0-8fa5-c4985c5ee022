#!/usr/bin/env python3
"""
精确重现交易ID匹配错误问题
模拟真实场景：
1. 交易383在13:09开仓，计划13:28平仓
2. 交易384在13:39开仓，但被错误地立即平仓
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.settlement_checker import settlement_checker
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_precise_id_matching_issue():
    """精确重现ID匹配问题"""
    logger.info("=== 精确重现交易ID匹配问题 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        
        # 1. 创建交易383的场景（13:09开仓）
        logger.info("1. 创建交易383场景...")
        
        # 模拟13:09的时间
        signal_time_383 = datetime(2025, 8, 13, 13, 9, 0, 279643)
        
        # 创建数据库记录
        test_signal_383 = {
            "signal_timestamp": signal_time_383.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120434.4,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 1.0,
            "decision_details": {"test": "id_383_simulation"}
        }
        
        db_id_383 = db.save_trade_signal(test_signal_383)
        logger.info(f"创建交易383模拟记录，数据库ID: {db_id_383}")
        
        # 添加到SimpleExitManager
        trade_data_383 = {
            "id": db_id_383,
            "entry_price": 120434.4,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 1.0,
            "signal_timestamp": signal_time_383.isoformat()
        }
        
        simple_exit_manager.add_position(db_id_383, trade_data_383)
        
        # 检查计划平仓时间
        exit_info_383 = simple_exit_manager._pending_exits[db_id_383]
        logger.info(f"交易383计划平仓时间: {exit_info_383['planned_exit_time']}")
        
        # 2. 模拟时间推进到13:39
        logger.info("2. 模拟时间推进到13:39...")
        
        # 模拟13:39的时间
        signal_time_384 = datetime(2025, 8, 13, 13, 39, 1, 54094)
        
        # 创建交易384
        test_signal_384 = {
            "signal_timestamp": signal_time_384.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120599.89,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 5.0,
            "decision_details": {"test": "id_384_simulation"}
        }
        
        db_id_384 = db.save_trade_signal(test_signal_384)
        logger.info(f"创建交易384模拟记录，数据库ID: {db_id_384}")
        
        # 3. 检查此时交易383是否应该被平仓
        logger.info("3. 检查交易383是否应该被平仓...")
        
        current_time = signal_time_384  # 使用13:39作为当前时间
        planned_exit_383 = exit_info_383['planned_exit_time']
        
        logger.info(f"当前时间: {current_time}")
        logger.info(f"交易383计划平仓时间: {planned_exit_383}")
        
        if current_time >= planned_exit_383:
            logger.info("✅ 交易383确实应该在此时被平仓")
        else:
            logger.info("❌ 交易383还不应该被平仓")
        
        # 4. 添加交易384到SimpleExitManager
        logger.info("4. 添加交易384到SimpleExitManager...")
        
        trade_data_384 = {
            "id": db_id_384,
            "entry_price": 120599.89,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 5.0,
            "signal_timestamp": signal_time_384.isoformat()
        }
        
        simple_exit_manager.add_position(db_id_384, trade_data_384)
        
        # 检查队列状态
        logger.info(f"队列中的交易: {list(simple_exit_manager._pending_exits.keys())}")
        
        # 5. 模拟平仓检查（使用13:39的时间）
        logger.info("5. 模拟平仓检查...")
        
        # 手动设置当前时间为13:39
        original_utcnow = datetime.utcnow
        datetime.utcnow = lambda: signal_time_384
        
        try:
            await simple_exit_manager._check_exits()
        finally:
            # 恢复原始的utcnow函数
            datetime.utcnow = original_utcnow
        
        # 6. 检查结果
        logger.info("6. 检查平仓结果...")
        
        remaining_trades = list(simple_exit_manager._pending_exits.keys())
        logger.info(f"剩余交易: {remaining_trades}")
        
        # 检查数据库中的状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            trade_383_db = session.query(TradeHistory).filter(TradeHistory.id == db_id_383).first()
            trade_384_db = session.query(TradeHistory).filter(TradeHistory.id == db_id_384).first()
            
            logger.info(f"数据库中交易383状态: {trade_383_db.status if trade_383_db else 'NOT_FOUND'}")
            logger.info(f"数据库中交易384状态: {trade_384_db.status if trade_384_db else 'NOT_FOUND'}")
            
            if trade_383_db and trade_383_db.status != "PENDING":
                logger.info(f"交易383平仓价格: {trade_383_db.exit_price}")
                logger.info(f"交易383平仓时间: {trade_383_db.exit_timestamp}")
            
            if trade_384_db and trade_384_db.status != "PENDING":
                logger.info(f"交易384平仓价格: {trade_384_db.exit_price}")
                logger.info(f"交易384平仓时间: {trade_384_db.exit_timestamp}")
        
        # 7. 分析可能的问题
        logger.info("7. 分析可能的问题...")
        
        if db_id_383 not in remaining_trades and db_id_384 in remaining_trades:
            logger.info("✅ 正确：交易383被平仓，交易384仍在队列中")
        elif db_id_383 in remaining_trades and db_id_384 not in remaining_trades:
            logger.warning("❌ 错误：交易384被错误平仓，交易383仍在队列中")
            logger.warning("这可能是ID匹配错误的问题！")
        elif db_id_383 not in remaining_trades and db_id_384 not in remaining_trades:
            logger.warning("❌ 异常：两个交易都被平仓了")
        else:
            logger.info("两个交易都还在队列中（可能是模拟时间问题）")
        
        # 8. 测试settlement_checker的行为
        logger.info("8. 测试settlement_checker的行为...")
        
        # 检查settlement_checker是否会处理这些交易
        pending_trades = db.get_pending_trades()
        logger.info(f"settlement_checker看到的PENDING交易: {len(pending_trades)}")
        
        for trade in pending_trades:
            if trade['id'] in [db_id_383, db_id_384]:
                signal_time = datetime.fromisoformat(trade['signal_timestamp'])
                time_since_signal = (signal_time_384 - signal_time).total_seconds() / 60
                logger.info(f"  交易{trade['id']}: 信号后经过 {time_since_signal:.1f} 分钟")
                
                if time_since_signal >= 10:
                    logger.warning(f"  交易{trade['id']} 可能被settlement_checker处理（超过10分钟）")
        
        # 清理测试数据
        logger.info("9. 清理测试数据...")
        simple_exit_manager._pending_exits.clear()
        
        with db.get_session() as session:
            session.query(TradeHistory).filter(
                TradeHistory.id.in_([db_id_383, db_id_384])
            ).delete()
            session.commit()
        
        logger.info("✅ 精确测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_concurrent_processing():
    """测试并发处理可能导致的ID混淆"""
    logger.info("=== 测试并发处理ID混淆 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        
        # 创建多个接近时间的交易
        base_time = datetime.utcnow()
        
        trade_ids = []
        for i in range(3):
            signal_time = base_time + timedelta(seconds=i*2)
            
            test_signal = {
                "signal_timestamp": signal_time.isoformat(),
                "symbol": "BTCUSDT",
                "direction": "LONG",
                "entry_price": 120000.0 + i * 100,
                "confidence_score": 0.8,
                "market_state": "TRENDING_UP",
                "trigger_pattern": "test_pattern",
                "confirmed_indicators": ["rsi", "macd"],
                "suggested_bet": 1.0 + i,
                "decision_details": {"test": f"concurrent_{i}"}
            }
            
            db_id = db.save_trade_signal(test_signal)
            trade_ids.append(db_id)
            
            # 添加到SimpleExitManager
            trade_data = {
                "id": db_id,
                "entry_price": test_signal["entry_price"],
                "direction": "LONG",
                "symbol": "BTCUSDT",
                "suggested_bet": test_signal["suggested_bet"],
                "signal_timestamp": signal_time.isoformat()
            }
            
            simple_exit_manager.add_position(db_id, trade_data)
        
        logger.info(f"创建了 {len(trade_ids)} 个测试交易: {trade_ids}")
        
        # 设置第一个交易为过期状态
        past_time = datetime.utcnow() - timedelta(seconds=1)
        simple_exit_manager._pending_exits[trade_ids[0]]["planned_exit_time"] = past_time
        
        # 并发执行多个检查
        logger.info("并发执行平仓检查...")
        
        tasks = []
        for i in range(3):
            tasks.append(simple_exit_manager._check_exits())
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 检查结果
        remaining_trades = list(simple_exit_manager._pending_exits.keys())
        logger.info(f"剩余交易: {remaining_trades}")
        
        # 分析哪些交易被平仓了
        closed_trades = [tid for tid in trade_ids if tid not in remaining_trades]
        logger.info(f"被平仓的交易: {closed_trades}")
        
        if len(closed_trades) == 1 and closed_trades[0] == trade_ids[0]:
            logger.info("✅ 并发处理正确：只有过期的交易被平仓")
        else:
            logger.warning(f"❌ 并发处理异常：期望只平仓 {trade_ids[0]}，实际平仓 {closed_trades}")
        
        # 清理
        simple_exit_manager._pending_exits.clear()
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            session.query(TradeHistory).filter(
                TradeHistory.id.in_(trade_ids)
            ).delete()
            session.commit()
        
        logger.info("✅ 并发测试完成")
        
    except Exception as e:
        logger.error(f"❌ 并发测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_precise_id_matching_issue())
    print("\n" + "="*50 + "\n")
    asyncio.run(test_concurrent_processing())
