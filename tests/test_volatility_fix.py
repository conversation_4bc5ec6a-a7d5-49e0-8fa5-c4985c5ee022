#!/usr/bin/env python3
"""
波动率修复验证测试

测试修复后的波动率计算和风险管理逻辑是否能正常工作
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.config_manager import config
from quant.risk_manager import RiskManager


def test_volatility_calculation():
    """测试不同波动率值的风险计算结果."""
    print("🧪 测试波动率计算和风险管理")
    print("=" * 50)
    
    # 创建风险管理器实例
    risk_manager = RiskManager()
    
    # 测试不同波动率情况下的position size计算
    test_cases = [
        {
            "name": "极低波动率 (修复前会阻止)",
            "market_data": {"volatility": 0.0001, "current_price": 118000},
            "signal_data": {"confidence_score": 0.7, "suggested_bet": 50.0, "direction": "LONG"},
        },
        {
            "name": "低波动率",
            "market_data": {"volatility": 0.02, "current_price": 118000},
            "signal_data": {"confidence_score": 0.7, "suggested_bet": 50.0, "direction": "LONG"},
        },
        {
            "name": "正常波动率",
            "market_data": {"volatility": 0.08, "current_price": 118000},
            "signal_data": {"confidence_score": 0.7, "suggested_bet": 50.0, "direction": "LONG"},
        },
        {
            "name": "高波动率",
            "market_data": {"volatility": 0.25, "current_price": 118000},
            "signal_data": {"confidence_score": 0.7, "suggested_bet": 50.0, "direction": "LONG"},
        },
        {
            "name": "低置信度 + 低波动率 (双重限制)",
            "market_data": {"volatility": 0.02, "current_price": 118000},
            "signal_data": {"confidence_score": 0.4, "suggested_bet": 50.0, "direction": "LONG"},
        },
    ]
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}")
        print("-" * 30)
        
        result = risk_manager.calculate_position_size(
            test_case["signal_data"], 
            test_case["market_data"]
        )
        
        print(f"输入波动率: {test_case['market_data']['volatility']:.4f}")
        print(f"输入置信度: {test_case['signal_data']['confidence_score']:.2f}")
        print(f"建议金额: ${test_case['signal_data']['suggested_bet']}")
        print(f"结果金额: ${result.position_size_usdt:.2f}")
        print(f"风险等级: {result.risk_level.value}")
        print(f"风险因子: {result.risk_factor:.3f}")
        print(f"备注: {'; '.join(result.notes)}")
        
        # 判断是否可执行
        executable = result.position_size_usdt > 0
        status = "✅ 可执行" if executable else "❌ 被阻止"
        print(f"执行状态: {status}")


def test_config_loading():
    """测试配置加载是否正确."""
    print("\n🔧 测试配置加载")
    print("=" * 50)
    
    # 检查关键配置值
    at_cfg = config.get("AUTO_TRADER", {})
    rm_cfg = config.get("RISK_MANAGEMENT", {})
    rf_cfg = config.get("RISK_FILTERS", {})
    
    print(f"AUTO_TRADER.min_order_usdt: {at_cfg.get('min_order_usdt')} (应该是1.0)")
    print(f"RISK_MANAGEMENT.volatility_threshold: {rm_cfg.get('volatility_threshold')} (应该是0.15)")
    print(f"RISK_MANAGEMENT.min_position_size_usdt: {rm_cfg.get('min_position_size_usdt')} (应该是1.0)")
    print(f"RISK_FILTERS.low_vol_threshold: {rf_cfg.get('low_vol_threshold')} (应该是0.01)")
    
    # 验证配置是否合理
    issues = []
    
    if at_cfg.get('min_order_usdt', 100) > 5:
        issues.append("min_order_usdt仍然过高")
    
    if rf_cfg.get('low_vol_threshold', 0.05) > 0.02:
        issues.append("low_vol_threshold仍然过高")
        
    if rm_cfg.get('volatility_threshold', 0.3) > 0.2:
        issues.append("volatility_threshold仍然过高")
    
    if issues:
        print("\n⚠️ 配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("\n✅ 配置看起来正确")
        return True


def simulate_real_scenario():
    """模拟真实场景下的交易计算."""
    print("\n🎯 模拟真实交易场景")
    print("=" * 50)
    
    # 基于最近实际信号数据的模拟
    risk_manager = RiskManager()
    
    # 模拟最近的信号数据 (置信度0.68, 但被阻止的情况)
    signal_data = {
        "confidence_score": 0.68,
        "suggested_bet": 50.0,  # 原始建议金额
        "direction": "LONG",
        "confidence_breakdown": {
            "trend_score": 0.67,
            "momentum_score": 0.53,
            "volatility_score": 0.7,
            "volume_score": 0.45,
            "market_regime_score": 0.8
        }
    }
    
    # 使用修复后的波动率值 (最小0.05)
    market_data = {
        "volatility": 0.05,  # 修复后的最小值
        "current_price": 118000
    }
    
    print("📝 输入数据:")
    print(f"  置信度: {signal_data['confidence_score']:.3f}")
    print(f"  原始建议金额: ${signal_data['suggested_bet']}")
    print(f"  波动率: {market_data['volatility']:.3f}")
    
    result = risk_manager.calculate_position_size(signal_data, market_data)
    
    print(f"\n📊 计算结果:")
    print(f"  最终金额: ${result.position_size_usdt:.2f}")
    print(f"  风险因子: {result.risk_factor:.3f}")
    print(f"  风险等级: {result.risk_level.value}")
    print(f"  处理备注: {'; '.join(result.notes)}")
    
    # 检查是否满足AUTO_TRADER要求
    min_order = config.get("AUTO_TRADER", {}).get("min_order_usdt", 100)
    can_execute = result.position_size_usdt >= min_order
    
    print(f"\n🎯 执行检查:")
    print(f"  最小订单要求: ${min_order}")
    print(f"  是否可执行: {'✅ 是' if can_execute else '❌ 否'}")
    
    if can_execute:
        print(f"  🎉 修复成功！信号现在可以正常执行了")
        return True
    else:
        print(f"  ⚠️ 仍需进一步调整配置")
        return False


def main():
    """主测试函数."""
    print("🔬 波动率修复验证测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    tests = [
        ("配置加载测试", test_config_loading),
        ("波动率计算测试", lambda: test_volatility_calculation() or True),
        ("真实场景模拟", simulate_real_scenario),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print(f"\n📊 测试总结: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！波动率问题已修复")
        print("\n💡 建议:")
        print("  1. 重启交易系统以应用配置更改")
        print("  2. 监控接下来几个交易信号的执行情况") 
        print("  3. 如果仍有问题，可能需要进一步调整阈值")
    else:
        print("⚠️ 部分测试失败，需要进一步调整")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)