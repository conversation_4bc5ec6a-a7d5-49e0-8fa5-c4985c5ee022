#!/usr/bin/env python3
"""
测试多个平仓管理器的集成和优先级
验证：
1. 哪个管理器实际执行了平仓
2. SimpleExitManager的保护机制是否被其他管理器绕过
3. 各管理器的执行顺序和优先级
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.strategies.position_exit_manager import position_exit_manager
from quant.settlement_checker import settlement_checker
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_exit_manager_integration():
    """测试多个平仓管理器的集成"""
    logger.info("=== 测试多个平仓管理器的集成 ===")
    
    try:
        # 1. 检查各管理器的状态
        logger.info("1. 检查各管理器状态...")
        
        # SimpleExitManager状态
        simple_status = simple_exit_manager.get_status()
        logger.info(f"SimpleExitManager: {simple_status}")
        
        # PositionExitManager状态
        position_status = position_exit_manager.get_status()
        logger.info(f"PositionExitManager: {position_status}")
        
        # 2. 创建测试交易
        logger.info("2. 创建测试交易...")
        current_time = datetime.utcnow()
        
        test_trade_data = {
            "id": 8888,
            "entry_price": 120000.0,  # 使用真实价格，不是测试价格
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 10.0,
            "signal_timestamp": current_time
        }
        
        # 3. 添加到SimpleExitManager
        logger.info("3. 添加到SimpleExitManager...")
        simple_exit_manager.add_position(8888, test_trade_data)
        
        # 检查是否成功添加
        if 8888 in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[8888]
            logger.info("✅ 成功添加到SimpleExitManager")
            logger.info(f"  计划平仓时间: {exit_info['planned_exit_time']}")
            logger.info(f"  最小平仓时间: {exit_info['min_exit_time']}")
            
            # 计算预期持仓时间
            signal_time = exit_info['signal_time']
            planned_exit = exit_info['planned_exit_time']
            expected_duration = (planned_exit - signal_time).total_seconds() / 60
            logger.info(f"  预期持仓时间: {expected_duration:.2f} 分钟")
        else:
            logger.error("❌ 未能添加到SimpleExitManager")
        
        # 4. 检查是否会被其他管理器处理
        logger.info("4. 检查其他管理器...")
        
        # 模拟等待一小段时间
        logger.info("等待5秒观察...")
        await asyncio.sleep(5)
        
        # 5. 检查交易是否仍在队列中
        logger.info("5. 检查交易状态...")
        if 8888 in simple_exit_manager._pending_exits:
            logger.info("✅ 交易仍在SimpleExitManager队列中")
        else:
            logger.warning("❌ 交易已从SimpleExitManager队列中移除")
        
        # 6. 手动触发检查
        logger.info("6. 手动触发平仓检查...")
        await simple_exit_manager._check_exits()
        
        # 7. 最终状态检查
        logger.info("7. 最终状态检查...")
        if 8888 in simple_exit_manager._pending_exits:
            logger.info("✅ 交易未被立即平仓，保护机制生效")
        else:
            logger.warning("❌ 交易被立即平仓，保护机制失效")
        
        # 清理测试数据
        if 8888 in simple_exit_manager._pending_exits:
            del simple_exit_manager._pending_exits[8888]
            logger.info("清理测试交易")
        
        logger.info("✅ 集成测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_problematic_trades():
    """测试问题交易的处理流程"""
    logger.info("=== 测试问题交易处理流程 ===")
    
    try:
        # 模拟问题交易的情况
        current_time = datetime.utcnow()
        
        # 创建一个应该立即平仓的交易（如果没有保护）
        problem_trade = {
            "id": 7777,
            "entry_price": 50000.0,  # 使用测试价格
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 20.0,
            "signal_timestamp": current_time
        }
        
        logger.info(f"创建问题交易: {problem_trade}")
        
        # 添加到SimpleExitManager
        simple_exit_manager.add_position(7777, problem_trade)
        
        # 检查时间计算
        if 7777 in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[7777]
            
            signal_time = exit_info['signal_time']
            planned_exit = exit_info['planned_exit_time']
            min_exit = exit_info['min_exit_time']
            
            logger.info(f"信号时间: {signal_time.isoformat()}")
            logger.info(f"计划平仓时间: {planned_exit.isoformat()}")
            logger.info(f"最小平仓时间: {min_exit.isoformat()}")
            
            # 检查时间差
            time_to_min_exit = (min_exit - current_time).total_seconds() / 60
            time_to_planned_exit = (planned_exit - current_time).total_seconds() / 60
            
            logger.info(f"距离最小平仓时间: {time_to_min_exit:.2f} 分钟")
            logger.info(f"距离计划平仓时间: {time_to_planned_exit:.2f} 分钟")
            
            # 立即检查是否会被平仓
            logger.info("立即执行平仓检查...")
            await simple_exit_manager._check_exits()
            
            # 检查结果
            if 7777 in simple_exit_manager._pending_exits:
                logger.info("✅ 交易未被立即平仓，保护机制生效")
            else:
                logger.warning("❌ 交易被立即平仓，保护机制失效")
        
        # 清理
        if 7777 in simple_exit_manager._pending_exits:
            del simple_exit_manager._pending_exits[7777]
        
        logger.info("✅ 问题交易测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    async def main():
        await test_exit_manager_integration()
        print()
        await test_problematic_trades()
    
    asyncio.run(main())
