#!/bin/bash

# Trading System Deployment Script
# Automates deployment for development and production environments

set -e  # Exit on any error

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PYTHON_VERSION="3.11"
VENV_NAME="trading_system_venv"
SERVICE_NAME="trading-system"
SERVICE_USER="trading"
SERVICE_GROUP="trading"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root for production deployment"
    fi
}

# Detect environment
detect_environment() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        ENVIRONMENT="macos"
    elif [[ -f /etc/debian_version ]] || [[ -f /etc/lsb-release ]]; then
        ENVIRONMENT="linux"
    else
        error "Unsupported operating system: $OSTYPE"
    fi
    
    log "Detected environment: $ENVIRONMENT"
}

# Install system dependencies
install_system_deps() {
    log "Installing system dependencies..."
    
    if [[ "$ENVIRONMENT" == "linux" ]]; then
        apt-get update
        apt-get install -y \
            python3 \
            python3-pip \
            python3-venv \
            sqlite3 \
            supervisor \
            nginx \
            curl \
            wget \
            git
    elif [[ "$ENVIRONMENT" == "macos" ]]; then
        if ! command -v python3.13 &> /dev/null; then
            warn "Python 3.13 is not installed. Please install it manually from https://www.python.org/downloads/mac-osx/"
            exit 1
        fi
    fi
}


# Create virtual environment
create_venv() {
    log "Creating Python virtual environment..."
    
    cd "$PROJECT_ROOT"
    
    # Remove existing venv if it exists
    if [[ -d "$VENV_NAME" ]]; then
        log "Removing existing virtual environment..."
        rm -rf "$VENV_NAME"
    fi
    
    # Create new virtual environment
    python3 -m venv "$VENV_NAME"
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    pip install -r requirements.txt
    
    log "Virtual environment created successfully"
}

# Initialize database
init_database() {
    log "Initializing database..."
    
    cd "$PROJECT_ROOT"
    source "$VENV_NAME/bin/activate"
    
    # Run database initialization
    python scripts/init_database.py --force
    
    log "Database initialized successfully"
}

# Create systemd service (Linux only)
create_systemd_service() {
    if [[ "$ENVIRONMENT" != "linux" ]]; then
        return
    fi
    
    log "Creating systemd service..."
    
    # Create service user if it doesn't exist
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/false -d /opt/trading-system "$SERVICE_USER"
    fi
    
    # Create service directory
    mkdir -p /opt/trading-system
    cp -r "$PROJECT_ROOT"/* /opt/trading-system/
    chown -R "$SERVICE_USER:$SERVICE_GROUP" /opt/trading-system
    
    # Create systemd service file
    cat > /etc/systemd/system/"$SERVICE_NAME".service << EOF
[Unit]
Description=Trading Signal System
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_GROUP
WorkingDirectory=/opt/trading-system
Environment=PATH=/opt/trading-system/$VENV_NAME/bin
ExecStart=/opt/trading-system/$VENV_NAME/bin/python main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log "Systemd service created successfully"
}

# Create launchd service (macOS only)
create_launchd_service() {
    if [[ "$ENVIRONMENT" != "macos" ]]; then
        return
    fi
    
    log "Creating launchd service..."
    
    # Create launchd plist file
    cat > ~/Library/LaunchAgents/com.trading.system.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.trading.system</string>
    <key>ProgramArguments</key>
    <array>
        <string>$PROJECT_ROOT/$VENV_NAME/bin/python</string>
        <string>$PROJECT_ROOT/main.py</string>
    </array>
    <key>WorkingDirectory</key>
    <string>$PROJECT_ROOT</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$PROJECT_ROOT/logs/stdout.log</string>
    <key>StandardErrorPath</key>
    <string>$PROJECT_ROOT/logs/stderr.log</string>
</dict>
</plist>
EOF
    
    # Load the service
    launchctl load ~/Library/LaunchAgents/com.trading.system.plist
    
    log "Launchd service created successfully"
}

# Create configuration files
create_config_files() {
    log "Creating configuration files..."
    
    # Create environment-specific config
    if [[ "$ENVIRONMENT" == "production" ]]; then
        cp "$PROJECT_ROOT/config.json" "$PROJECT_ROOT/config.json.backup"
        # In production, you would want to set up proper config here
        warn "Please update config.json with production settings"
    fi
    
    # Create logs directory
    mkdir -p "$PROJECT_ROOT/logs"
    
    log "Configuration files created successfully"
}

# Start the service
start_service() {
    log "Starting trading system service..."
    
    if [[ "$ENVIRONMENT" == "linux" ]]; then
        systemctl start "$SERVICE_NAME"
        systemctl status "$SERVICE_NAME"
    elif [[ "$ENVIRONMENT" == "macos" ]]; then
        launchctl start com.trading.system
    fi
    
    log "Service started successfully"
}

# Main deployment function
main() {
    log "Starting Trading System deployment..."
    
    # Parse command line arguments
    ENVIRONMENT=${1:-development}
    
    log "Deploying to environment: $ENVIRONMENT"
    
    # Check if we need root privileges
    if [[ "$ENVIRONMENT" == "production" ]]; then
        check_root
    fi
    
    # Run deployment steps
    detect_environment
    install_system_deps
    create_venv
    init_database
    create_config_files
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        create_systemd_service
        create_launchd_service
        start_service
    fi
    
    log "Deployment completed successfully!"
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        echo ""
        echo "To start the application in development mode:"
        echo "  cd $PROJECT_ROOT"
        echo "  source $VENV_NAME/bin/activate"
        echo "  python main.py"
    fi
}

# Run main function with all arguments
main "$@"