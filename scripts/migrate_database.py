#!/usr/bin/env python3
"""
Database Migration Script

Handles database schema migrations and updates.
"""

import sys
import sqlite3
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseMigrator:
    """Handles database migrations."""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = Path(db_path)
        self.migrations_table = "schema_migrations"
    
    def run_migrations(self):
        """Run all pending migrations."""
        try:
            if not self.db_path.exists():
                logger.error("Database does not exist. Run init_database.py first.")
                return False
            
            # Connect to database
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Create migrations table if it doesn't exist
            self._create_migrations_table(cursor)
            
            # Get applied migrations
            applied_migrations = self._get_applied_migrations(cursor)
            
            # Define migrations
            migrations = [
                {
                    'version': '001',
                    'name': 'initial_schema',
                    'description': 'Create initial trade_history table',
                    'sql': self._get_initial_schema_sql()
                }
            ]
            
            # Run pending migrations
            for migration in migrations:
                if migration['version'] not in applied_migrations:
                    logger.info(f"Running migration {migration['version']}: {migration['name']}")
                    
                    # Execute migration
                    cursor.executescript(migration['sql'])
                    
                    # Record migration
                    cursor.execute(
                        f"INSERT INTO {self.migrations_table} (version, name, description, applied_at) "
                        f"VALUES (?, ?, ?, ?)",
                        (migration['version'], migration['name'], migration['description'], datetime.utcnow())
                    )
                    
                    logger.info(f"Migration {migration['version']} completed successfully")
            
            conn.commit()
            conn.close()
            
            logger.info("All migrations completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def _create_migrations_table(self, cursor):
        """Create migrations table if it doesn't exist."""
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS {self.migrations_table} (
                version TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                applied_at DATETIME NOT NULL
            )
        """)
    
    def _get_applied_migrations(self, cursor) -> set:
        """Get set of applied migration versions."""
        cursor.execute(f"SELECT version FROM {self.migrations_table}")
        return {row[0] for row in cursor.fetchall()}
    
    def _get_initial_schema_sql(self) -> str:
        """Get SQL for initial schema."""
        return """
            -- Create trade_history table
            CREATE TABLE IF NOT EXISTS trade_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_timestamp DATETIME NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                direction VARCHAR(10) NOT NULL,
                entry_price FLOAT NOT NULL,
                confidence_score FLOAT NOT NULL,
                market_state VARCHAR(50) NOT NULL,
                trigger_pattern VARCHAR(100) NOT NULL,
                confirmed_indicators TEXT NOT NULL,
                suggested_bet FLOAT NOT NULL,
                status VARCHAR(20) NOT NULL,
                exit_price FLOAT,
                exit_timestamp DATETIME,
                pnl FLOAT,
                decision_details TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Create indexes
            CREATE INDEX IF NOT EXISTS idx_trade_history_signal_timestamp ON trade_history(signal_timestamp);
            CREATE INDEX IF NOT EXISTS idx_trade_history_symbol ON trade_history(symbol);
            CREATE INDEX IF NOT EXISTS idx_trade_history_status ON trade_history(status);
            CREATE INDEX IF NOT EXISTS idx_status_timestamp ON trade_history(status, signal_timestamp);
        """


def main():
    """Main migration function."""
    migrator = DatabaseMigrator()
    
    try:
        success = migrator.run_migrations()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())