{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(tree:*)", "Bash(md-tree explode:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python:*)", "Bash(pip3 install:*)", "Bash(black:*)", "Bash(ruff check:*)", "<PERSON><PERSON>(timeout:*)", "Bash(DEBUG=true python3 main.py config.development.json)", "Bash(ls:*)", "Bash(DEBUG=true timeout:*)", "<PERSON><PERSON>(python:*)", "Bash(ENVIRONMENT=development python3:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ENVIRONMENT=development timeout 20 python3 main.py)", "Bash(rm:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(curl -I https://api.binance.com)", "Bash(SSL_CERT_FILE=\"\" python3:*)", "Bash(DEBUG=true python3:*)", "Bash(ENVIRONMENT=development DISABLE_SSL_VERIFY=true python3 main.py)", "Bash(ENVIRONMENT=development DISABLE_SSL_VERIFY=true timeout 30 python3 main.py)", "Bash(DISABLE_SSL_VERIFY=true python3:*)", "Bash(SSL_CERT_FILE=\"/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/certifi/cacert.pem\" python3:*)", "Bash(grep:*)", "<PERSON><PERSON>(echo:*)"], "deny": []}}