# 需求 (Requirements)

## 功能性需求 (Functional Requirements)
- **FR1-FR9**: (核心策略逻辑) 系统必须实现基于30分钟K线、多周期共振、K线形态、多指标过滤、市场状态识别、置信度评分和趋势强度过滤的完整信号生成逻辑。
- **FR10**: (动态资金) 系统信号需包含基于每日胜率动态计算出的建议投注额（5/20/50 USDT）。
- **FR11-FR12**: (钉钉通知) 系统必须能实时发送包含特定关键词的钉钉通知。
- **FR13-FR14**: (自动结算) 系统必须能自动监控10分钟合约到期，并判断、记录交易结果。
- **FR15**: (富文本历史) 所有交易历史必须持久化存储，并包含完整的决策依据（入场分钟数、市场状态、分数、形态、指标确认列表等）。
- **FR16-FR17**: (胜率与导出) 系统必须能统计每日胜率（每日零点重置），并能自动将前一日的富文本历史导出为`.jsonl`文件。

## 非功能性需求 (Non-Functional Requirements)
- **NFR1**: (性能) 信号生成全流程必须在1分钟内完成。系统不负责下单。
- **NFR2**: (可靠性) 核心逻辑Uptime > 99.9%。
- **NFR3**: (可维护性) 遵循约定目录结构；优先复用现有模块，必要时可新建。
- **NFR4**: (通知性能) 钉钉通知延迟 < 2秒。
- **NFR5**: (测试) MVP阶段不要求编写自动化测试。