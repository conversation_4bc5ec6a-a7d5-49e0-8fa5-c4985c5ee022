#!/usr/bin/env python3
"""
SimpleExitManager监控工具
用于实时监控平仓管理器的状态和健康度
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def monitor_exit_manager():
    """监控SimpleExitManager的状态"""
    logger.info("=== SimpleExitManager 实时监控 ===")
    
    try:
        # 1. 获取队列状态
        status = simple_exit_manager.get_queue_status()
        
        print("\n📊 队列状态概览:")
        print(f"  总交易数: {status['total_trades']}")
        print(f"  处理锁数: {status['processing_locks']}")
        print(f"  锁定交易: {status['locked_trades']}")
        
        print(f"\n📈 交易状态分布:")
        for state, count in status['trades_by_status'].items():
            print(f"  {state}: {count}")
        
        # 2. 检查超时交易
        if status['overdue_trades']:
            print(f"\n🚨 超时交易 ({len(status['overdue_trades'])}个):")
            for overdue in status['overdue_trades']:
                print(f"  交易{overdue['trade_id']}: 超时 {overdue['overdue_minutes']:.1f} 分钟")
        else:
            print("\n✅ 无超时交易")
        
        # 3. 检查即将平仓的交易
        if status['upcoming_exits']:
            print(f"\n⏰ 即将平仓 ({len(status['upcoming_exits'])}个):")
            for upcoming in status['upcoming_exits']:
                print(f"  交易{upcoming['trade_id']}: {upcoming['minutes_to_exit']:.1f} 分钟后平仓")
        else:
            print("\n📅 暂无即将平仓的交易")
        
        # 4. 验证队列完整性
        print(f"\n🔍 队列完整性检查:")
        integrity_ok = simple_exit_manager.validate_exit_queue_integrity()
        if integrity_ok:
            print("  ✅ 队列完整性正常")
        else:
            print("  ❌ 队列完整性异常")
        
        # 5. 数据库对比检查
        print(f"\n🗄️  数据库对比检查:")
        pending_trades = db.get_pending_trades()
        db_pending_count = len(pending_trades)
        queue_count = status['total_trades']
        
        print(f"  数据库PENDING交易: {db_pending_count}")
        print(f"  队列中的交易: {queue_count}")
        
        if db_pending_count == queue_count:
            print("  ✅ 数据库与队列同步")
        else:
            print("  ⚠️  数据库与队列不同步")
            
            # 详细分析差异
            db_ids = {trade['id'] for trade in pending_trades}
            queue_ids = set(simple_exit_manager._pending_exits.keys())
            
            missing_in_queue = db_ids - queue_ids
            extra_in_queue = queue_ids - db_ids
            
            if missing_in_queue:
                print(f"    数据库中有但队列中没有: {missing_in_queue}")
            
            if extra_in_queue:
                print(f"    队列中有但数据库中没有: {extra_in_queue}")
        
        # 6. 详细交易信息
        if status['total_trades'] > 0:
            print(f"\n📋 详细交易信息:")
            
            for trade_id, exit_info in simple_exit_manager._pending_exits.items():
                trade_data = exit_info.get("trade_data", {})
                signal_time = exit_info.get("signal_time")
                planned_exit = exit_info.get("planned_exit_time")
                
                if signal_time and planned_exit:
                    current_time = datetime.utcnow()
                    time_held = (current_time - signal_time).total_seconds() / 60
                    time_to_exit = (planned_exit - current_time).total_seconds() / 60
                    
                    print(f"  交易{trade_id}:")
                    print(f"    价格: ${trade_data.get('entry_price', 0):,.2f}")
                    print(f"    方向: {trade_data.get('direction', 'N/A')}")
                    print(f"    金额: ${trade_data.get('suggested_bet', 0):.2f}")
                    print(f"    已持仓: {time_held:.1f} 分钟")
                    print(f"    距离平仓: {time_to_exit:.1f} 分钟")
                    print(f"    状态: {exit_info.get('status', 'N/A')}")
        
        print(f"\n⏱️  监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")
        import traceback
        traceback.print_exc()


async def continuous_monitor(interval_seconds: int = 30):
    """连续监控模式"""
    logger.info(f"开始连续监控，间隔 {interval_seconds} 秒...")
    
    try:
        while True:
            await monitor_exit_manager()
            print("\n" + "="*80 + "\n")
            await asyncio.sleep(interval_seconds)
    except KeyboardInterrupt:
        logger.info("监控已停止")
    except Exception as e:
        logger.error(f"连续监控失败: {e}")


async def check_specific_trade(trade_id: int):
    """检查特定交易的状态"""
    logger.info(f"=== 检查交易 {trade_id} ===")
    
    try:
        # 1. 检查队列中的状态
        if trade_id in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[trade_id]
            print(f"✅ 交易{trade_id}在队列中:")
            print(f"  计划平仓时间: {exit_info.get('planned_exit_time')}")
            print(f"  状态: {exit_info.get('status')}")
            print(f"  交易数据: {exit_info.get('trade_data')}")
        else:
            print(f"❌ 交易{trade_id}不在队列中")
        
        # 2. 检查数据库中的状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
            
            if trade:
                print(f"✅ 交易{trade_id}在数据库中:")
                print(f"  状态: {trade.status}")
                print(f"  信号时间: {trade.signal_timestamp}")
                print(f"  开仓价格: ${trade.entry_price:,.2f}")
                print(f"  平仓价格: ${trade.exit_price or 0:,.2f}")
                print(f"  平仓时间: {trade.exit_timestamp}")
            else:
                print(f"❌ 交易{trade_id}不在数据库中")
        
        # 3. 检查是否被锁定
        if trade_id in simple_exit_manager._processing_locks:
            print(f"🔒 交易{trade_id}当前被锁定（正在处理中）")
        else:
            print(f"🔓 交易{trade_id}未被锁定")
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="SimpleExitManager监控工具")
    parser.add_argument("--continuous", "-c", action="store_true", help="连续监控模式")
    parser.add_argument("--interval", "-i", type=int, default=30, help="监控间隔（秒）")
    parser.add_argument("--trade-id", "-t", type=int, help="检查特定交易ID")
    
    args = parser.parse_args()
    
    if args.trade_id:
        asyncio.run(check_specific_trade(args.trade_id))
    elif args.continuous:
        asyncio.run(continuous_monitor(args.interval))
    else:
        asyncio.run(monitor_exit_manager())
