"""
Notification Manager <PERSON><PERSON><PERSON>les DingTalk notifications for trading signals.
"""

from datetime import datetime, timezone, timedelta
from typing import Any

import requests

from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class NotificationManager:
    """Manages DingTalk notifications for trading signals."""

    def __init__(self):
        self.webhook_url = config.get_dingtalk_config()
        self.retry_count = 3
        self.timeout = 2  # 2 second timeout for NFR4 compliance

    def send_signal_notification(self, signal_data: dict[str, Any]) -> bool:
        """Send trading signal notification to DingTalk."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_signal_message(signal_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send signal notification: {e}")
            return False

    def send_enhanced_signal_notification(self, signal_data: dict[str, Any]) -> bool:
        """Send enhanced trading signal notification with comprehensive details."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_enhanced_signal_message(signal_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send enhanced signal notification: {e}")
            return False

    def send_settlement_notification(self, settlement_data: dict[str, Any]) -> bool:
        """Send trade settlement notification to DingTalk."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_settlement_message(settlement_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send settlement notification: {e}")
            return False

    def send_enhanced_settlement_notification(
        self, settlement_data: dict[str, Any]
    ) -> bool:
        """Send enhanced trade settlement notification with comprehensive details."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_enhanced_settlement_message(settlement_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send enhanced settlement notification: {e}")
            return False

    def send_error_notification(self, error_data: dict[str, Any]) -> bool:
        """Send error notification to DingTalk."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_error_message(error_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
            return False

    def send_risk_alert(self, alert_data: dict[str, Any]) -> bool:
        """Send risk management alert to DingTalk."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_risk_alert_message(alert_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send risk alert notification: {e}")
            return False

    def send_system_health_alert(self, alert_data: dict[str, Any]) -> bool:
        """Send system health alert to DingTalk."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_system_health_alert_message(alert_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send system health alert notification: {e}")
            return False

    # ===== Auto trading additions =====
    def send_trade_execution(self, exec_data: dict[str, Any]) -> bool:
        """Send trade execution notification (order placed/closed)."""
        if not self.webhook_url:
            logger.warning("DingTalk webhook URL not configured")
            return False

        try:
            message = self._format_trade_execution_message(exec_data)
            return self._send_message(message)
        except Exception as e:
            logger.error(f"Failed to send trade execution notification: {e}")
            return False

    def _to_cst(self, ts: str) -> str:
        try:
            if 'T' in ts:
                dt = datetime.fromisoformat(ts.replace('Z', '+00:00'))
            else:
                dt = datetime.fromisoformat(ts)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            else:
                dt = dt.astimezone(timezone.utc)
            cst = dt.astimezone(timezone(timedelta(hours=8)))
            return cst.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            return ts

    def _format_signal_message(self, signal_data: dict[str, Any]) -> str:
        """Format trading signal message for DingTalk."""
        # Data validation and default values
        symbol = signal_data.get("symbol", "BTCUSDT")
        direction = signal_data.get("direction", "LONG")
        entry_price = signal_data.get("entry_price", 0.0)
        suggested_bet = signal_data.get("suggested_bet", 0)
        signal_timestamp = signal_data.get("signal_timestamp", "")
        confidence_score = signal_data.get("confidence_score", 0.0)
        trigger_pattern = signal_data.get("trigger_pattern", "UNKNOWN")
        market_state = signal_data.get("market_state", "UNKNOWN")
        confirmed_indicators = signal_data.get("confirmed_indicators", [])
        current_kline = signal_data.get("current_kline", 1)
        total_klines = signal_data.get("total_klines", 48)
        entry_minutes = signal_data.get("entry_minutes", 1)

        # Format direction emoji and text
        direction_emoji = "⬆️" if direction == "LONG" else "⬇️"
        direction_text = "做多信号 (BUY)" if direction == "LONG" else "做空信号 (SELL)"

        # Format confidence as percentage
        confidence_percentage = int(confidence_score * 100)

        # Format entry price
        entry_price_formatted = f"{entry_price:.2f}"

        # Format timestamp
        try:
            from datetime import datetime
            if "T" in signal_timestamp:
                dt = datetime.fromisoformat(signal_timestamp.replace("Z", "+00:00"))
                formatted_timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                formatted_timestamp = signal_timestamp
        except:
            formatted_timestamp = signal_timestamp

        # Format confirmed indicators as markdown list
        indicators_list = ""
        for indicator in confirmed_indicators:
            indicators_list += f"* {indicator}\n"
        
        # Calculate K-line information based on signal timestamp
        current_kline = self._calculate_current_kline(signal_timestamp)
        total_klines = 48  # Fixed total for 30-minute K-lines in a day
        
        # Get consistent signal identifier
        signal_identifier = f"{current_kline}/{total_klines}"

        analysis_tag = "（分析信号）" if signal_data.get("analysis_only", False) else ""
        signal_type_line = "* **类型**: 分析信号（不交易）" if signal_data.get("analysis_only", False) else "* **类型**: 可交易信号"

        message = f"""#### Mitchquant 交易信号【小火箭出发】{analysis_tag} ({signal_identifier})

-----

## {direction_emoji} **{direction_text}**

* **品种**: {symbol}
* **入场参考价**: **{entry_price_formatted}**
* **建议金额**: {suggested_bet} USDT
* **信号时间(北京时间)**: {self._to_cst(signal_timestamp)}
* **K线序号**: {signal_identifier}
* **入场分钟**: {entry_minutes} / 15
{signal_type_line}

-----

#### **判断依据**

* **置信度**: {confidence_percentage} / 100
* **触发模式**: {trigger_pattern}
* **市场状态**: {market_state}
* **确认指标**:
{indicators_list}-----

> ###### *风险提示：交易有风险，投资需谨慎。本信号仅为程序化分析结果，不构成任何投资建议。*
"""
        return message

    def _format_enhanced_signal_message(self, signal_data: dict[str, Any]) -> str:
        """Format enhanced trading signal message with comprehensive details."""
        # Data validation and default values
        symbol = signal_data.get("symbol", "BTCUSDT")
        direction = signal_data.get("direction", "LONG")
        entry_price = signal_data.get("entry_price", 0.0)
        suggested_bet = signal_data.get("suggested_bet", 0)
        signal_timestamp = signal_data.get("signal_timestamp", "")
        confidence_score = signal_data.get("confidence_score", 0.0)
        trigger_pattern = signal_data.get("trigger_pattern", "UNKNOWN")
        market_state = signal_data.get("market_state", "UNKNOWN")
        confirmed_indicators = signal_data.get("confirmed_indicators", [])
        current_kline = signal_data.get("current_kline", 1)
        total_klines = signal_data.get("total_klines", 48)
        entry_minutes = signal_data.get("entry_minutes", 1)

        # Get enhanced data
        market_regime_score = signal_data.get("market_regime_score", 0.0)
        trend_strength_score = signal_data.get("trend_strength_score", 0.0)
        confidence_breakdown = signal_data.get("confidence_breakdown", {})
        
        # Fix price data consistency - use entry_price as the authoritative source
        current_price = signal_data.get("current_price", entry_price)
        price_change = signal_data.get("price_change", 0)
        price_change_pct = signal_data.get("price_change_pct", 0)
        market_cap = signal_data.get("market_cap", "N/A")
        volume_24h = signal_data.get("volume_24h", "N/A")
        
        # Calculate K-line information based on signal timestamp
        current_kline = self._calculate_current_kline(signal_timestamp)
        total_klines = 48  # Fixed total for 30-minute K-lines in a day
        
        # Get consistent signal identifier
        signal_identifier = f"{current_kline}/{total_klines}"

        # Log price data for debugging
        logger.info(f"Signal price data - Entry: {entry_price}, Current: {current_price}, Change: {price_change_pct:+.2f}%")
        logger.info(f"Signal timestamp: {signal_timestamp}")
        logger.info(f"K-line info - Current: {current_kline}, Total: {total_klines}")
        logger.info(f"Signal identifier: {signal_identifier}")

        # Format direction emoji and text
        direction_emoji = "⬆️" if direction == "LONG" else "⬇️"
        direction_text = "做多信号 (BUY)" if direction == "LONG" else "做空信号 (SELL)"

        # Format confidence as percentage
        confidence_percentage = int(confidence_score * 100)

        # Format entry price
        entry_price_formatted = f"{entry_price:.2f}"

        # Format timestamp
        try:
            from datetime import datetime
            if "T" in signal_timestamp:
                dt = datetime.fromisoformat(signal_timestamp.replace("Z", "+00:00"))
                formatted_timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                formatted_timestamp = signal_timestamp
        except:
            formatted_timestamp = signal_timestamp

        # Format confirmed indicators as markdown list
        indicators_list = ""
        for indicator in confirmed_indicators:
            indicators_list += f"* {indicator}\n"

        # Format confidence breakdown
        confidence_details = ""
        if confidence_breakdown:
            confidence_details = f"""
* **技术指标权重**: {confidence_breakdown.get('technical_weight', 0):.2f}
* **市场状态权重**: {confidence_breakdown.get('market_weight', 0):.2f}
* **趋势强度权重**: {confidence_breakdown.get('trend_weight', 0):.2f}
"""

        analysis_tag = "（分析信号）" if signal_data.get("analysis_only", False) else ""
        signal_type_line = "* **类型**: 分析信号（不交易）" if signal_data.get("analysis_only", False) else "* **类型**: 可交易信号"

        message = f"""#### Mitchquant 交易信号【小火箭出发】{analysis_tag} ({current_kline}/{total_klines})

-----

## {direction_emoji} **{direction_text}**

* **品种**: {symbol}
* **入场参考价**: **{entry_price_formatted}**
* **建议金额**: {suggested_bet} USDT
* **信号时间(北京时间)**: {self._to_cst(signal_timestamp)}
* **K线序号**: {signal_identifier}
* **入场分钟**: {entry_minutes} / 15
{signal_type_line}

-----

#### **判断依据**

* **置信度**: {confidence_percentage} / 100
* **触发模式**: {trigger_pattern}
* **市场状态**: {market_state}
* **市场制度评分**: {market_regime_score:.2f}
* **趋势强度评分**: {trend_strength_score:.2f}
* **确认指标**:
{indicators_list}{confidence_details}-----

#### **市场数据**

* **实时价格**: ${current_price:,.2f}
* **价格变动**: {price_change_pct:+.2f}%
* **24小时成交量**: {volume_24h}
* **市值**: {market_cap}

-----

> ###### *风险提示：交易有风险，投资需谨慎。本信号仅为程序化分析结果，不构成任何投资建议。*
"""
        return message

    def _format_settlement_message(self, settlement_data: dict[str, Any]) -> str:
        """Format trade settlement message for DingTalk."""
        result_emoji = "✅" if settlement_data["status"] == "WIN" else "❌"
        pnl_emoji = "💰" if settlement_data.get("pnl", 0) > 0 else "💸"

        signal_kline = settlement_data.get("signal_kline", settlement_data.get("original_signal_id", "Unknown/48"))
        settlement_kline = settlement_data.get("settlement_kline", signal_kline)
        message = f"""
### {result_emoji} 交易结算通知（时间为北京时间）

**交易结果**: {settlement_data['status']}
**出场价格**: ${settlement_data['exit_price']:,.2f}
**盈亏**: {pnl_emoji} ${settlement_data.get('pnl', 0):,.2f}
**结算时间(北京时间)**: {self._to_cst(settlement_data['exit_timestamp'])}
**K线标识**: 信号 {signal_kline} / 结算 {settlement_kline}

[交易系统] 交易已自动结算
"""
        return message

    def _format_enhanced_settlement_message(
        self, settlement_data: dict[str, Any]
    ) -> str:
        """Format enhanced trade settlement message with comprehensive details and signal traceability."""
        result_emoji = "✅" if settlement_data["status"] == "WIN" else "❌"
        pnl_emoji = "💰" if settlement_data.get("pnl", 0) > 0 else "💸"

        # Extract core settlement data
        trade_id = settlement_data.get("trade_id", "Unknown")
        original_signal_id = settlement_data.get("original_signal_id", trade_id)
        entry_price = settlement_data.get("entry_price", 0.0)
        direction = settlement_data.get("direction", "Unknown")
        pnl_percentage = settlement_data.get("pnl_percentage", 0.0)

        # Extract signal traceability information
        signal_confidence = settlement_data.get("signal_confidence", 0.0)
        signal_market_state = settlement_data.get("signal_market_state", "Unknown")
        signal_trigger_pattern = settlement_data.get("signal_trigger_pattern", "Unknown")
        signal_confirmed_indicators = settlement_data.get("signal_confirmed_indicators", [])
        entry_timestamp = settlement_data.get("entry_timestamp", "Unknown")

        # Extract time calculation details
        calculated_settlement_time = settlement_data.get("calculated_settlement_time", "Unknown")
        actual_settlement_time = settlement_data.get("actual_settlement_time", "Unknown")
        time_difference_minutes = settlement_data.get("time_difference_minutes", 0)

        # Format signal indicators
        indicators_text = ""
        if signal_confirmed_indicators:
            if isinstance(signal_confirmed_indicators, str):
                try:
                    import json
                    indicators = json.loads(signal_confirmed_indicators)
                    indicators_text = ", ".join(indicators[:3])  # Show first 3 indicators
                    if len(indicators) > 3:
                        indicators_text += f" (+{len(indicators)-3}更多)"
                except:
                    indicators_text = signal_confirmed_indicators
            elif isinstance(signal_confirmed_indicators, list):
                indicators_text = ", ".join(signal_confirmed_indicators[:3])
                if len(signal_confirmed_indicators) > 3:
                    indicators_text += f" (+{len(signal_confirmed_indicators)-3}更多)"

        # Calculate duration if available
        duration_info = ""
        entry_time = settlement_data.get("entry_timestamp")
        exit_time = settlement_data.get("exit_timestamp")
        if entry_time and exit_time:
            try:
                from datetime import datetime
                entry_dt = datetime.fromisoformat(entry_time.replace("Z", "+00:00"))
                exit_dt = datetime.fromisoformat(exit_time.replace("Z", "+00:00"))
                duration = exit_dt - entry_dt
                duration_minutes = duration.total_seconds() / 60
                duration_info = f"**持仓时长**: {duration_minutes:.1f} 分钟\n"
            except Exception:
                pass

        # Time accuracy indicator
        time_accuracy = "✅" if abs(time_difference_minutes) <= 1 else "⚠️"
        time_accuracy_text = ""
        if time_difference_minutes != 0:
            time_accuracy_text = f" ({time_accuracy} 偏差 {time_difference_minutes:.1f} 分钟)"

        # K线标识显示，确保与信号一致
        signal_kline = settlement_data.get("signal_kline", original_signal_id)
        settlement_kline = settlement_data.get("settlement_kline", original_signal_id)

        message = f"""
### {result_emoji} 交易结算通知 - 信号追溯（时间为北京时间）

**📋 信号标识**: 第 {signal_kline} 号K线信号（结算K线: {settlement_kline}）
**💰 交易结果**: {settlement_data['status']}
**📊 交易方向**: {direction}
**💵 入场价格**: ${entry_price:,.2f}
**💸 出场价格**: ${settlement_data['exit_price']:,.2f}
**📈 盈亏**: {pnl_emoji} ${settlement_data.get('pnl', 0):,.2f}
**📊 盈亏比例**: {pnl_percentage:.2f}%

**🎯 原始信号信息**:
- **信号置信度**: {signal_confidence:.2f}
- **市场状态**: {signal_market_state}
- **触发模式**: {signal_trigger_pattern}
- **确认指标**: {indicators_text if indicators_text else "无"}
- **信号生成时间(北京时间)**: {self._to_cst(entry_timestamp)}

**⏰ 时间计算**:
- **理论结算时间(北京时间)**: {self._to_cst(calculated_settlement_time)}
- **实际结算时间(北京时间)**: {self._to_cst(actual_settlement_time)}{time_accuracy_text}
{duration_info}**🔍 数据一致性**: {"✅ 已验证" if settlement_data.get("data_consistency_verified", False) else "❌ 未验证"}
**📋 一致性检查**: {"✅ 通过" if settlement_data.get("consistency_check_passed", False) else "❌ 失败"}
**⚡ 价格来源**: {settlement_data.get("price_source", "unknown")}

[交易系统] 交易已自动结算完成
"""
        return message

    def _format_error_message(self, error_data: dict[str, Any]) -> str:
        """Format error message for DingTalk."""
        message = f"""
### ⚠️ 系统错误通知

**错误类型**: {error_data.get('error_type', 'Unknown')}
**错误信息**: {error_data.get('error_message', 'Unknown error')}
**发生时间**: {datetime.now().isoformat()}

请及时检查系统状态！
"""
        return message

    def _format_risk_alert_message(self, alert_data: dict[str, Any]) -> str:
        """Format risk alert message for DingTalk."""
        alert_type = alert_data.get("alert_type", "UNKNOWN")
        reason = alert_data.get("reason", "Unknown risk alert")

        if alert_type == "TRADING_SUSPENDED":
            emoji = "🚨"
            title = "交易暂停通知"
            risk_report = alert_data.get("risk_report", {})
            account_state = risk_report.get("account_state", {})

            message = f"""
### {emoji} {title}

**状态**: 交易已暂停
**暂停原因**: {reason}

**账户状态**:
- 余额: ${account_state.get('balance_usdt', 0):,.2f}
- 今日盈亏: ${account_state.get('daily_pnl', 0):,.2f}
- 连续亏损: {account_state.get('consecutive_losses', 0)} 次

**风险限制**:
- 每日亏损限制: {risk_report.get('risk_limits', {}).get('max_daily_loss_percent', 0) * 100:.1f}%
- 最大连续亏损: {risk_report.get('risk_limits', {}).get('max_consecutive_losses', 0)} 次

**通知时间**: {datetime.now().isoformat()}

请检查风险参数并手动恢复交易！
"""
        elif alert_type == "RISK_WARNING":
            emoji = "⚠️"
            title = "风险警告通知"
            risk_report = alert_data.get("risk_report", {})
            account_state = risk_report.get("account_state", {})

            message = f"""
### {emoji} {title}

**状态**: 风险警告
**警告原因**: {reason}

**账户状态**:
- 余额: ${account_state.get('balance_usdt', 0):,.2f}
- 今日盈亏: ${account_state.get('daily_pnl', 0):,.2f}
- 连续亏损: {account_state.get('consecutive_losses', 0)} 次

**风险指标**:
- 风险状态: {risk_report.get('risk_status', 'Unknown')}
- 总交易数: {risk_report.get('risk_metrics', {}).get('total_trades', 0)}
- 最大回撤: ${risk_report.get('risk_metrics', {}).get('max_drawdown', 0):,.2f}

**通知时间**: {datetime.now().isoformat()}

请密切监控风险参数！
"""
        else:
            emoji = "🔔"
            message = f"""
### {emoji} 风险通知

**通知类型**: {alert_type}
**通知原因**: {reason}
**通知时间**: {datetime.now().isoformat()}
"""

        return message

    def _format_system_health_alert_message(self, alert_data: dict[str, Any]) -> str:
        """Format system health alert message for DingTalk."""
        alert_type = alert_data.get("alert_type", "UNKNOWN")
        message = alert_data.get("message", "System health alert")
        health_report = alert_data.get("health_report", {})

        if alert_type == "SYSTEM_CRITICAL":
            emoji = "🚨"
            title = "系统严重警告"

            # Extract key metrics
            system_metrics = health_report.get("system_metrics", {})
            summary = health_report.get("summary", {})

            message_content = f"""
### {emoji} {title}

**状态**: 系统健康严重警告
**警告原因**: {message}

**系统指标**:
- CPU使用率: {system_metrics.get('cpu_percent', 0):.1f}%
- 内存使用率: {system_metrics.get('memory_percent', 0):.1f}%
- 磁盘使用率: {system_metrics.get('disk_usage_percent', 0):.1f}%
- 运行时间: {summary.get('uptime_hours', 0):.1f} 小时

**警告统计**:
- 总警告数: {summary.get('total_alerts', 0)}
- 严重警告: {summary.get('critical_alerts', 0)}
- 一般警告: {summary.get('warning_alerts', 0)}

**需要立即处理！**

**通知时间**: {datetime.now().isoformat()}
"""
        elif alert_type == "SYSTEM_WARNING":
            emoji = "⚠️"
            title = "系统警告"

            # Extract key metrics
            system_metrics = health_report.get("system_metrics", {})
            summary = health_report.get("summary", {})

            message_content = f"""
### {emoji} {title}

**状态**: 系统健康警告
**警告原因**: {message}

**系统指标**:
- CPU使用率: {system_metrics.get('cpu_percent', 0):.1f}%
- 内存使用率: {system_metrics.get('memory_percent', 0):.1f}%
- 磁盘使用率: {system_metrics.get('disk_usage_percent', 0):.1f}%
- 运行时间: {summary.get('uptime_hours', 0):.1f} 小时

**警告统计**:
- 总警告数: {summary.get('total_alerts', 0)}
- 严重警告: {summary.get('critical_alerts', 0)}
- 一般警告: {summary.get('warning_alerts', 0)}

**请密切监控系统状态！**

**通知时间**: {datetime.now().isoformat()}
"""
        else:
            emoji = "🔔"
            message_content = f"""
### {emoji} 系统健康通知

**通知类型**: {alert_type}
**通知原因**: {message}
**通知时间**: {datetime.now().isoformat()}
"""

        return message_content

    def _format_trade_execution_message(self, exec_data: dict[str, Any]) -> str:
        """Format trade execution message for DingTalk."""
        action = exec_data.get("action", "UNKNOWN")
        symbol = exec_data.get("symbol", "BTCUSDT")
        direction = exec_data.get("direction", "-")
        size = exec_data.get("size_usdt", 0)
        price = exec_data.get("price", 0.0)
        reason = exec_data.get("reason", "")
        trade_id = exec_data.get("trade_id", "-")
        ts = exec_data.get("timestamp", datetime.now().isoformat())
        extra = exec_data.get("extra", {})

        emoji = "🟢" if action == "OPEN" else ("🔴" if action == "CLOSE" else "🔔")
        title = "开仓执行" if action == "OPEN" else ("平仓执行" if action == "CLOSE" else "交易执行")

        notes = ""
        if extra:
            try:
                # Show a few key-value pairs
                kv = [f"{k}: {v}" for k, v in list(extra.items())[:6]]
                notes = "\n".join([f"- {x}" for x in kv])
            except Exception:
                notes = ""

        message = f"""
### {emoji} {title}

**交易ID**: {trade_id}
**品种**: {symbol}
**方向**: {direction}
**执行价格**: ${price:,.2f}
**名义金额**: {size} USDT
**原因**: {reason}
**时间(北京时间)**: {self._to_cst(ts)}
{('**附加信息**:\n' + notes) if notes else ''}
"""
        return message

    def _calculate_current_kline(self, signal_timestamp: str) -> int:
        """Calculate current K-line number based on signal timestamp for 30-minute intervals."""
        try:
            from datetime import datetime
            
            # Parse the timestamp
            if "T" in signal_timestamp:
                dt = datetime.fromisoformat(signal_timestamp.replace("Z", "+00:00"))
            else:
                # Try to parse as regular datetime string
                dt = datetime.strptime(signal_timestamp, "%Y-%m-%d %H:%M:%S")
            
            # Get the start of the day (00:00)
            start_of_day = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Calculate minutes since start of day
            minutes_since_start = (dt - start_of_day).total_seconds() / 60
            
            # Calculate K-line number (30-minute intervals)
            # Each K-line represents 30 minutes, so divide by 30
            kline_number = int(minutes_since_start / 30) + 1  # +1 because K-lines are 1-indexed
            
            # Ensure it's within valid range (1-48 for 30-minute K-lines)
            kline_number = max(1, min(kline_number, 48))
            
            logger.info(f"Calculated K-line number: {kline_number} for timestamp: {signal_timestamp}")
            return kline_number
            
        except Exception as e:
            logger.error(f"Error calculating K-line number: {e}")
            # Fallback to current time calculation
            try:
                now = datetime.now()
                start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
                minutes_since_start = (now - start_of_day).total_seconds() / 60
                kline_number = int(minutes_since_start / 30) + 1
                return max(1, min(kline_number, 48))
            except:
                return 1  # Ultimate fallback
    
    def get_signal_kline_identifier(self, signal_timestamp: str) -> str:
        """Get signal identifier in X/Y format for consistent tracing."""
        try:
            kline_number = self._calculate_current_kline(signal_timestamp)
            return f"{kline_number}/48"
        except Exception as e:
            logger.error(f"Error getting signal K-line identifier: {e}")
            return "Unknown/48"

    def _send_message(self, message: str) -> bool:
        """Send message to DingTalk webhook."""
        payload = {
            "msgtype": "markdown",
            "markdown": {"title": "交易系统通知", "text": message},
        }

        for attempt in range(self.retry_count):
            try:
                response = requests.post(
                    self.webhook_url, json=payload, timeout=self.timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info("DingTalk notification sent successfully")
                        return True
                    else:
                        logger.error(
                            f"DingTalk API error: {result.get('errmsg', 'Unknown error')}"
                        )
                else:
                    logger.error(f"DingTalk HTTP error: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logger.error(f"DingTalk request failed (attempt {attempt + 1}): {e}")
                if attempt < self.retry_count - 1:
                    import time

                    time.sleep(1)  # Wait before retry

        return False


# Global notification manager instance
notification_manager = NotificationManager()
