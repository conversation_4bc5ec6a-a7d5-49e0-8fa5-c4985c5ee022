"""
Quant Framework - A comprehensive quantitative trading framework

This framework provides:
- Strategy development and backtesting
- Technical indicators and signal generation
- Portfolio management and risk control
- Data collection and processing
- Performance monitoring and alerting
"""

# Trading System Core Components
from .binance_client import BinanceClient, binance_client
from .config_manager import Config<PERSON><PERSON><PERSON>, config
from .database_manager import <PERSON>Manager, TradeHistory, db
from .error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>, error_handler
from .exceptions import *

# from .analysis_engine import analysis_engine, AnalysisEngine  # Temporarily disabled due to pandas-ta compatibility
from .notification_manager import NotificationManager, notification_manager
from .settlement_checker import SettlementChecker, settlement_checker

# Strategy Components
# from .strategies.base.strategy_base import StrategyBase
# from .strategies.base.signal_base import SignalBase
# from .strategies.base.risk_manager import RiskManager
# from .strategies.portfolio.position_manager import PositionManager
# from .strategies.portfolio.risk_control import RiskControl
# from .strategies.portfolio.performance import PerformanceAnalyzer

# Technical Indicators
# from .strategies.indicators.ma import MovingAverage
# from .strategies.indicators.rsi import RSI
# from .strategies.indicators.macd import MACD
# from .strategies.indicators.bollinger import BollingerBands

# Signal Generators
# from .strategies.signals.trend_signals import TrendSignalGenerator
# from .strategies.signals.mean_reversion import MeanReversionSignalGenerator
# from .strategies.signals.arbitrage_signals import ArbitrageSignalGenerator

# Data Management
# from .data.collectors.kline_collector import KlineCollector
# from .data.collectors.ticker_collector import TickerCollector
# from .data.collectors.orderbook_collector import OrderbookCollector
# from .data.processors.cleaner import DataCleaner
# from .data.processors.normalizer import DataNormalizer
# from .data.processors.aggregator import DataAggregator
# from .data.storage.timeseries import TimeSeriesStorage
# from .data.storage.cache import DataCache

# Backtesting
# from .backtest.engine import BacktestEngine
# from .backtest.broker import BacktestBroker
# from .backtest.metrics import BacktestMetrics
# from .backtest.reports import BacktestReport

# Monitoring
# from .monitoring.health_check import HealthChecker
# from .monitoring.performance_monitor import PerformanceMonitor
# from .monitoring.alert_manager import AlertManager

# Version
__version__ = "1.0.0"
__author__ = "Quant Framework Team"

# Main classes for easy import
__all__ = [
    # Trading System Core
    "config",
    "ConfigManager",
    "db",
    "DatabaseManager",
    "TradeHistory",
    "binance_client",
    "BinanceClient",
    "notification_manager",
    "NotificationManager",
    "settlement_checker",
    "SettlementChecker",
    "error_handler",
    "ErrorHandler",
]

# Framework information
FRAMEWORK_INFO = {
    "name": "Quant Framework",
    "version": __version__,
    "description": "A comprehensive quantitative trading framework",
    "features": [
        "Multi-strategy support",
        "Technical analysis indicators",
        "Risk management",
        "Portfolio optimization",
        "Backtesting engine",
        "Real-time monitoring",
        "Alert management",
    ],
}


def get_framework_info():
    """Get framework information"""
    return FRAMEWORK_INFO


def create_strategy(config=None):
    """Create a new strategy instance"""
    # from .strategies.base.strategy_base import StrategyBase
    # return StrategyBase(config or {})
    raise NotImplementedError("Strategy components are temporarily disabled")


def create_backtest_engine(config=None):
    """Create a new backtest engine instance"""
    # from .backtest.engine import BacktestEngine
    # return BacktestEngine(config or {})
    raise NotImplementedError("Backtest components are temporarily disabled")


def create_monitoring_system(config=None):
    """Create a complete monitoring system"""
    # config = config or {}
    # return {
    #     'health_checker': HealthChecker(config.get('health', {})),
    #     'performance_monitor': PerformanceMonitor(config.get('performance', {})),
    #     'alert_manager': AlertManager(config.get('alerts', {}))
    # }
    raise NotImplementedError("Monitoring components are temporarily disabled")
