"""
Technical Analysis Indicators Module

Provides comprehensive technical analysis indicators for trading signal generation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class TrendDirection(Enum):
    """Trend direction enumeration."""
    STRONG_UP = "strong_up"
    UP = "up"
    NEUTRAL = "neutral"
    DOWN = "down"
    STRONG_DOWN = "strong_down"


class MarketRegime(Enum):
    """Market regime enumeration."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"


class TechnicalIndicators:
    """Technical analysis indicators calculator."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def sma(self, data: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average."""
        return data.rolling(window=period).mean()
    
    def ema(self, data: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average."""
        return data.ewm(span=period, adjust=False).mean()
    
    def rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index."""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """MACD (Moving Average Convergence Divergence)."""
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """Bollinger Bands."""
        middle = self.sma(data, period)
        std = data.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower,
            'bandwidth': (upper - lower) / middle * 100,
            'percent_b': (data - lower) / (upper - lower) * 100
        }
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """Stochastic Oscillator."""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            'k': k_percent,
            'd': d_percent
        }
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range."""
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Dict[str, pd.Series]:
        """Average Directional Index."""
        # Calculate True Range
        prev_close = close.shift(1)
        tr = pd.concat([
            high - low,
            abs(high - prev_close),
            abs(low - prev_close)
        ], axis=1).max(axis=1)
        
        # Calculate Directional Movement
        up_move = high - high.shift(1)
        down_move = low.shift(1) - low
        
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
        
        # Calculate Smoothed Values
        plus_dm_smooth = pd.Series(plus_dm).ewm(span=period, adjust=False).mean()
        minus_dm_smooth = pd.Series(minus_dm).ewm(span=period, adjust=False).mean()
        tr_smooth = tr.ewm(span=period, adjust=False).mean()
        
        # Calculate Directional Indicators
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)
        
        # Calculate ADX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.ewm(span=period, adjust=False).mean()
        
        return {
            'adx': adx,
            'plus_di': plus_di,
            'minus_di': minus_di
        }
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Williams %R."""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        wr = -100 * (highest_high - close) / (highest_high - lowest_low)
        return wr
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """Commodity Channel Index."""
        tp = (high + low + close) / 3  # Typical Price
        sma_tp = tp.rolling(window=period).mean()
        mad = tp.rolling(window=period).apply(lambda x: np.fabs(x - x.mean()).mean())
        cci = (tp - sma_tp) / (0.015 * mad)
        return cci
    
    def fibonacci_retracement(self, high_price: float, low_price: float) -> Dict[str, float]:
        """Calculate Fibonacci retracement levels."""
        diff = high_price - low_price
        levels = {
            '0%': high_price,
            '23.6%': high_price - 0.236 * diff,
            '38.2%': high_price - 0.382 * diff,
            '50%': high_price - 0.5 * diff,
            '61.8%': high_price - 0.618 * diff,
            '78.6%': high_price - 0.786 * diff,
            '100%': low_price
        }
        return levels
    
    def pivot_points(self, high: float, low: float, close: float) -> Dict[str, float]:
        """Calculate pivot points."""
        pivot = (high + low + close) / 3
        resistance1 = 2 * pivot - low
        support1 = 2 * pivot - high
        resistance2 = pivot + (high - low)
        support2 = pivot - (high - low)
        resistance3 = high + 2 * (pivot - low)
        support3 = low - 2 * (high - pivot)
        
        return {
            'pivot': pivot,
            'r1': resistance1,
            's1': support1,
            'r2': resistance2,
            's2': support2,
            'r3': resistance3,
            's3': support3
        }
    
    def volume_profile(self, data: pd.DataFrame, bins: int = 20) -> Dict[str, Any]:
        """Calculate volume profile."""
        price_min = data['low'].min()
        price_max = data['high'].max()
        price_bins = np.linspace(price_min, price_max, bins + 1)
        
        volume_profile = {}
        for i in range(bins):
            bin_lower = price_bins[i]
            bin_upper = price_bins[i + 1]
            bin_volume = data[
                (data['close'] >= bin_lower) & 
                (data['close'] < bin_upper)
            ]['volume'].sum()
            
            volume_profile[f"{bin_lower:.2f}-{bin_upper:.2f}"] = bin_volume
        
        # Find Volume Point of Control (VPOC)
        vpoc_level = max(volume_profile.items(), key=lambda x: x[1])[0]
        
        return {
            'volume_profile': volume_profile,
            'vpoc_level': vpoc_level,
            'total_volume': data['volume'].sum()
        }
    
    def vwap(self, data: pd.DataFrame) -> pd.Series:
        """Volume Weighted Average Price."""
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        vwap = (typical_price * data['volume']).cumsum() / data['volume'].cumsum()
        return vwap
    
    def momentum(self, data: pd.Series, period: int = 10) -> pd.Series:
        """Momentum indicator."""
        return data.diff(period)
    
    def rate_of_change(self, data: pd.Series, period: int = 10) -> pd.Series:
        """Rate of Change."""
        return (data / data.shift(period) - 1) * 100
    
    def money_flow_index(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                        volume: pd.Series, period: int = 14) -> pd.Series:
        """Money Flow Index."""
        typical_price = (high + low + close) / 3
        raw_money_flow = typical_price * volume
        
        # Positive and Negative Money Flow
        positive_mf = raw_money_flow.where(typical_price > typical_price.shift(1), 0)
        negative_mf = raw_money_flow.where(typical_price < typical_price.shift(1), 0)
        
        positive_mf_sum = positive_mf.rolling(window=period).sum()
        negative_mf_sum = negative_mf.rolling(window=period).sum()
        
        money_ratio = positive_mf_sum / negative_mf_sum
        mfi = 100 - (100 / (1 + money_ratio))
        
        return mfi
    
    def detect_trend(self, data: pd.Series, short_period: int = 20, long_period: int = 50) -> TrendDirection:
        """Detect trend direction using moving averages."""
        if len(data) < long_period:
            return TrendDirection.NEUTRAL
        
        short_ma = self.sma(data, short_period)
        long_ma = self.sma(data, long_period)
        
        current_short = short_ma.iloc[-1]
        current_long = long_ma.iloc[-1]
        prev_short = short_ma.iloc[-2]
        prev_long = long_ma.iloc[-2]
        
        # Check for strong trends
        if current_short > current_long and prev_short <= prev_long:
            return TrendDirection.STRONG_UP
        elif current_short < current_long and prev_short >= prev_long:
            return TrendDirection.STRONG_DOWN
        elif current_short > current_long:
            return TrendDirection.UP
        elif current_short < current_long:
            return TrendDirection.DOWN
        else:
            return TrendDirection.NEUTRAL
    
    def detect_market_regime(self, data: pd.DataFrame, period: int = 20) -> MarketRegime:
        """Detect market regime based on volatility and trend."""
        if len(data) < period:
            return MarketRegime.SIDEWAYS
        
        # Calculate volatility
        returns = data['close'].pct_change()
        volatility = returns.rolling(window=period).std() * np.sqrt(252)
        
        # Calculate trend strength
        trend = self.detect_trend(data['close'])
        
        current_volatility = volatility.iloc[-1]
        avg_volatility = volatility.mean()
        
        # Determine regime
        if current_volatility > avg_volatility * 1.5:
            return MarketRegime.VOLATILE
        elif trend in [TrendDirection.STRONG_UP, TrendDirection.UP]:
            return MarketRegime.BULLISH
        elif trend in [TrendDirection.STRONG_DOWN, TrendDirection.DOWN]:
            return MarketRegime.BEARISH
        else:
            return MarketRegime.SIDEWAYS
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all technical indicators for the given data."""
        if data.empty or len(data) < 50:
            return {}
        
        indicators = {}
        
        try:
            # Moving Averages
            indicators['sma_20'] = self.sma(data['close'], 20)
            indicators['sma_50'] = self.sma(data['close'], 50)
            indicators['sma_200'] = self.sma(data['close'], 200)
            indicators['ema_12'] = self.ema(data['close'], 12)
            indicators['ema_26'] = self.ema(data['close'], 26)
            
            # Momentum Indicators
            indicators['rsi'] = self.rsi(data['close'])
            indicators['stochastic'] = self.stochastic(data['high'], data['low'], data['close'])
            indicators['williams_r'] = self.williams_r(data['high'], data['low'], data['close'])
            indicators['cci'] = self.cci(data['high'], data['low'], data['close'])
            indicators['mfi'] = self.money_flow_index(data['high'], data['low'], data['close'], data['volume'])
            
            # MACD
            indicators['macd'] = self.macd(data['close'])
            
            # Volatility Indicators
            indicators['bollinger_bands'] = self.bollinger_bands(data['close'])
            indicators['atr'] = self.atr(data['high'], data['low'], data['close'])
            
            # Trend Indicators
            indicators['adx'] = self.adx(data['high'], data['low'], data['close'])
            
            # Volume Indicators
            indicators['vwap'] = self.vwap(data)
            indicators['volume_profile'] = self.volume_profile(data)
            
            # Market Analysis
            indicators['trend'] = self.detect_trend(data['close'])
            indicators['market_regime'] = self.detect_market_regime(data)
            
            # Fibonacci and Pivot Points
            if len(data) >= 20:
                recent_high = data['high'].iloc[-20:].max()
                recent_low = data['low'].iloc[-20:].min()
                indicators['fibonacci'] = self.fibonacci_retracement(recent_high, recent_low)
                
                last_close = data['close'].iloc[-1]
                indicators['pivot_points'] = self.pivot_points(recent_high, recent_low, last_close)
            
            self.logger.info("All technical indicators calculated successfully")
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
        
        return indicators


# Global instance
technical_indicators = TechnicalIndicators()