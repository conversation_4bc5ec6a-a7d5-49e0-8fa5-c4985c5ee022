"""
Binance Swap.

https://binance-docs.github.io/apidocs/futures/cn/#185368440e
Author: <PERSON>-<PERSON>tel
Date:   2022/04/07
Email:  <EMAIL>
"""

import hashlib
import hmac
from urllib.parse import urlencode, urljoin

from quant import const
from quant.asset import Asset
from quant.config import config
from quant.market import Kline, Orderbook, Ticker, Trade
from quant.order import Order
from quant.position import Position
from quant.quant import Quant
from quant.utils import logger, tools
from quant.utils.decorator import method_locker
from quant.utils.http_client import HttpRequests
from quant.utils.web import Websockets


class BinanceSwapRestApi:
    """Binance Swap REST API client.

    Attributes:
            access_key: Account's ACCESS KEY.
            secret_key: Account's SECRET KEY.
            host: HTTP request host, default is `https://fapi.binance.com`.
    """

    def __init__(
        self, access_key: str = None, secret_key: str = None, host: str = None
    ):
        """initialize REST API client."""
        self._host = host or const.EXCHANGES[const.BINANCE_SWAP]["host"]
        self._access_key = access_key or config.platforms["binance"]["access_key"]
        self._secret_key = secret_key or config.platforms["binance"]["secret_key"]

    def request(self, method, uri, params=None, body=None, headers=None, auth=False):
        """Do HTTP request.

        Args:
                method: HTTP request method. `GET` / `POST` / `DELETE` / `PUT`.
                uri: HTTP request uri.
                params: HTTP query params.
                body:   HTTP request body.
                headers: HTTP request headers.
                auth: If this request requires authentication.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        url = urljoin(self._host, uri)
        params = params if params else {}
        if body:
            params.update(body)
        if auth:
            # use server time
            # success, error = HttpRequests.get("https://fapi.binance.com/fapi/v1/time")
            # if error:
            # 	return None, error
            # timestamp = success["serverTime"]

            # use local time
            timestamp = tools.get_cur_timestamp_ms()

            query = urlencode(sorted(params.items()))
            query += f"&timestamp={timestamp}"
            secret = bytes(self._secret_key.encode("utf-8"))
            signature = hmac.new(
                secret, query.encode("utf-8"), hashlib.sha256
            ).hexdigest()
            query += f"&signature={signature}"
            headers = {"X-MBX-APIKEY": self._access_key}
            success, error = HttpRequests.request(
                method, url + "?" + query, headers=headers, timeout=10
            )
        else:
            success, error = HttpRequests.request(
                method, url, params=params, headers=headers, timeout=10
            )
        return success, error

    def _convert_symbol_name_format(self, symbol: str):
        """Convert BTC/USDT to BTCUSDT that this exchange needed."""
        s = symbol.replace("/", "")
        return s

    def get_exchange_info(self):
        """Get trading rules and trading pair information.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/fapi/v1/exchangeInfo")
        return success, error

    def set_lever_rate(self, symbol: str, leverage: int):
        """Set leverage for specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                leverage: Leverage you want to set.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/fapi/v1/leverage"
        body = {
            "symbol": self._convert_symbol_name_format(symbol),
            "leverage": leverage,
        }
        result = self.request("POST", uri, body=body, auth=True)
        return result

    def set_margin_mode(self, symbol: str, margin_mode: str):
        """Set margin mode.
        Args:
                symbol: Symbol's name.
                margin_mode: Margin's mode, `fixed` or `crossed`.
        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        if margin_mode == "fixed":
            margin_type = "ISOLATED"
        elif margin_mode == "crossed":
            margin_type = "CROSSED"
        else:
            error = "margin_mode error, only support fixed or crossed !"
            return None, error
        uri = "/fapi/v1/marginType"
        body = {
            "symbol": self._convert_symbol_name_format(symbol),
            "marginType": margin_type,
        }
        success, error = self.request("POST", uri, body=body, auth=True)
        return success, error

    def get_asset(self):
        """Get current account's asset information.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/fapi/v2/account", auth=True)
        return success, error

    def asset(self, currency: str) -> tuple[Asset | None, None | str]:
        """Get specific currency's processed information.

        Args:
                currency: Currency name, e.g. `BNB`.

        Returns:
                success: Asset dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_asset()
        if error:
            return None, error
        data = success
        platform = const.BINANCE_SWAP
        d = [b for b in data.get("assets") if b.get("asset") == currency][0]
        free = float(d["crossWalletBalance"])
        locked = float(d["openOrderInitialMargin"])
        total = float(d["walletBalance"])
        timestamp = d["updateTime"]
        asset = Asset(
            platform=platform,
            timestamp=timestamp,
            currency=currency,
            total=total,
            locked=locked,
            free=free,
            assets=data,
        )
        return asset, None

    def get_orderbook(self, symbol: str):
        """Get 5 levels of depth data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol), "limit": 5}
        success, error = self.request("GET", "/fapi/v1/depth", params=params)
        return success, error

    def orderbook(self, symbol: str) -> tuple[Orderbook | None, None | str]:
        """Get processed depth data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: OrderBook dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_orderbook(symbol)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_SWAP
        a = data["asks"]
        b = data["bids"]
        asks = [[float(i[0]), float(i[1])] for i in a]
        bids = [[float(i[0]), float(i[1])] for i in b]
        timestamp = data["E"]
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbooks=data,
        )
        return orderbook, None

    def get_trade(self, symbol: str):
        """Get the latest transaction data of specific data.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol), "limit": 1}
        success, error = self.request("GET", "/fapi/v1/trades", params=params)
        return success, error

    def trade(self, symbol: str) -> tuple[Trade | None, None | str]:
        """Get processed data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Trade dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_trade(symbol)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_SWAP
        action = const.BUY if data[0]["isBuyerMaker"] else const.SELL
        price = float(data[0]["price"])
        quantity = float(data[0]["qty"])
        timestamp = data[0]["time"]
        trade = Trade(
            platform=platform,
            symbol=symbol,
            action=action,
            price=price,
            quantity=quantity,
            timestamp=timestamp,
            trades=data,
        )
        return trade, None

    def get_kline(self, symbol: str, interval: const):
        """Get the latest 500 bar data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                interval: Bar's timeframe, e.g. `KLINE_1M`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {
            "symbol": self._convert_symbol_name_format(symbol),
            "interval": str(interval).lstrip("kline_"),
            "limit": 500,
        }
        success, error = self.request("GET", "/fapi/v1/klines", params=params)
        return success, error

    def kline(
        self, symbol: str, interval: const
    ) -> tuple[Kline | None, None | str]:
        """Get processed kline data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                interval: Kline's interval.

        Returns:
                success: Trade dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_kline(symbol=symbol, interval=interval)
        if error:
            return None, error
        klines = [
            [i[0], float(i[1]), float(i[2]), float(i[3]), float(i[4]), float(i[5])]
            for i in success
        ]
        t = [k[0] for k in klines]
        o = [k[1] for k in klines]
        h = [k[2] for k in klines]
        l = [k[3] for k in klines]
        c = [k[4] for k in klines]
        v = [k[5] for k in klines]
        platform = const.BINANCE_SWAP
        kline = Kline(
            platform=platform,
            symbol=symbol,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            timestamp=t,
            interval=interval,
            klines=klines,
        )
        return kline, None

    def buy(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create an buy order.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        info = {
            "symbol": self._convert_symbol_name_format(symbol),
            "side": "BUY",
            "positionSide": "LONG",
            "quantity": quantity,
        }
        if order_type == const.POST_ONLY:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTX"
            info["price"] = price
        elif order_type == const.MARKET:
            info["type"] = "MARKET"
        else:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTC"
            info["price"] = price
        result, error = self.request("POST", "/fapi/v1/order", body=info, auth=True)
        if error:
            return None, error
        order_no = result["orderId"]
        return order_no, None

    def sell(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create an sell order.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        info = {
            "symbol": self._convert_symbol_name_format(symbol),
            "side": "SELL",
            "positionSide": "LONG",
            "quantity": quantity,
        }
        if order_type == const.POST_ONLY:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTX"
            info["price"] = price
        elif order_type == const.MARKET:
            info["type"] = "MARKET"
        else:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTC"
            info["price"] = price
        result, error = self.request("POST", "/fapi/v1/order", body=info, auth=True)
        if error:
            return None, error
        order_no = result["orderId"]
        return order_no, None

    def buy_to_cover(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create an sell close
        Args:
                symbol: Symbol name, e.g. `BTCUSDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.
        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        info = {
            "symbol": self._convert_symbol_name_format(symbol),
            "side": "BUY",
            "positionSide": "SHORT",
            "quantity": quantity,
        }
        if order_type == const.POST_ONLY:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTX"
            info["price"] = price
        elif order_type == const.MARKET:
            info["type"] = "MARKET"
        else:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTC"
            info["price"] = price
        result, error = self.request("POST", "/fapi/v1/order", body=info, auth=True)
        if error:
            return None, error
        order_no = result["orderId"]
        return order_no, None

    def sell_short(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create an sell open
        Args:
                symbol: Symbol name, e.g. `BTCUSDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.
        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        info = {
            "symbol": self._convert_symbol_name_format(symbol),
            "side": "SELL",
            "positionSide": "SHORT",
            "quantity": quantity,
        }
        if order_type == const.POST_ONLY:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTX"
            info["price"] = price
        elif order_type == const.MARKET:
            info["type"] = "MARKET"
        else:
            info["type"] = "LIMIT"
            info["timeInForce"] = "GTC"
            info["price"] = price
        result, error = self.request("POST", "/fapi/v1/order", body=info, auth=True)
        if error:
            return None, error
        order_no = result["orderId"]
        return order_no, None

    def revoke_order(self, symbol: str, order_no: int | str):
        """Cancelling an unfilled order.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_no: Order's no.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {
            "symbol": self._convert_symbol_name_format(symbol),
            "orderId": str(order_no),
        }
        success, error = self.request(
            "DELETE", "/fapi/v1/order", params=params, auth=True
        )
        if error:
            return order_no, error
        else:
            return order_no, None

    def revoke_orders(self, symbol: str, order_nos: list[int | str]):
        """Cancel multiple orders by order ids.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_nos: Order's nos.

        Returns:
                success: Order's nos, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = [], []
        for order_no in order_nos:
            _, e = self.revoke_order(symbol, order_no)
            if e:
                error.append((order_no, e))
            else:
                success.append(order_no)
        return success, error

    def get_order_status(self, symbol: str, order_no: int | str):
        """Get order details by order id.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_no: order's no.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {
            "symbol": self._convert_symbol_name_format(symbol),
            "orderId": str(order_no),
        }
        success, error = self.request("GET", "/fapi/v1/order", params=params, auth=True)
        return success, error

    def order(
        self, symbol: str, order_no: int | str
    ) -> tuple[Order | None, None | str]:
        """Get processed information of specific
        Args:
                symbol: Symbol's name, e.g. `BTCUSDT`.
                order_no: order's no.
        Returns:
                success: Order dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_order_status(symbol, order_no)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_SWAP
        order_no = int(data["orderId"])
        if data["side"] == "BUY" and data["positionSide"] == "LONG":
            action = const.BUY
        elif data["side"] == "SELL" and data["positionSide"] == "SHORT":
            action = const.SELL_SHORT
        elif data["side"] == "SELL" and data["positionSide"] == "LONG":
            action = const.SELL
        elif data["side"] == "BUY" and data["positionSide"] == "SHORT":
            action = const.BUY_TO_COVER
        else:
            action = const.UNKNOWN

        if data["origType"] == "LIMIT":
            order_type = const.LIMIT
        elif data["origType"] == "MARKET":
            order_type = const.MARKET
        else:
            order_type = const.UNKNOWN

        price = float(data["price"])
        quantity = float(data["origQty"])
        filled_qty = float(data["executedQty"])
        remain = quantity - filled_qty
        if data["status"] == "CANCELED":
            status = const.CANCELED
        elif data["status"] == "NEW":
            status = const.SUBMITTED
        elif data["status"] == "PARTIALLY_FILLED":
            status = const.PARTIAL_FILLED
        elif data["status"] == "FILLED":
            status = const.FILLED
        elif data["status"] == "EXPIRED":
            status = const.FAILED
        else:
            status = const.UNKNOWN

        timestamp = data["time"]
        avg_price = float(data["avgPrice"])
        fee = 0.0
        update_time = data["updateTime"]
        order = Order(
            platform=platform,
            symbol=symbol,
            order_no=order_no,
            action=action,
            order_type=order_type,
            price=price,
            quantity=quantity,
            filled_qty=filled_qty,
            remain=remain,
            status=status,
            timestamp=timestamp,
            avg_price=avg_price,
            fee=fee,
            utime=update_time,
            orders=data,
        )
        return order, None

    def get_open_orders(self, symbol: str):
        """Get all open order information.
        Args:
                symbol: Symbol's name, e.g. `BTCUSDT`.
        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/fapi/v1/openOrders"
        params = {"symbol": self._convert_symbol_name_format(symbol)}
        success, error = self.request("GET", uri, params=params, auth=True)
        if error:
            return None, error
        else:
            order_nos = []
            for order_info in success:
                order_no = order_info["orderId"]
                order_nos.append(order_no)
            return order_nos, None

    def get_position(self):
        """Get all symbol's positions.
        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/fapi/v2/positionRisk"
        success, error = self.request("GET", uri, auth=True)
        return success, error

    def position(self, symbol: str) -> tuple[Position | None, None | str]:
        """Get processed position data of single symbol.

        Args:
                symbol: Symbol's name.

        Returns:
                success: Position dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_position()
        if error:
            return None, error
        data = success
        platform = const.BINANCE_SWAP
        timestamp = tools.get_cur_timestamp_ms()
        ds = [
            p
            for p in data
            if p.get("symbol") == self._convert_symbol_name_format(symbol)
        ]
        long_quantity = max(
            [float(d["positionAmt"]) if d["positionSide"] == "LONG" else 0 for d in ds]
        )
        long_avg_price = max(
            [float(d["entryPrice"]) if d["positionSide"] == "LONG" else 0 for d in ds]
        )
        short_quantity = max(
            [
                abs(float(d["positionAmt"])) if d["positionSide"] == "SHORT" else 0
                for d in ds
            ]
        )
        short_avg_price = max(
            [
                abs(float(d["entryPrice"])) if d["positionSide"] == "SHORT" else 0
                for d in ds
            ]
        )
        position = Position(
            platform=platform,
            symbol=symbol,
            long_quantity=long_quantity,
            long_avg_price=long_avg_price,
            short_quantity=short_quantity,
            short_avg_price=short_avg_price,
            utime=timestamp,
            positions=data,
        )
        return position, None

    def get_funding_rate(self, symbol: str):
        """Get funding rate.
        Args:
                symbol: Symbol's name.
        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol)}
        success, error = self.request("GET", "/fapi/v1/premiumIndex", params=params)
        return success, error

    def get_history_funding_rate(self, symbol: str):
        """
        Get history funding rate.
        Args:
                symbol: Symbol's name.
        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol)}
        success, error = self.request("GET", "/fapi/v1/fundingRate", params=params)
        return success, error

    def generate_listen_key(self):
        """Generate listen key, start a new user data stream.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        headers = {"X-MBX-APIKEY": self._access_key}
        success, error = self.request("POST", "/fapi/v1/listenKey", headers=headers)
        return success, error

    def put_listen_key(self):
        """Keep alive a user data stream to prevent a timeout.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        headers = {"X-MBX-APIKEY": self._access_key}
        success, error = self.request("PUT", "/fapi/v1/listenKey", headers=headers)
        return success, error

    def delete_listen_key(self):
        """Delete a listen key.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        headers = {"X-MBX-APIKEY": self._access_key}
        success, error = self.request("DELETE", "/fapi/v1/listenKey", headers=headers)
        return success, error


class BinanceSwapPublic:
    """Binance SWAP public websockets.

    Attributes:
            channels: Channels list, e.g. `["TRADE", "ORDERBOOK"]`.
            symbols: Symbols list, e.g. `["BTC/USDT", "ETH/USDT"]`.
            ticker_callback: Ticker callback function.
            orderbook_callback: OrderBook callback function.
            trade_callback: Trade callback function.
            kline_callback: Kline callback function.
            host: HTTP request host, default is `wss://fstream.binance.com/stream?streams=`.
    """

    def __init__(
        self,
        channels: list[str],
        symbols: list[str],
        ticker_callback=None,
        orderbook_callback=None,
        trade_callback=None,
        kline_callback=None,
        host=None,
    ):

        self._cc = {}
        self._ss = {}
        self._tickers = {}
        self._symbols = symbols
        self._record_ss_and_tickers()
        self._channels = channels
        self._url = host or const.EXCHANGES[const.BINANCE_SWAP]["wss"]
        self._kline_callback = kline_callback
        self._trade_callback = trade_callback
        self._orderbook_callback = orderbook_callback
        self._ticker_callback = ticker_callback
        url = self._make_url()
        self._ws = Websockets(
            url=url,
            connected_callback=self._connected_callback,
            process_callback=self._process_callback,
        )
        Quant.create_loop_task(interval=30, func=self._check_update)

    def _record_update(self, symbol: str, channel_type: str):
        """Record every channel's symbol information and it's last update time."""
        channel = f"{symbol}@{channel_type}"
        self._cc[channel] = tools.get_cur_timestamp_ms()

    def _record_ss_and_tickers(self):
        """Record all symbol's name, like`{"BTCUSDT": "BTC/USDT"}`, so we can get its origin format later. And also record all tickers."""
        for symbol in self._symbols:
            s = symbol.replace("/", "")
            self._ss[s] = symbol

            self._tickers[symbol] = {}

    def _make_url(self):
        """Generate request url."""
        for channel in self._channels:
            if channel == "ticker":
                for symbol in self._symbols:
                    s = symbol.replace("/", "")
                    self._url = self._url + "/" + s.lower() + "@ticker"
                    self._url = self._url + "/" + s.lower() + "@bookTicker"
            if channel == "orderbook":
                for symbol in self._symbols:
                    s = symbol.replace("/", "")
                    self._url = self._url + "/" + s.lower() + "@depth20@100ms"
            if channel.startswith("kline"):
                for symbol in self._symbols:
                    s = symbol.replace("/", "")
                    self._url = self._url + "/" + s.lower() + "@" + channel.lower()
            if channel == "trade":
                for symbol in self._symbols:
                    s = symbol.replace("/", "")
                    self._url = self._url + "/" + s.lower() + "@trade"
            self._url = self._url.replace("=/", "=")
        return self._url

    def _connected_callback(self):
        """Do action when websockets connected callback."""
        logger.info(
            "Binance Swap public Websockets connection successfully.", caller=self
        )

    @method_locker("binance_swap_public_process_callback.locker")
    def _process_callback(self, msg):
        """Process message that received from websockets connection.
        Args:
                msg: message received from websockets connection.
        """
        if not isinstance(msg, dict):
            return

        channel = msg.get("stream")

        if not channel:
            return
        if channel.endswith("@depth20@100ms"):
            self._process_orderbook(msg)
        elif msg.get("data").get("e") == "bookTicker":
            self._process_book_ticker(msg)
        elif msg.get("data").get("e") == "kline":
            self._process_kline(msg)
        elif msg.get("data").get("e") == "trade":
            self._process_trade(msg)
        elif msg.get("data").get("e") == "24hrTicker":
            self._process_ticker(msg)

    def _process_book_ticker(self, msg):
        """Process book ticker."""
        platform = const.BINANCE_SWAP
        s = msg.get("data").get("s")
        symbol = self._ss[s]
        data: dict = self._tickers.get(symbol)
        if not data:
            return
        ask_price = float(msg.get("data").get("a"))
        ask_quantity = float(msg.get("data").get("A"))
        bid_price = float(msg.get("data").get("b"))
        bid_quantity = float(msg.get("data").get("B"))
        t = msg.get("data").get("T")
        o = data.get("o")
        h = data.get("h")
        l = data.get("l")
        c = data.get("c")
        v = data.get("v")
        q = data.get("q")
        ticker = Ticker(
            platform=platform,
            symbol=symbol,
            ask_price=ask_price,
            ask_quantity=ask_quantity,
            bid_price=bid_price,
            bid_quantity=bid_quantity,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            turnover=q,
            timestamp=t,
            tickers=msg,
        )
        self._record_update(symbol=symbol, channel_type="bookTicker")
        if self._ticker_callback:
            Quant.create_single_task(self._ticker_callback, ticker)

    def _process_orderbook(self, msg):
        """Process orderbook data."""
        platform = const.BINANCE_SWAP
        s = str(msg.get("stream")).split("@depth20@100ms")[0].upper()
        symbol = self._ss[s]
        a = msg.get("data").get("a")
        b = msg.get("data").get("b")
        asks = [list(map(float, item)) for item in a]
        bids = [list(map(float, item)) for item in b]
        timestamp = tools.get_cur_timestamp_ms()
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbooks=msg,
        )
        self._record_update(symbol=symbol, channel_type=const.ORDERBOOK)
        if self._orderbook_callback:
            Quant.create_single_task(self._orderbook_callback, orderbook)

    def _process_kline(self, msg):
        """Process kline data."""
        platform = const.BINANCE_SWAP
        s = msg.get("data").get("s")
        symbol = self._ss[s]
        o = float(msg.get("data").get("k").get("o"))
        h = float(msg.get("data").get("k").get("h"))
        l = float(msg.get("data").get("k").get("l"))
        c = float(msg.get("data").get("k").get("c"))
        v = float(msg.get("data").get("k").get("v"))
        timestamp = msg.get("data").get("k").get("t")
        i = str(msg.get("data").get("k").get("i"))
        interval = "".join(["kline_", i.lower()])
        data = msg
        kline = Kline(
            platform=platform,
            symbol=symbol,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            timestamp=timestamp,
            interval=interval,
            klines=data,
        )
        self._record_update(symbol=symbol, channel_type=const.KLINE)
        if self._kline_callback:
            Quant.create_single_task(self._kline_callback, kline)

    def _process_trade(self, msg):
        """Process trade data."""
        platform = const.BINANCE_SWAP
        s = msg.get("data").get("s")
        symbol = self._ss[s]
        action = const.SELL if msg.get("data").get("m") else const.BUY
        price = float(msg.get("data").get("p"))
        quantity = float(msg.get("data").get("q"))
        timestamp = msg.get("data").get("T")
        data = msg
        trade = Trade(
            platform=platform,
            symbol=symbol,
            action=action,
            price=price,
            quantity=quantity,
            timestamp=timestamp,
            trades=data,
        )
        self._record_update(symbol=symbol, channel_type=const.TRADE)
        if self._trade_callback:
            Quant.create_single_task(self._trade_callback, trade)

    def _process_ticker(self, msg):
        """Process ticker data."""
        s = msg.get("data").get("s")
        symbol = self._ss[s]
        data: dict = self._tickers.get(symbol)
        o = float(msg.get("data").get("o"))
        h = float(msg.get("data").get("h"))
        l = float(msg.get("data").get("l"))
        c = float(msg.get("data").get("c"))
        v = float(msg.get("data").get("v"))
        q = float(msg.get("data").get("q"))
        data.update(o=o)
        data.update(h=h)
        data.update(l=l)
        data.update(c=c)
        data.update(v=v)
        data.update(q=q)
        self._record_update(symbol=symbol, channel_type=const.TICKER)

    def _check_update(self):
        """Check every channel's symbol and its last update time, if more than 1 minute, reconnect immediately."""
        cur_ms = tools.get_cur_timestamp_ms()
        for channel, timestamp in self._cc.items():
            if cur_ms - timestamp >= 1 * 60 * 1000:
                logger.warning(
                    f"{channel} more than 1min not updated! manual reconnecting right now!",
                    caller=self,
                )
                Quant.create_single_task(self._ws.reconnect)


class BinanceSwapPrivate:
    """Binance SWAP private websockets.

    Attributes:
            symbols: Symbols list, e.g. `["BTC/USDT", "ETH/USDT"]`.
            access_key: ACCOUNT'S ACCESS KEY.
            secret_key: ACCOUNT'S SECRET KEY.
            order_callback: Order callback function.
            asset_callback: Asset callback function.
            position_callback=Position callback function.
            host: HTTP request host, default is `wss://fstream.binance.com/stream?streams=`.
    """

    def __init__(
        self,
        symbols: list[str],
        access_key: str = None,
        secret_key: str = None,
        order_callback=None,
        asset_callback=None,
        position_callback=None,
        host=None,
    ):
        self._ss = {}
        self._symbols = symbols
        self._record_ss()
        self._access_key = access_key or config.platforms["binance"]["access_key"]
        self._secret_key = secret_key or config.platforms["binance"]["secret_key"]
        self._order_callback = order_callback
        self._asset_callback = asset_callback
        self._position_callback = position_callback
        self._ws = None
        self._listen_key = None
        self._url = host or const.EXCHANGES[const.BINANCE_SWAP]["wss"]
        Quant.create_single_task(self._initialize_websockets)
        self._put_listen_key_task_id = Quant.create_loop_task(
            30 * 60, self._put_listen_key
        )

    def _record_ss(self):
        """Record all symbol's name, like`{"BTCUSDT": "BTC/USDT"}`, so we can get its origin format later."""
        for symbol in self._symbols:
            s = symbol.replace("/", "")
            self._ss[s] = symbol

    def _connected_callback(self):
        """Do action when websockets connected callback."""
        logger.info(
            "Binance Swap private Websockets connection successfully.", caller=self
        )

        self._get_account_when_connected()

    def _get_account_when_connected(self):
        """When private websockets connected, get account information."""
        rest_api = BinanceSwapRestApi(self._access_key, self._secret_key)

        # ALL CURRENCY BALANCE.
        success, error = rest_api.get_asset()
        if error:
            logger.error("Get account ASSET error:", error, caller=self)
            return
        data = success["assets"]
        platform = const.BINANCE_SWAP
        for a in data:
            timestamp = a["updateTime"]
            currency = a["asset"]
            free = float(a["crossWalletBalance"])
            locked = float(a["openOrderInitialMargin"])
            total = float(a["walletBalance"])
            if total <= 0:
                continue
            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=currency,
                total=total,
                locked=locked,
                free=free,
                assets=a,
            )
            if self._asset_callback:
                Quant.create_single_task(self._asset_callback, asset)

        # OPEN ORDERS.
        for symbol in self._symbols:
            order_ids, error = rest_api.get_open_orders(symbol=symbol)
            if error:
                logger.error(f"Get {symbol} OPEN ORDERS error:", error, caller=self)
                continue
            for order_no in order_ids:
                order, error = rest_api.order(symbol=symbol, order_no=order_no)
                if error:
                    logger.error(
                        f"Get {symbol}'s {order_no} order status error:",
                        error,
                        caller=self,
                    )
                    continue
                if self._order_callback:
                    Quant.create_single_task(self._order_callback, order)

        # ALl POSITIONS.
        for symbol in self._symbols:
            position, error = rest_api.position(symbol=symbol)
            position.positions = None
            if error:
                logger.error(f"Get {symbol} POSITION error:", error, caller=self)
                continue
            if self._position_callback:
                Quant.create_single_task(self._position_callback, position)

    def _initialize_websockets(self):
        """Initialize websockets connection."""
        rest_api = BinanceSwapRestApi(self._access_key, self._secret_key)
        success, error = rest_api.generate_listen_key()
        if error:
            logger.error(f"post listen key error:{error}", caller=self)
            return
        self._listen_key = success["listenKey"]
        self._url += self._listen_key
        self._ws = Websockets(
            self._url,
            connected_callback=self._connected_callback,
            process_callback=self._process_callback,
        )

    def _put_listen_key(self):
        """Put listen key."""
        rest_api = BinanceSwapRestApi(self._access_key, self._secret_key)
        success, error = rest_api.put_listen_key()
        if error:
            logger.error(f"put_listen_key error: {error}", caller=self)
            logger.info("reconnecting websockets ...", caller=self)
            Quant.create_single_task(self._initialize_websockets)
            return
        logger.debug(f"put_listen_key successfully:{success}", caller=self)

    @method_locker("binance_swap_private_process_callback.locker")
    def _process_callback(self, msg):
        """Process message received from websockets connection.

        Args:
                msg: Message received from websockets connection.
        """
        if not isinstance(msg, dict):
            return
        if msg.get("data"):
            event = msg.get("data").get("e")
            if event == "ORDER_TRADE_UPDATE":
                self._process_order(msg)
            elif event == "ACCOUNT_UPDATE":
                self._process_asset(msg)
                self._process_position(msg)

    def _process_order(self, msg):
        """Process order data."""
        data = msg.get("data")
        platform = const.BINANCE_SWAP
        s = data["o"]["s"]
        symbol = self._ss.get(s, None)
        if not symbol:  # this symbol is not subscribed manually.
            return
        price = float(data["o"]["p"])
        quantity = float(data["o"]["q"])
        order_no = int(data["o"]["i"])
        timestamp = int(data["T"])
        avg_price = float(data["o"]["ap"])
        update_time = int(data["E"])
        filled_qty = float(data["o"]["z"])
        remain = quantity - filled_qty
        fee = float(data["o"]["n"]) if "n" in data["o"].keys() else 0.0

        if data["o"]["S"] == "BUY" and data["o"]["ps"] == "BOTH":
            action = const.BUY
        elif data["o"]["S"] == "SELL" and data["o"]["ps"] == "BOTH":
            action = const.SELL
        elif data["o"]["S"] == "BUY" and data["o"]["ps"] == "LONG":
            action = const.BUY
        elif data["o"]["S"] == "BUY" and data["o"]["ps"] == "SHORT":
            action = const.BUY_TO_COVER
        elif data["o"]["S"] == "SELL" and data["o"]["ps"] == "LONG":
            action = const.SELL
        elif data["o"]["S"] == "SELL" and data["o"]["ps"] == "SHORT":
            action = const.SELL_SHORT
        else:
            action = const.UNKNOWN

        if data["o"]["o"] == "LIMIT" and data["o"]["f"] == "GTC":
            order_type = const.LIMIT
        elif data["o"]["o"] == "LIMIT" and data["o"]["f"] == "GTX":
            order_type = const.POST_ONLY
        elif data["o"]["o"] == "MARKET":
            order_type = const.MARKET
        elif data["o"]["o"] == "STOP":
            order_type = "STOP"
        elif data["o"]["o"] == "TAKE_PROFIT":
            order_type = "TAKE_PROFIT"
        elif data["o"]["o"] == "LIQUIDATION":
            order_type = "LIQUIDATION"
        else:
            order_type = const.UNKNOWN

        if data["o"]["X"] == "CANCELED":
            status = const.CANCELED
        elif data["o"]["X"] == "NEW":
            status = const.SUBMITTED
        elif data["o"]["X"] == "PARTIALLY_FILLED":
            status = const.PARTIAL_FILLED
        elif data["o"]["X"] == "FILLED":
            status = const.FILLED
        elif data["o"]["X"] == "EXPIRED":
            status = const.FAILED
        elif data["o"]["X"] == "NEW_INSURANCE":
            status = const.FILLED
        elif data["o"]["X"] == "NEW_ADL":
            status = const.FILLED
        else:
            status = const.UNKNOWN

        order = Order(
            platform=platform,
            symbol=symbol,
            order_no=order_no,
            action=action,
            order_type=order_type,
            price=price,
            quantity=quantity,
            filled_qty=filled_qty,
            remain=remain,
            status=status,
            timestamp=timestamp,
            avg_price=avg_price,
            fee=fee,
            utime=update_time,
            orders=data,
        )
        if self._order_callback:
            Quant.create_single_task(self._order_callback, order)

    def _process_asset(self, msg):
        """Process asset data."""
        data = msg.get("data")
        for item in data["a"]["B"]:
            platform = const.BINANCE_SWAP
            currency = item["a"]
            free = float(item["cw"])
            locked = float(item["wb"]) - float(item["cw"])
            total = float(item["wb"])
            timestamp = int(data["T"])
            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=currency,
                total=total,
                locked=locked,
                free=free,
                assets=data,
            )
            if self._asset_callback:
                Quant.create_single_task(self._asset_callback, asset)

    def _process_position(self, msg):
        """Process position data."""
        data = msg.get("data")
        if not data["a"][
            "P"
        ]:  # 资金费率结算时，只推送资金信息而不会推送持仓信息，防止后面切片报错。
            return

        positions = data["a"]["P"]
        platform = const.BINANCE_SWAP
        s = data["a"]["P"][0]["s"]
        symbol = self._ss.get(s, None)
        if not symbol:  # this symbol is not subscribed manually.
            return
        timestamp = int(data["T"])

        total, long_quantity, long_avg_price, short_quantity, short_avg_price = (
            0,
            0,
            0,
            0,
            0,
        )
        for item in data["a"]["P"]:
            total += abs(float(item["pa"]))
        if total == 0:  # 所有持仓的绝对值总和为0，没有任何持仓时
            long_quantity = 0
            long_avg_price = 0
            short_quantity = 0
            short_avg_price = 0
        else:  # 有持仓时
            if float(data["a"]["P"][0]["pa"]) > 0:  # 如果单向持仓数量大于0
                long_quantity = float(data["a"]["P"][0]["pa"])
                long_avg_price = float(data["a"]["P"][0]["ep"])
                short_quantity = 0
                short_avg_price = 0
            elif float(data["a"]["P"][0]["pa"]) < 0:  # 如果单向持仓数量小于0
                long_quantity = 0
                long_avg_price = 0
                short_quantity = abs(float(data["a"]["P"][0]["pa"]))
                short_avg_price = float(data["a"]["P"][0]["ep"])
            else:  # 双向持仓有仓位时
                for i in data["a"]["P"]:
                    if i["ps"] in ["LONG", "SHORT"]:
                        if i["ps"] == "LONG":
                            if float(i["pa"]) > 0:
                                long_quantity = float(i["pa"])
                                long_avg_price = float(i["ep"])
                            elif float(i["pa"]) == 0:
                                long_quantity = 0
                                long_avg_price = 0
                        if i["ps"] == "SHORT":
                            if float(i["pa"]) < 0:
                                short_quantity = abs(float(i["pa"]))
                                short_avg_price = float(i["ep"])
                            elif float(i["pa"]) == 0:
                                short_quantity = 0
                                short_avg_price = 0
        if not long_quantity:
            long_quantity = 0
            long_avg_price = 0
        if not short_quantity:
            short_quantity = 0
            short_avg_price = 0

        position = Position(
            platform=platform,
            symbol=symbol,
            long_quantity=long_quantity,
            long_avg_price=long_avg_price,
            short_quantity=short_quantity,
            short_avg_price=short_avg_price,
            utime=timestamp,
            positions=positions,
        )
        if self._position_callback:
            Quant.create_single_task(self._position_callback, position)
