"""
Okx Spot.

https://www.okx.com/docs-v5/zh/#7d0fb355e8
Author: <PERSON><PERSON><PERSON><PERSON>
Date:   2022/05/06
Email:  <EMAIL>
"""

import base64
import hmac
import json
from urllib.parse import urljoin

from quant import const
from quant.asset import Asset
from quant.config import config
from quant.market import Kline, Orderbook, Ticker, Trade
from quant.order import Order
from quant.quant import Quant
from quant.utils import logger, tools
from quant.utils.decorator import method_locker
from quant.utils.http_client import HttpRequests
from quant.utils.web import Websockets


class OkxSpotRestApi:
    """Okx Spot REST API client.

    Attributes:
            access_key: Account's ACCESS KEY.
            secret_key: Account's SECRET KEY.
            host: HTTP request host, default is `https://www.okx.com`.
    """

    def __init__(
        self,
        access_key: str = None,
        secret_key: str = None,
        passphrase: str = None,
        host: str = None,
    ):
        """initialize REST API client."""
        self._host = host or const.EXCHANGES[const.OKX_SPOT]["host"]
        self._access_key = access_key or config.platforms["okx"]["access_key"]
        self._secret_key = secret_key or config.platforms["okx"]["secret_key"]
        self._passphrase = passphrase or config.platforms["okx"]["passphrase"]

    def request(self, method, uri, params=None, body=None, headers=None, auth=False):
        """Do HTTP request.

        Args:
                method: HTTP request method. `GET` / `POST` / `DELETE` / `PUT`.
                uri: HTTP request uri.
                params: HTTP query params.
                body:   HTTP request body.
                headers: HTTP request headers.
                auth: If this request requires authentication.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        if params:
            query = "&".join(
                [f"{k}={params[k]}" for k in sorted(params.keys())]
            )
            uri += "?" + query
        url = urljoin(self._host, uri)

        # Generate signature.
        if auth:
            t = str(tools.get_cur_timestamp_ms())
            timestamp = "".join([t[0:10], ".", t[10:14]])
            if body:
                body = json.dumps(body)
            else:
                body = ""
            message = str(timestamp) + str.upper(method) + uri + str(body)
            mac = hmac.new(
                bytes(self._secret_key, encoding="utf8"),
                bytes(message, encoding="utf-8"),
                digestmod="sha256",
            )
            d = mac.digest()
            sign = base64.b64encode(d).decode()

            if not headers:
                headers = {}
            headers["Content-Type"] = "application/json"
            headers["OK-ACCESS-KEY"] = self._access_key
            headers["OK-ACCESS-SIGN"] = sign
            headers["OK-ACCESS-TIMESTAMP"] = str(timestamp)
            headers["OK-ACCESS-PASSPHRASE"] = self._passphrase
        result, error = HttpRequests.request(
            method, url, data=body, headers=headers, timeout=10
        )
        if error:
            return None, error
        if result.get("code") and result.get("code") != "0":
            return None, result
        return result, error

    def _convert_symbol_name_format(self, symbol: str):
        """Convert BTC/USDT to BTC/USDT that this exchange needed."""
        s = symbol.replace("/", "-")
        return s

    def get_exchange_info(self):
        """Get trading rules and trading pair information.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/public/instruments"
        params = {"instType": "SPOT"}
        success, error = self.request(method="GET", uri=uri, params=params)
        return success, error

    def get_orderbook(self, symbol: str):
        """Get 5 levels of depth data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/market/books"
        params = {"instId": self._convert_symbol_name_format(symbol), "sz": 5}
        success, error = self.request(method="GET", uri=uri, params=params)
        return success, error

    def orderbook(self, symbol: str) -> tuple[Orderbook | None, None | str]:
        """Get processed depth data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: OrderBook dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_orderbook(symbol)
        if error:
            return None, error
        data = success["data"][0]
        platform = const.OKX_SPOT
        a = data["asks"]
        b = data["bids"]
        asks = [[float(i[0]), float(i[1])] for i in a]
        bids = [[float(i[0]), float(i[1])] for i in b]
        timestamp = int(data["ts"])
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbooks=success,
        )
        return orderbook, None

    def get_asset(self, currency: str):
        """Get current account's asset information.

        Args:
                currency: Currency's name, e.g. `USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        params = {"ccy": currency}
        success, error = self.request(
            "GET", "/api/v5/account/balance", params=params, auth=True
        )
        return success, error

    def asset(self, currency: str) -> tuple[Asset | None, None | str]:
        """Get specific currency's processed information.

        Args:
                currency: Currency name, e.g. `BTC`.

        Returns:
                success: Asset dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_asset(currency)
        if error:
            return None, error
        if not success.get("data")[0].get("details"):
            currency = currency
            free = 0
            locked = 0
            total = 0
        else:
            data = success["data"][0]["details"][0]
            currency = data["ccy"]
            free = float(data["availEq"])
            locked = float(data["frozenBal"])
            total = float(data["eq"])
        timestamp = tools.get_cur_timestamp_ms()
        platform = const.OKX_SPOT
        asset = Asset(
            platform=platform,
            timestamp=timestamp,
            currency=currency,
            total=total,
            locked=locked,
            free=free,
            assets=success,
        )
        return asset, None

    def get_trade(self, symbol: str):
        """Get the latest transaction data of specific data.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/market/trades"
        params = {"instId": self._convert_symbol_name_format(symbol), "limit": 1}
        success, error = self.request(method="GET", uri=uri, params=params)
        return success, error

    def trade(self, symbol: str) -> tuple[Trade | None, None | str]:
        """Get processed data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.

        Returns:
                success: Trade dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_trade(symbol)
        if error:
            return None, error
        data = success["data"][0]
        platform = const.OKX_SPOT
        action = (data["side"]).upper()
        price = float(data["px"])
        quantity = float(data["sz"])
        timestamp = int(data["ts"])
        trade = Trade(
            platform=platform,
            symbol=symbol,
            action=action,
            price=price,
            quantity=quantity,
            timestamp=timestamp,
            trades=success,
        )
        return trade, None

    def get_kline(self, symbol: str, interval: const):
        """Get the latest 500 bar data of specific symbol.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                interval: Bar's timeframe, e.g. `KLINE_1M`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        bar = (
            str(interval).lstrip("kline_").lower()
            if str(interval).endswith("m")
            else str(interval).lstrip("kline_").upper()
        )
        uri = "/api/v5/market/candles"
        params = {
            "instId": self._convert_symbol_name_format(symbol),
            "bar": bar,
            "limit": 200,
        }
        success, error = self.request(method="GET", uri=uri, params=params)
        return success, error

    def kline(
        self, symbol: str, interval: const
    ) -> tuple[Kline | None, None | str]:
        """Get processed kline data of specific symbol.
        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                interval: Kline's interval.
        Returns:
                success: Trade dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_kline(symbol=symbol, interval=interval)
        if error:
            return None, error
        data = success["data"]
        data.reverse()
        klines = [
            [int(i[0]), float(i[1]), float(i[2]), float(i[3]), float(i[4]), float(i[5])]
            for i in data
        ]
        t = [k[0] for k in klines]
        o = [k[1] for k in klines]
        h = [k[2] for k in klines]
        l = [k[3] for k in klines]
        c = [k[4] for k in klines]
        v = [k[5] for k in klines]
        platform = const.OKX_SPOT
        kline = Kline(
            platform=platform,
            symbol=symbol,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            timestamp=t,
            interval=interval,
            klines=klines,
        )
        return kline, None

    def buy(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create a buy order.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/trade/order"
        data = {
            "instId": self._convert_symbol_name_format(symbol),
            "tdMode": "cash",
            "side": "buy",
            "sz": quantity,
        }
        if order_type == const.POST_ONLY:
            data["ordType"] = "post_only"
            data["px"] = price
        elif order_type == const.MARKET:
            data["ordType"] = "market"
        else:
            data["ordType"] = "limit"
            data["px"] = price
        success, error = self.request(method="POST", uri=uri, body=data, auth=True)
        if error:
            return None, error
        return success["data"][0]["ordId"], error

    def sell(
        self,
        symbol: str,
        price: float | int | str | None,
        quantity: float | int | str,
        order_type: const = None,
    ):
        """Create a sell order.

        Args:
                symbol: Symbol name, e.g. `BTC/USDT`.
                price: Commission price.
                quantity: Commission quantity.
                order_type: Order's type, `POST_ONLY` or `LIMIT` or `MARKET`.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/trade/order"
        data = {
            "instId": self._convert_symbol_name_format(symbol),
            "tdMode": "cash",
            "side": "sell",
            "sz": quantity,
        }
        if order_type == const.POST_ONLY:
            data["ordType"] = "post_only"
            data["px"] = price
        elif order_type == const.MARKET:
            data["ordType"] = "market"
        else:
            data["ordType"] = "limit"
            data["px"] = price
        success, error = self.request(method="POST", uri=uri, body=data, auth=True)
        if error:
            return None, error
        return success["data"][0]["ordId"], error

    def revoke_order(self, symbol: str, order_no: int | str):
        """Cancelling an unfilled order.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_no: Order's no.

        Returns:
                success: Order's no, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/trade/cancel-order"
        data = {"instId": self._convert_symbol_name_format(symbol), "ordId": order_no}
        _, error = self.request(method="POST", uri=uri, body=data, auth=True)
        if error:
            return order_no, error
        else:
            return order_no, None

    def revoke_orders(self, symbol: str, order_nos: list[int | str]):
        """Cancel multiple orders by order ids.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_nos: Order's nos.

        Returns:
                success: Order's nos, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = [], []
        for order_no in order_nos:
            _, e = self.revoke_order(symbol, order_no)
            if e:
                error.append((order_no, e))
            else:
                success.append(order_no)
        return success, error

    def get_order_status(self, symbol: str, order_no: int | str):
        """Get order details by order id.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_no: order's no.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/trade/order"
        params = {"instId": self._convert_symbol_name_format(symbol), "ordId": order_no}
        success, error = self.request(method="GET", uri=uri, params=params, auth=True)
        return success, error

    def order(
        self, symbol: str, order_no: int | str
    ) -> tuple[Order | None, None | str]:
        """Get processed information of specific symbol.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.
                order_no: order's no.

        Returns:
                success: Order dataclass, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        success, error = self.get_order_status(symbol, order_no)
        if error:
            return None, error
        data = success["data"][0]
        platform = const.OKX_SPOT
        order_no = data["ordId"]
        action = data["side"].upper()
        order_type = data["ordType"].upper()
        symbol = data["instId"]
        price = float(data["px"]) if data["px"] else 0.0
        quantity = float(data["sz"])
        filled_qty = float(data["accFillSz"])
        remain = quantity - filled_qty
        timestamp = int(data["cTime"])
        avg_price = float(data["avgPx"]) if data["avgPx"] else 0.0
        fee = None if not data["fee"] else float(data["fee"])
        update_time = int(data["uTime"])

        if data["state"] == "canceled":
            status = const.CANCELED
        elif data["state"] == "live":
            status = const.SUBMITTED
        elif data["state"] == "partially_filled":
            status = const.PARTIAL_FILLED
        elif data["state"] == "filled":
            status = const.FILLED
        else:
            status = const.UNKNOWN

        order = Order(
            platform=platform,
            symbol=symbol,
            order_no=order_no,
            action=action,
            order_type=order_type,
            price=price,
            quantity=quantity,
            filled_qty=filled_qty,
            remain=remain,
            status=status,
            timestamp=timestamp,
            avg_price=avg_price,
            fee=fee,
            utime=update_time,
            orders=success,
        )
        return order, None

    def get_open_orders(self, symbol: str):
        """Get all open order information.

        Args:
                symbol: Symbol's name, e.g. `BTC/USDT`.

        Returns:
                success: Success results, otherwise it's None.
                error: Error information, otherwise it's None.
        """
        uri = "/api/v5/trade/orders-pending"
        params = {
            "instType": "SPOT",
            "instId": self._convert_symbol_name_format(symbol),
        }
        success, error = self.request(method="GET", uri=uri, params=params, auth=True)
        if error:
            return None, error
        else:
            order_ids = []
            if success.get("data"):
                for order_info in success["data"]:
                    order_ids.append(order_info["ordId"])
            return order_ids, None


class OkxSpotPublic:
    """Okx SPOT public websockets.

    Attributes:
            channels: Channels list, e.g. `["TRADE", "ORDERBOOK"]`.
            symbols: Symbols list, e.g. `["BTC/USDT", "ETH/USDT"]`.
            ticker_callback: Ticker callback function.
            orderbook_callback: OrderBook callback function.
            trade_callback: Trade callback function.
            kline_callback: Kline callback function.
            host: HTTP request host, default is `wss://ws.okex.com:8443/ws/v5/public`.
    """

    def __init__(
        self,
        channels: list[str],
        symbols: list[str],
        ticker_callback=None,
        orderbook_callback=None,
        trade_callback=None,
        kline_callback=None,
        host=None,
    ):
        self._cc = {}
        self._ss = {}
        self._symbols = symbols
        self._record_ss()
        self._channels = channels
        self._url = host or const.EXCHANGES[const.OKX_SPOT]["wss"] + "/public"
        self._kline_callback = kline_callback
        self._trade_callback = trade_callback
        self._orderbook_callback = orderbook_callback
        self._ticker_callback = ticker_callback
        self._ws = Websockets(
            url=self._url,
            connected_callback=self._connected_callback,
            process_callback=self._process_callback,
        )
        Quant.create_loop_task(interval=30, func=self._check_update)
        Quant.create_loop_task(20, self._send_heartbeat_msg)

    def _record_update(self, symbol: str, channel_type: str):
        """Record every channel's symbol information, and it's last update time."""
        channel = f"{symbol}@{channel_type}"
        self._cc[channel] = tools.get_cur_timestamp_ms()

    def _record_ss(self):
        """Record all symbol's name, like`{"BTCUSDT": "BTC/USDT"}`, so we can get its origin format later."""
        for symbol in self._symbols:
            s = symbol.replace("/", "-")
            self._ss[s] = symbol

    def _connected_callback(self):
        channel_list = []
        for channel in self._channels:
            if channel.startswith("kline_"):
                bar = (
                    str(channel).lstrip("kline_").lower()
                    if str(channel).endswith("m")
                    else str(channel).lstrip("kline_").upper()
                )
                for symbol in self._symbols:
                    s = symbol.replace("/", "-")
                    channel_list.append({"channel": "candle%s" % bar, "instId": s})
            if channel == const.ORDERBOOK:
                for symbol in self._symbols:
                    s = symbol.replace("/", "-")
                    channel_list.append({"channel": "books5", "instId": s})
            if channel == const.TRADE:
                for symbol in self._symbols:
                    s = symbol.replace("/", "-")
                    channel_list.append({"channel": "trades", "instId": s})
            if channel == const.TICKER:
                for symbol in self._symbols:
                    s = symbol.replace("/", "-")
                    channel_list.append({"channel": "tickers", "instId": s})
        data = {"op": "subscribe", "args": channel_list}
        self._ws.send(data)

    def _send_heartbeat_msg(self):
        try:
            self._ws.send("ping")
        except ConnectionResetError:
            logger.error("send ping message error !", caller=self)

    @method_locker("okx_spot_public_process_callback.locker")
    def _process_callback(self, msg):
        if msg == "pong":
            return

        event = msg.get("event")
        if event:
            if event == "subscribe":
                logger.info(
                    f"OkexSpot public Websockets connection successfully:{msg}",
                    caller=self,
                )
                return
            elif event == "error":
                logger.error(
                    f"OkexSpot public Websockets connection failed: {msg}",
                    caller=self,
                )
                return
        else:
            channel = msg.get("arg").get("channel")
            if channel == "books5":
                self._process_orderbook(msg)
            elif str(channel).startswith("candle"):
                self._process_kline(msg)
            elif channel == "trades":
                self._process_trade(msg)
            elif channel == "tickers":
                self._process_ticker(msg)

    def _process_ticker(self, msg):
        """Process ticker."""
        s = str(msg.get("arg")["instId"])
        symbol = self._ss[s]
        platform = const.OKX_SPOT
        ask_price = float(msg.get("data")[0]["askPx"])
        ask_quantity = float(msg.get("data")[0].get("askSz"))
        bid_price = float(msg.get("data")[0].get("bidPx"))
        bid_quantity = float(msg.get("data")[0].get("bidSz"))
        o = float(msg.get("data")[0].get("open24h"))
        h = float(msg.get("data")[0].get("high24h"))
        l = float(msg.get("data")[0].get("low24h"))
        c = float(msg.get("data")[0].get("last"))
        v = float(msg.get("data")[0].get("vol24h"))
        q = float(msg.get("data")[0].get("volCcy24h"))
        t = int(msg.get("data")[0].get("ts"))
        ticker = Ticker(
            platform=platform,
            symbol=symbol,
            ask_price=ask_price,
            ask_quantity=ask_quantity,
            bid_price=bid_price,
            bid_quantity=bid_quantity,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            turnover=q,
            timestamp=t,
            tickers=msg,
        )
        self._record_update(symbol=symbol, channel_type=const.TICKER)
        if self._ticker_callback:
            Quant.create_single_task(self._ticker_callback, ticker)

    def _process_orderbook(self, msg):
        """Process orderbook data."""
        s = str(msg.get("arg")["instId"])
        symbol = self._ss[s]
        platform = const.OKX_SPOT
        a = msg.get("data")[0]["asks"]
        b = msg.get("data")[0]["bids"]
        asks = [[float(i[0]), float(i[1])] for i in a]
        bids = [[float(i[0]), float(i[1])] for i in b]
        timestamp = int(msg.get("data")[0]["ts"])
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbooks=msg,
        )
        self._record_update(symbol=symbol, channel_type=const.ORDERBOOK)
        if self._orderbook_callback:
            Quant.create_single_task(self._orderbook_callback, orderbook)

    def _process_kline(self, msg):
        """Process kline data."""
        platform = const.OKX_SPOT
        interval = str(msg.get("arg")["channel"]).replace("candle", "kline_").lower()
        s = str(msg.get("arg")["instId"])
        symbol = self._ss[s]
        timestamp = int(msg.get("data")[-1][0])
        o = float(msg.get("data")[-1][1])
        h = float(msg.get("data")[-1][2])
        l = float(msg.get("data")[-1][3])
        c = float(msg.get("data")[-1][4])
        v = float(msg.get("data")[-1][5])
        kline = Kline(
            platform=platform,
            symbol=symbol,
            open=o,
            high=h,
            low=l,
            close=c,
            volume=v,
            timestamp=timestamp,
            interval=interval,
            klines=msg,
        )
        self._record_update(symbol=symbol, channel_type=const.KLINE)
        if self._kline_callback:
            Quant.create_single_task(self._kline_callback, kline)

    def _process_trade(self, msg):
        """Process trade data."""
        platform = const.OKX_SPOT
        s = str(msg.get("arg")["instId"])
        symbol = self._ss[s]
        action = str(msg.get("data")[0]["side"]).upper()
        price = float(msg.get("data")[0]["px"])
        quantity = float(msg.get("data")[0]["sz"])
        timestamp = int(msg.get("data")[0]["ts"])
        trade = Trade(
            platform=platform,
            symbol=symbol,
            action=action,
            price=price,
            quantity=quantity,
            timestamp=timestamp,
            trades=msg,
        )
        self._record_update(symbol=symbol, channel_type=const.TRADE)
        if self._trade_callback:
            Quant.create_single_task(self._trade_callback, trade)

    def _check_update(self):
        """Check every channel's symbol and its last update time, if more than 1 minute, reconnect immediately."""
        cur_ms = tools.get_cur_timestamp_ms()
        for channel, timestamp in self._cc.items():
            if cur_ms - timestamp >= 1 * 60 * 1000:
                logger.warning(
                    f"{channel} more than 1min not updated! manual reconnecting right now!",
                    caller=self,
                )
                Quant.create_single_task(self._ws.reconnect)


class OkxSpotPrivate:
    """Okx SPOT private websockets.

    Attributes:
            symbols: Symbols list, e.g. `["BTC/USDT", "ETH/USDT"]`.
            access_key: ACCOUNT'S ACCESS KEY.
            secret_key: ACCOUNT'S SECRET KEY.
            passphrase: ACCOUNT'S PASSPHRASE.
            order_callback: Order callback function.
            asset_callback: Asset callback function.
            host: HTTP request host, default is `wss://ws.okex.com:8443/ws/v5/private`.
    """

    def __init__(
        self,
        symbols: list[str],
        access_key: str = None,
        secret_key: str = None,
        passphrase: str = None,
        order_callback=None,
        asset_callback=None,
        host=None,
    ):
        self._ss = {}
        self._symbols = symbols
        self._record_ss()
        self._access_key = access_key or config.platforms["okx"]["access_key"]
        self._secret_key = secret_key or config.platforms["okx"]["secret_key"]
        self._passphrase = passphrase or config.platforms["okx"]["passphrase"]
        self._order_callback = order_callback
        self._asset_callback = asset_callback
        self._ws = None
        self._url = host or const.EXCHANGES[const.OKX_SPOT]["wss"] + "/private"
        Quant.create_single_task(self._initialize_websockets)
        Quant.create_loop_task(20, self._send_heartbeat_msg)

    def _record_ss(self):
        """Record all symbol's name, like`{"BTC-USDT": "BTC/USDT"}`, so we can get its origin format later."""
        for symbol in self._symbols:
            s = symbol.replace("/", "-")
            self._ss[s] = symbol

    def _send_heartbeat_msg(self):
        try:
            self._ws.send("ping")
        except ConnectionResetError:
            logger.error("send ping message error !", caller=self)

    def _connected_callback(self):
        """Do action when websockets connected callback."""
        logger.info("Okx Spot private Websockets connection successfully.", caller=self)
        self._get_account_when_connected()
        t = str(tools.get_cur_timestamp_ms())
        timestamp = "".join([t[0:10], ".", t[10:14]])
        message = str(timestamp) + "GET" + "/users/self/verify"
        mac = hmac.new(
            bytes(self._secret_key, encoding="utf8"),
            bytes(message, encoding="utf8"),
            digestmod="sha256",
        )
        d = mac.digest()
        signature = base64.b64encode(d).decode()
        data = {
            "op": "login",
            "args": [
                {
                    "apiKey": self._access_key,
                    "passphrase": self._passphrase,
                    "timestamp": timestamp,
                    "sign": signature,
                }
            ],
        }
        self._ws.send(json.dumps(data))

    def _get_account_when_connected(self):
        """When private websockets connected, get account information."""
        rest_api = OkxSpotRestApi(self._access_key, self._secret_key, self._passphrase)
        # OPEN ORDERS.
        for symbol in self._symbols:
            order_ids, error = rest_api.get_open_orders(symbol=symbol)
            if error:
                logger.error(f"Get {symbol} OPEN ORDERS error:", error, caller=self)
                continue
            for order_no in order_ids:
                order, error = rest_api.order(symbol=symbol, order_no=order_no)
                if error:
                    logger.error(
                        f"Get {symbol}'s {order_no} order status error:",
                        error,
                        caller=self,
                    )
                    continue
                if self._order_callback:
                    Quant.create_single_task(self._order_callback, order)

    def _initialize_websockets(self):
        """Initialize websockets connection."""
        self._ws = Websockets(
            self._url, self._connected_callback, process_callback=self._process_callback
        )

    @method_locker("okx_spot_private_process_callback.locker")
    def _process_callback(self, msg):
        if msg == "pong":
            return

        event = msg.get("event")
        if event:
            if event == "login":
                if msg.get("code") == "0":
                    logger.info(
                        f"OkxSpot private Websockets connection authorized successfully:{msg}",
                        caller=self,
                    )
                    channel_list = [
                        {"channel": "orders", "instType": "SPOT"},
                        {"channel": "account"},
                    ]
                    data = {"op": "subscribe", "args": channel_list}
                    self._ws.send(json.dumps(data))
                    return
                else:
                    logger.error(
                        f"OkxSpot private Websockets connection authorized failed:{msg}",
                        caller=self,
                    )
                    return
            elif event == "subscribe":
                logger.info(
                    f"OkxSpot private Websockets connection successfully:{msg}",
                    caller=self,
                )
                return
            elif msg.get("event") == "error":
                logger.error(
                    f"OkxSpot private Websockets connection failed: {msg}",
                    caller=self,
                )
                return
        else:
            channel = msg.get("arg").get("channel")
            if channel == "orders":
                self._process_order(msg)
            elif channel == "account":
                self._process_asset(msg)

    def _process_order(self, msg):
        """Process order data."""
        for item in msg.get("data"):
            platform = const.OKX_SPOT
            s = item["instId"]
            symbol = self._ss.get(s, None)
            if not symbol:  # this symbol is not subscribed manually.
                return
            order_no = item["ordId"]
            price = float(item["px"]) if item["px"] else 0.0
            quantity = float(item["sz"])
            filled_qty = float(item["accFillSz"])
            remain = quantity - filled_qty
            timestamp = int(item["cTime"])
            avg_price = float(item["avgPx"]) if item["avgPx"] else 0.0
            fee = 0.0 if not float(item["fee"]) else float(float(item["fee"]))
            update_time = int(item["uTime"])

            if item["side"] == "buy" and item["posSide"] == "long":
                action = const.BUY
            elif item["side"] == "sell" and item["posSide"] == "short":
                action = const.SELL_SHORT
            elif item["side"] == "sell" and item["posSide"] == "long":
                action = const.SELL
            elif item["side"] == "buy" and item["posSide"] == "short":
                action = const.BUY_TO_COVER
            elif item["side"] == "buy" and item["posSide"] == "net":
                action = const.BUY
            elif item["side"] == "sell" and item["posSide"] == "net":
                action = const.SELL
            elif item["side"] == "buy" and item["posSide"] == "":
                action = const.BUY
            elif item["side"] == "sell" and item["posSide"] == "":
                action = const.SELL
            else:
                action = const.UNKNOWN

            if item["ordType"] == "limit":
                order_type = const.LIMIT
            elif item["ordType"] == "market":
                order_type = const.MARKET
            elif item["ordType"] == "post_only":
                order_type = const.POST_ONLY
            else:
                order_type = const.UNKNOWN

            if item["state"] == "canceled":
                status = const.CANCELED
            elif item["state"] == "live":
                status = const.SUBMITTED
            elif item["state"] == "partially_filled":
                status = const.PARTIAL_FILLED
            elif item["state"] == "filled":
                status = const.FILLED
            else:
                status = const.UNKNOWN
            order = Order(
                platform=platform,
                symbol=symbol,
                order_no=order_no,
                action=action,
                order_type=order_type,
                price=price,
                quantity=quantity,
                filled_qty=filled_qty,
                remain=remain,
                status=status,
                timestamp=timestamp,
                avg_price=avg_price,
                fee=fee,
                utime=update_time,
                orders=msg,
            )
            if self._order_callback:
                Quant.create_single_task(self._order_callback, order)

    def _process_asset(self, msg):
        """Process asset data."""
        for item in msg.get("data")[0].get("details"):
            platform = const.OKX_SPOT
            currency = item["ccy"]
            total = float(item["eq"]) if item["eq"] else 0
            free = float(item["availEq"]) if item["availEq"] else 0
            locked = float(item["frozenBal"]) if item["frozenBal"] else 0
            timestamp = int(item["uTime"])
            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=currency,
                total=total,
                locked=locked,
                free=free,
                assets=item,
            )
            if self._asset_callback:
                Quant.create_single_task(self._asset_callback, asset)
