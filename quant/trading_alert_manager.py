"""
Trading-focused Alert Manager

Concentrates on trading-specific alerts with proper suppression and prioritization.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict

from quant.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TradingAlert:
    """Trading-specific alert data structure"""
    alert_id: str
    alert_type: str
    severity: str  # "trading_critical", "trading_warning", "system_info"
    component: str
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class TradingAlertManager:
    """Focuses on trading-critical alerts only"""
    
    def __init__(self, config_path: str = "alert_thresholds.json"):
        self.logger = logger
        self.active_alerts: Dict[str, TradingAlert] = {}
        self.alert_history: List[TradingAlert] = []
        self.suppression_rules: Dict[str, datetime] = {}
        
        # Load thresholds
        self.thresholds = self._load_thresholds(config_path)
        
        # Alert suppression windows (seconds)
        self.suppression_windows = {
            "signal_generation_failure": 300,  # 5 minutes
            "notification_failure": 300,       # 5 minutes
            "settlement_failure": 600,         # 10 minutes
            "data_freshness": 180,             # 3 minutes
            "websocket_disconnection": 120,    # 2 minutes
            "api_error": 180,                  # 3 minutes
            "system_resource": 600,            # 10 minutes
        }
        
        # Hourly alert limits
        self.hourly_limits = {
            "trading_critical": 5,
            "trading_warning": 10,
            "system_info": 3
        }
        
        self.alert_counts: Dict[str, List[datetime]] = {}
        
    def _load_thresholds(self, config_path: str) -> Dict[str, Any]:
        """Load alert thresholds from configuration"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"Alert thresholds loaded from {config_path}")
                return config
        except FileNotFoundError:
            logger.warning("Alert thresholds config not found, using defaults")
            return self._get_default_thresholds()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in alert thresholds config: {e}")
            logger.warning("Using default thresholds")
            return self._get_default_thresholds()
        except Exception as e:
            logger.error(f"Error loading alert thresholds: {e}")
            return self._get_default_thresholds()
    
    def _get_default_thresholds(self) -> Dict[str, Any]:
        """Default thresholds if config not found"""
        return {
            "TRADING_ALERT_THRESHOLDS": {
                "signal_generation_latency": {"warning": 45.0, "critical": 55.0},
                "notification_latency": {"warning": 1.5, "critical": 2.0},
                "settlement_delay": {"warning": 300.0, "critical": 600.0},
                "data_freshness": {"warning": 120.0, "critical": 300.0},
                "api_error_rate": {"warning": 0.1, "critical": 0.2},
                "websocket_reconnect_failures": {"warning": 5, "critical": 10}
            }
        }
    
    def check_trading_alerts(self, metrics: Dict[str, Any]) -> List[TradingAlert]:
        """Check for trading-specific alerts"""
        alerts = []
        
        try:
            if not metrics:
                self.logger.warning("Empty metrics provided to check_trading_alerts")
                return []
            
            # Signal generation latency
            if "signal_generation_time" in metrics:
                latency = metrics["signal_generation_time"]
                if latency >= self.thresholds["TRADING_ALERT_THRESHOLDS"]["signal_generation_latency"]["critical"]:
                    alerts.append(self._create_alert(
                        "signal_generation_latency_critical",
                        "trading_critical",
                        "Signal Generation",
                        f"Signal generation time critical: {latency:.1f}s",
                        {"latency": latency, "threshold": 55.0}
                    ))
                elif latency >= self.thresholds["TRADING_ALERT_THRESHOLDS"]["signal_generation_latency"]["warning"]:
                    alerts.append(self._create_alert(
                        "signal_generation_latency_warning", 
                        "trading_warning",
                        "Signal Generation",
                        f"Signal generation time high: {latency:.1f}s",
                        {"latency": latency, "threshold": 45.0}
                    ))
            
            # Notification latency
            if "notification_latency" in metrics:
                latency = metrics["notification_latency"]
                if latency >= self.thresholds["TRADING_ALERT_THRESHOLDS"]["notification_latency"]["critical"]:
                    alerts.append(self._create_alert(
                        "notification_latency_critical",
                        "trading_critical", 
                        "Notification",
                        f"Notification latency critical: {latency:.2f}s",
                        {"latency": latency, "threshold": 2.0}
                    ))
            
            # Data freshness
            if "last_data_update" in metrics:
                freshness = time.time() - metrics["last_data_update"]
                if freshness >= self.thresholds["TRADING_ALERT_THRESHOLDS"]["data_freshness"]["critical"]:
                    alerts.append(self._create_alert(
                        "data_freshness_critical",
                        "trading_critical",
                        "Data Feed",
                        f"Data freshness critical: {freshness:.0f}s old",
                        {"freshness": freshness, "threshold": 300.0}
                    ))
            
            # WebSocket connection health
            if "websocket_status" in metrics:
                status = metrics["websocket_status"]
                if status.get("failed_connections", 0) >= self.thresholds["TRADING_ALERT_THRESHOLDS"]["websocket_reconnect_failures"]["critical"]:
                    alerts.append(self._create_alert(
                        "websocket_critical",
                        "trading_critical",
                        "WebSocket",
                        f"WebSocket connection critical: {status['failed_connections']} failures",
                        status
                    ))
            
            # Settlement health
            if "settlement_status" in metrics:
                status = metrics["settlement_status"]
                if status.get("pending_settlements", 0) > 5:
                    alerts.append(self._create_alert(
                        "settlement_backlog",
                        "trading_warning",
                        "Settlement",
                        f"Settlement backlog: {status['pending_settlements']} pending",
                        status
                    ))
        
        except Exception as e:
            self.logger.error(f"Error in check_trading_alerts: {e}")
            return []
        
        # Filter out None alerts that might have been suppressed
        valid_alerts = [alert for alert in alerts if alert is not None]
        return valid_alerts
    
    def check_system_alerts(self, metrics: Dict[str, Any]) -> List[TradingAlert]:
        """Check system alerts with higher thresholds"""
        alerts = []
        
        try:
            if not metrics:
                self.logger.warning("Empty metrics provided to check_system_alerts")
                return []
            
            # Only check critical system resources that impact trading
            if "system_metrics" in metrics:
                sys_metrics = metrics["system_metrics"]
                
                # CPU - only critical if sustained high usage
                if sys_metrics.get("cpu_percent", 0) >= 90:
                    alerts.append(self._create_alert(
                        "cpu_critical",
                        "system_info",
                        "System",
                        f"CPU usage critical: {sys_metrics['cpu_percent']:.1f}%",
                        sys_metrics
                    ))
                
                # Memory - only critical if very high
                if sys_metrics.get("memory_percent", 0) >= 95:
                    alerts.append(self._create_alert(
                        "memory_critical", 
                        "system_info",
                        "System",
                        f"Memory usage critical: {sys_metrics['memory_percent']:.1f}%",
                        sys_metrics
                    ))
                
                # Disk - only critical if nearly full
                if sys_metrics.get("disk_usage_percent", 0) >= 95:
                    alerts.append(self._create_alert(
                        "disk_critical",
                        "system_info", 
                        "System",
                        f"Disk usage critical: {sys_metrics['disk_usage_percent']:.1f}%",
                        sys_metrics
                    ))
        
        except Exception as e:
            self.logger.error(f"Error in check_system_alerts: {e}")
            return []
        
        # Filter out None alerts that might have been suppressed
        valid_alerts = [alert for alert in alerts if alert is not None]
        return valid_alerts
    
    def _create_alert(self, alert_type: str, severity: str, component: str, 
                     message: str, details: Dict[str, Any]) -> Optional[TradingAlert]:
        """Create alert with suppression check"""
        timestamp = datetime.now()
        
        # Check suppression
        if self._is_suppressed(alert_type, timestamp):
            return None
        
        # Check hourly limits
        if not self._check_hourly_limit(severity, timestamp):
            return None
        
        # Create alert
        alert_id = f"{alert_type}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        alert = TradingAlert(
            alert_id=alert_id,
            alert_type=alert_type,
            severity=severity,
            component=component,
            message=message,
            details=details,
            timestamp=timestamp
        )
        
        # Store suppression rule
        window = self.suppression_windows.get(alert_type, 300)
        self.suppression_rules[alert_type] = timestamp + timedelta(seconds=window)
        
        # Add to active alerts
        self.active_alerts[alert_id] = alert
        
        # Add to history
        self.alert_history.append(alert)
        
        # Limit history size
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
        
        logger.warning(f"Alert created: {alert_id} - {message}")
        return alert
    
    def _is_suppressed(self, alert_type: str, timestamp: datetime) -> bool:
        """Check if alert is suppressed"""
        if alert_type in self.suppression_rules:
            if timestamp < self.suppression_rules[alert_type]:
                return True
        
        return False
    
    def _check_hourly_limit(self, severity: str, timestamp: datetime) -> bool:
        """Check hourly alert limits"""
        if severity not in self.alert_counts:
            self.alert_counts[severity] = []
        
        # Clean old alerts (older than 1 hour)
        hour_ago = timestamp - timedelta(hours=1)
        self.alert_counts[severity] = [
            alert_time for alert_time in self.alert_counts[severity] 
            if alert_time > hour_ago
        ]
        
        # Check limit
        limit = self.hourly_limits.get(severity, 10)
        if len(self.alert_counts[severity]) >= limit:
            logger.warning(f"Hourly alert limit reached for {severity}")
            return False
        
        # Add current alert
        self.alert_counts[severity].append(timestamp)
        return True
    
    def resolve_alert(self, alert_id: str):
        """Resolve an alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            # Remove from active alerts
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert resolved: {alert_id}")
    
    def get_active_alerts(self) -> List[TradingAlert]:
        """Get active alerts sorted by severity"""
        severity_order = {"trading_critical": 0, "trading_warning": 1, "system_info": 2}
        return sorted(
            self.active_alerts.values(),
            key=lambda x: severity_order.get(x.severity, 3)
        )
    
    def get_alert_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get alert summary for specified period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_alerts = [
            alert for alert in self.alert_history 
            if alert.timestamp >= cutoff_time
        ]
        
        # Count by severity
        severity_counts = {}
        for alert in recent_alerts:
            severity_counts[alert.severity] = severity_counts.get(alert.severity, 0) + 1
        
        # Count by component
        component_counts = {}
        for alert in recent_alerts:
            component_counts[alert.component] = component_counts.get(alert.component, 0) + 1
        
        return {
            "period_hours": hours,
            "total_alerts": len(recent_alerts),
            "active_alerts": len(self.active_alerts),
            "severity_breakdown": severity_counts,
            "component_breakdown": component_counts,
            "most_active_component": max(component_counts.items(), key=lambda x: x[1])[0] if component_counts else None
        }
    
    def cleanup_old_suppressions(self):
        """Clean expired suppression rules"""
        now = datetime.now()
        expired_rules = [
            rule_type for rule_type, expiry_time in self.suppression_rules.items()
            if expiry_time <= now
        ]
        
        for rule_type in expired_rules:
            del self.suppression_rules[rule_type]
        
        if expired_rules:
            logger.debug(f"Cleaned {len(expired_rules)} expired suppression rules")


# Global trading alert manager instance
trading_alert_manager = TradingAlertManager()