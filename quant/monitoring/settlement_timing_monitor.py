"""
结算时机监控组件
监控和优化结算时机的准确性
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class SettlementTimingMonitor:
    """结算时机监控类"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = db_path
        self.target_settlement_time = 10  # 目标结算时间（分钟）
        self.tolerance = 1  # 允许的时间误差（分钟）
    
    def check_settlement_timing(self) -> Dict[str, Any]:
        """检查结算时机准确性"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查最近的结算时机
            cursor.execute("""
                SELECT id, signal_timestamp, exit_timestamp,
                       (julianday(exit_timestamp) - julianday(signal_timestamp)) * 24 * 60 as time_diff
                FROM trade_history 
                WHERE status IN ('WIN', 'LOSS') 
                AND exit_timestamp IS NOT NULL
                AND signal_timestamp > datetime('now', '-24 hours')
                ORDER BY signal_timestamp DESC
            """)
            
            recent_settlements = cursor.fetchall()
            
            timing_issues = []
            for trade in recent_settlements:
                time_diff = trade['time_diff']
                if abs(time_diff - self.target_settlement_time) > self.tolerance:
                    timing_issues.append({
                        'trade_id': trade['id'],
                        'expected_time': self.target_settlement_time,
                        'actual_time': time_diff,
                        'deviation': time_diff - self.target_settlement_time
                    })
            
            conn.close()
            
            return {
                'total_settlements': len(recent_settlements),
                'timing_issues': len(timing_issues),
                'accuracy_rate': (len(recent_settlements) - len(timing_issues)) / len(recent_settlements) * 100 if recent_settlements else 0,
                'issues': timing_issues
            }
            
        except Exception as e:
            logger.error(f"检查结算时机失败: {e}")
            return {'error': str(e)}
    
    def get_overdue_trades(self) -> List[Dict[str, Any]]:
        """获取超时未结算的交易"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cutoff_time = (datetime.utcnow() - timedelta(minutes=15)).isoformat()
            cursor.execute("""
                SELECT * FROM trade_history 
                WHERE status = 'PENDING' 
                AND signal_timestamp < ?
                ORDER BY signal_timestamp
            """, (cutoff_time,))
            
            overdue_trades = [dict(trade) for trade in cursor.fetchall()]
            conn.close()
            
            return overdue_trades
            
        except Exception as e:
            logger.error(f"获取超时交易失败: {e}")
            return []
    
    def generate_timing_report(self) -> str:
        """生成结算时机报告"""
        timing_check = self.check_settlement_timing()
        overdue_trades = self.get_overdue_trades()
        
        report = f"""
结算时机监控报告
================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

最近24小时结算统计:
- 总结算数: {timing_check.get('total_settlements', 0)}
- 时机异常数: {timing_check.get('timing_issues', 0)}
- 时机准确率: {timing_check.get('accuracy_rate', 0):.1f}%

当前超时未结算交易: {len(overdue_trades)}

建议措施:
1. 优化结算时机控制逻辑
2. 实现超时交易自动处理
3. 增加结算监控告警
"""
        return report
