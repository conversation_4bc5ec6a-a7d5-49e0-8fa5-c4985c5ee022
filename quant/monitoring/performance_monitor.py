"""
Performance Monitor
"""

import json
import threading
import time
from collections.abc import Callable
from datetime import datetime, timedelta
from typing import Any

import pandas as pd
import psutil


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, config: dict[str, Any] = None):
        self.config = config or {}
        self.monitor_interval = config.get("monitor_interval", 5) if config else 5  # 秒
        self.max_history_size = (
            config.get("max_history_size", 10000) if config else 10000
        )

        # 监控数据
        self.performance_data = {
            "cpu": [],
            "memory": [],
            "disk": [],
            "network": [],
            "custom": [],
        }

        # 自定义监控项
        self.custom_monitors = {}

        # 状态
        self.is_running = False
        self.monitor_thread = None

        # 告警阈值
        self.thresholds = {
            "cpu_percent": 80,
            "memory_percent": 80,
            "disk_percent": 80,
            "network_bandwidth": 1000000,  # 1MB/s
        }

        # 告警回调
        self.alert_callbacks = []

    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                timestamp = datetime.now()

                # 收集系统性能数据
                self._collect_system_performance(timestamp)

                # 收集自定义监控数据
                self._collect_custom_performance(timestamp)

                # 检查告警
                self._check_alerts(timestamp)

                # 限制历史数据大小
                self._limit_history_size()

                time.sleep(self.monitor_interval)

            except Exception as e:
                print(f"Error in performance monitoring: {e}")
                time.sleep(self.monitor_interval)

    def _collect_system_performance(self, timestamp: datetime):
        """收集系统性能数据"""
        # CPU性能
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()

        cpu_data = {
            "timestamp": timestamp,
            "cpu_percent": cpu_percent,
            "cpu_count": cpu_count,
            "cpu_freq_current": cpu_freq.current if cpu_freq else 0,
            "cpu_freq_max": cpu_freq.max if cpu_freq else 0,
        }

        self.performance_data["cpu"].append(cpu_data)

        # 内存性能
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        memory_data = {
            "timestamp": timestamp,
            "memory_percent": memory.percent,
            "memory_used": memory.used,
            "memory_total": memory.total,
            "memory_available": memory.available,
            "swap_percent": swap.percent,
            "swap_used": swap.used,
            "swap_total": swap.total,
        }

        self.performance_data["memory"].append(memory_data)

        # 磁盘性能
        disk_io = psutil.disk_io_counters()
        disk_usage = psutil.disk_usage("/")

        disk_data = {
            "timestamp": timestamp,
            "disk_read_bytes": disk_io.read_bytes if disk_io else 0,
            "disk_write_bytes": disk_io.write_bytes if disk_io else 0,
            "disk_read_count": disk_io.read_count if disk_io else 0,
            "disk_write_count": disk_io.write_count if disk_io else 0,
            "disk_percent": disk_usage.percent,
            "disk_used": disk_usage.used,
            "disk_total": disk_usage.total,
            "disk_free": disk_usage.free,
        }

        self.performance_data["disk"].append(disk_data)

        # 网络性能
        network_io = psutil.net_io_counters()

        network_data = {
            "timestamp": timestamp,
            "network_bytes_sent": network_io.bytes_sent,
            "network_bytes_recv": network_io.bytes_recv,
            "network_packets_sent": network_io.packets_sent,
            "network_packets_recv": network_io.packets_recv,
            "network_errin": network_io.errin,
            "network_errout": network_io.errout,
            "network_dropin": network_io.dropin,
            "network_dropout": network_io.dropout,
        }

        self.performance_data["network"].append(network_data)

    def _collect_custom_performance(self, timestamp: datetime):
        """收集自定义监控数据"""
        for monitor_name, monitor_info in self.custom_monitors.items():
            try:
                if monitor_info["enabled"]:
                    result = monitor_info["func"](**monitor_info["kwargs"])

                    custom_data = {
                        "timestamp": timestamp,
                        "monitor_name": monitor_name,
                        "value": result.get("value", 0),
                        "unit": result.get("unit", ""),
                        "status": result.get("status", "ok"),
                        "message": result.get("message", ""),
                    }

                    self.performance_data["custom"].append(custom_data)

            except Exception as e:
                print(f"Error in custom monitor {monitor_name}: {e}")

    def _check_alerts(self, timestamp: datetime):
        """检查告警"""
        # 获取最新的性能数据
        alerts = []

        # CPU告警
        if self.performance_data["cpu"]:
            latest_cpu = self.performance_data["cpu"][-1]
            if latest_cpu["cpu_percent"] > self.thresholds["cpu_percent"]:
                alerts.append(
                    {
                        "type": "cpu",
                        "level": "warning",
                        "message": f"CPU usage is {latest_cpu['cpu_percent']:.1f}%",
                        "timestamp": timestamp,
                        "value": latest_cpu["cpu_percent"],
                    }
                )

        # 内存告警
        if self.performance_data["memory"]:
            latest_memory = self.performance_data["memory"][-1]
            if latest_memory["memory_percent"] > self.thresholds["memory_percent"]:
                alerts.append(
                    {
                        "type": "memory",
                        "level": "warning",
                        "message": f"Memory usage is {latest_memory['memory_percent']:.1f}%",
                        "timestamp": timestamp,
                        "value": latest_memory["memory_percent"],
                    }
                )

        # 磁盘告警
        if self.performance_data["disk"]:
            latest_disk = self.performance_data["disk"][-1]
            if latest_disk["disk_percent"] > self.thresholds["disk_percent"]:
                alerts.append(
                    {
                        "type": "disk",
                        "level": "warning",
                        "message": f"Disk usage is {latest_disk['disk_percent']:.1f}%",
                        "timestamp": timestamp,
                        "value": latest_disk["disk_percent"],
                    }
                )

        # 触发告警回调
        for alert in alerts:
            self._trigger_alert(alert)

    def _trigger_alert(self, alert: dict[str, Any]):
        """触发告警"""
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                print(f"Error in alert callback: {e}")

    def _limit_history_size(self):
        """限制历史数据大小"""
        for key in self.performance_data:
            if len(self.performance_data[key]) > self.max_history_size:
                self.performance_data[key] = self.performance_data[key][
                    -self.max_history_size :
                ]

    def add_custom_monitor(
        self, name: str, monitor_func: Callable, interval: int = 60, **kwargs
    ):
        """添加自定义监控项"""
        self.custom_monitors[name] = {
            "func": monitor_func,
            "interval": interval,
            "kwargs": kwargs,
            "enabled": True,
            "last_run": None,
        }

    def remove_custom_monitor(self, name: str):
        """移除自定义监控项"""
        if name in self.custom_monitors:
            del self.custom_monitors[name]

    def enable_custom_monitor(self, name: str):
        """启用自定义监控项"""
        if name in self.custom_monitors:
            self.custom_monitors[name]["enabled"] = True

    def disable_custom_monitor(self, name: str):
        """禁用自定义监控项"""
        if name in self.custom_monitors:
            self.custom_monitors[name]["enabled"] = False

    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)

    def remove_alert_callback(self, callback: Callable):
        """移除告警回调"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)

    def set_threshold(self, metric: str, value: float):
        """设置告警阈值"""
        self.thresholds[metric] = value

    def get_performance_data(self, metric_type: str, hours: int = 24) -> pd.DataFrame:
        """获取性能数据"""
        if metric_type not in self.performance_data:
            return pd.DataFrame()

        cutoff_time = datetime.now() - timedelta(hours=hours)

        data = [
            record
            for record in self.performance_data[metric_type]
            if record["timestamp"] >= cutoff_time
        ]

        if not data:
            return pd.DataFrame()

        return pd.DataFrame(data).set_index("timestamp")

    def get_performance_summary(self, hours: int = 24) -> dict[str, Any]:
        """获取性能摘要"""
        summary = {"timestamp": datetime.now(), "period_hours": hours, "metrics": {}}

        # CPU摘要
        if self.performance_data["cpu"]:
            cpu_data = self.get_performance_data("cpu", hours)
            if not cpu_data.empty:
                summary["metrics"]["cpu"] = {
                    "avg_percent": cpu_data["cpu_percent"].mean(),
                    "max_percent": cpu_data["cpu_percent"].max(),
                    "min_percent": cpu_data["cpu_percent"].min(),
                    "current_percent": cpu_data["cpu_percent"].iloc[-1],
                }

        # 内存摘要
        if self.performance_data["memory"]:
            memory_data = self.get_performance_data("memory", hours)
            if not memory_data.empty:
                summary["metrics"]["memory"] = {
                    "avg_percent": memory_data["memory_percent"].mean(),
                    "max_percent": memory_data["memory_percent"].max(),
                    "min_percent": memory_data["memory_percent"].min(),
                    "current_percent": memory_data["memory_percent"].iloc[-1],
                }

        # 磁盘摘要
        if self.performance_data["disk"]:
            disk_data = self.get_performance_data("disk", hours)
            if not disk_data.empty:
                # 计算磁盘IO速率
                if len(disk_data) > 1:
                    time_diff = (
                        disk_data.index[-1] - disk_data.index[0]
                    ).total_seconds()
                    read_rate = (
                        disk_data["disk_read_bytes"].iloc[-1]
                        - disk_data["disk_read_bytes"].iloc[0]
                    ) / time_diff
                    write_rate = (
                        disk_data["disk_write_bytes"].iloc[-1]
                        - disk_data["disk_write_bytes"].iloc[0]
                    ) / time_diff
                else:
                    read_rate = 0
                    write_rate = 0

                summary["metrics"]["disk"] = {
                    "usage_percent": disk_data["disk_percent"].iloc[-1],
                    "read_rate_bytes_per_sec": read_rate,
                    "write_rate_bytes_per_sec": write_rate,
                    "total_bytes_read": disk_data["disk_read_bytes"].iloc[-1],
                    "total_bytes_written": disk_data["disk_write_bytes"].iloc[-1],
                }

        # 网络摘要
        if self.performance_data["network"]:
            network_data = self.get_performance_data("network", hours)
            if not network_data.empty:
                # 计算网络IO速率
                if len(network_data) > 1:
                    time_diff = (
                        network_data.index[-1] - network_data.index[0]
                    ).total_seconds()
                    sent_rate = (
                        network_data["network_bytes_sent"].iloc[-1]
                        - network_data["network_bytes_sent"].iloc[0]
                    ) / time_diff
                    recv_rate = (
                        network_data["network_bytes_recv"].iloc[-1]
                        - network_data["network_bytes_recv"].iloc[0]
                    ) / time_diff
                else:
                    sent_rate = 0
                    recv_rate = 0

                summary["metrics"]["network"] = {
                    "sent_rate_bytes_per_sec": sent_rate,
                    "recv_rate_bytes_per_sec": recv_rate,
                    "total_bytes_sent": network_data["network_bytes_sent"].iloc[-1],
                    "total_bytes_recv": network_data["network_bytes_recv"].iloc[-1],
                }

        return summary

    def export_performance_data(self, filepath: str, hours: int = 24):
        """导出性能数据"""
        export_data = {
            "export_timestamp": datetime.now(),
            "period_hours": hours,
            "data": {},
        }

        for metric_type in self.performance_data:
            df = self.get_performance_data(metric_type, hours)
            if not df.empty:
                export_data["data"][metric_type] = df.to_dict("records")

        with open(filepath, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

    def get_custom_monitor_status(self) -> dict[str, Any]:
        """获取自定义监控状态"""
        status = {}

        for monitor_name, monitor_info in self.custom_monitors.items():
            status[monitor_name] = {
                "enabled": monitor_info["enabled"],
                "interval": monitor_info["interval"],
                "last_run": monitor_info["last_run"],
            }

        return status

    def reset(self):
        """重置性能监控器"""
        self.stop_monitoring()

        # 清空性能数据
        for key in self.performance_data:
            self.performance_data[key].clear()

        # 清空自定义监控
        self.custom_monitors.clear()

        # 清空告警回调
        self.alert_callbacks.clear()

    def get_status(self) -> dict[str, Any]:
        """获取监控状态"""
        return {
            "is_running": self.is_running,
            "monitor_interval": self.monitor_interval,
            "custom_monitors_count": len(self.custom_monitors),
            "alert_callbacks_count": len(self.alert_callbacks),
            "data_points": {
                metric_type: len(data)
                for metric_type, data in self.performance_data.items()
            },
        }
