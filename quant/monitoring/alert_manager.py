"""
Alert Manager
"""

import json
import logging
import smtplib
import threading
import time
from collections.abc import Callable
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from enum import Enum
from typing import Any

import requests


class AlertLevel(Enum):
    """告警级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""

    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class AlertManager:
    """告警管理器"""

    def __init__(self, config: dict[str, Any] = None):
        self.config = config or {}

        # 告警规则
        self.alert_rules = {}

        # 活跃告警
        self.active_alerts = {}

        # 告警历史
        self.alert_history = []
        self.max_history_size = (
            config.get("max_history_size", 10000) if config else 10000
        )

        # 通知渠道
        self.notification_channels = {
            "email": EmailNotifier(config.get("email", {})),
            "webhook": WebhookNotifier(config.get("webhook", {})),
            "slack": SlackNotifier(config.get("slack", {})),
            "log": LogNotifier(config.get("log", {})),
        }

        # 告警抑制
        self.suppression_rules = {}

        # 告警聚合
        self.aggregation_rules = {}

        # 状态
        self.is_running = False
        self.check_thread = None
        self.check_interval = config.get("check_interval", 60) if config else 60  # 秒

        # 日志
        self.logger = logging.getLogger(__name__)

    def add_alert_rule(
        self,
        name: str,
        condition_func: Callable,
        level: AlertLevel = AlertLevel.WARNING,
        description: str = "",
        notification_channels: list[str] = None,
        suppression_window: int = 300,  # 5分钟
        aggregation_window: int = 3600,  # 1小时
        **kwargs,
    ):
        """
        添加告警规则

        Args:
            name: 规则名称
            condition_func: 条件函数
            level: 告警级别
            description: 描述
            notification_channels: 通知渠道
            suppression_window: 抑制窗口（秒）
            aggregation_window: 聚合窗口（秒）
            **kwargs: 其他参数
        """
        self.alert_rules[name] = {
            "condition_func": condition_func,
            "level": level,
            "description": description,
            "notification_channels": notification_channels or ["log"],
            "suppression_window": suppression_window,
            "aggregation_window": aggregation_window,
            "enabled": True,
            "kwargs": kwargs,
            "last_triggered": None,
            "trigger_count": 0,
        }

    def remove_alert_rule(self, name: str):
        """移除告警规则"""
        if name in self.alert_rules:
            del self.alert_rules[name]

    def enable_alert_rule(self, name: str):
        """启用告警规则"""
        if name in self.alert_rules:
            self.alert_rules[name]["enabled"] = True

    def disable_alert_rule(self, name: str):
        """禁用告警规则"""
        if name in self.alert_rules:
            self.alert_rules[name]["enabled"] = False

    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            return

        self.is_running = True
        self.check_thread = threading.Thread(target=self._monitoring_loop)
        self.check_thread.daemon = True
        self.check_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.check_thread:
            self.check_thread.join(timeout=5)

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                self._check_all_alerts()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Error in alert monitoring: {e}")
                time.sleep(self.check_interval)

    def _check_all_alerts(self):
        """检查所有告警规则"""
        timestamp = datetime.now()

        for rule_name, rule_info in self.alert_rules.items():
            if not rule_info["enabled"]:
                continue

            try:
                # 检查告警条件
                result = rule_info["condition_func"](**rule_info["kwargs"])

                if result["triggered"]:
                    # 检查是否被抑制
                    if self._is_suppressed(rule_name, timestamp):
                        continue

                    # 检查是否需要聚合
                    aggregated_alert = self._should_aggregate(
                        rule_name, result, timestamp
                    )

                    if aggregated_alert:
                        # 更新聚合告警
                        self._update_aggregated_alert(
                            rule_name, aggregated_alert, timestamp
                        )
                    else:
                        # 创建新告警
                        self._create_alert(rule_name, result, timestamp)

            except Exception as e:
                self.logger.error(f"Error checking alert rule {rule_name}: {e}")

    def _is_suppressed(self, rule_name: str, timestamp: datetime) -> bool:
        """检查告警是否被抑制"""
        rule_info = self.alert_rules[rule_name]

        # 检查时间抑制
        if rule_info["last_triggered"]:
            time_since_last = (timestamp - rule_info["last_triggered"]).total_seconds()
            if time_since_last < rule_info["suppression_window"]:
                return True

        # 检查抑制规则
        if rule_name in self.suppression_rules:
            suppression_rule = self.suppression_rules[rule_name]
            if (
                suppression_rule["active"]
                and timestamp < suppression_rule["expires_at"]
            ):
                return True

        return False

    def _should_aggregate(
        self, rule_name: str, result: dict[str, Any], timestamp: datetime
    ) -> dict[str, Any] | None:
        """检查是否应该聚合告警"""
        rule_info = self.alert_rules[rule_name]

        # 查找活跃告警
        for alert_id, alert in self.active_alerts.items():
            if (
                alert["rule_name"] == rule_name
                and alert["status"] == AlertStatus.ACTIVE
                and (timestamp - alert["created_at"]).total_seconds()
                < rule_info["aggregation_window"]
            ):

                # 可以聚合
                return alert

        return None

    def _update_aggregated_alert(
        self, rule_name: str, alert: dict[str, Any], timestamp: datetime
    ):
        """更新聚合告警"""
        alert["last_triggered"] = timestamp
        alert["trigger_count"] += 1
        alert["updated_at"] = timestamp

        # 更新规则触发时间
        self.alert_rules[rule_name]["last_triggered"] = timestamp
        self.alert_rules[rule_name]["trigger_count"] += 1

    def _create_alert(
        self, rule_name: str, result: dict[str, Any], timestamp: datetime
    ):
        """创建告警"""
        rule_info = self.alert_rules[rule_name]

        # 生成告警ID
        alert_id = f"{rule_name}_{timestamp.strftime('%Y%m%d_%H%M%S')}_{hash(str(result)) % 10000:04d}"

        # 创建告警
        alert = {
            "alert_id": alert_id,
            "rule_name": rule_name,
            "level": rule_info["level"],
            "description": rule_info["description"],
            "message": result.get("message", ""),
            "details": result.get("details", {}),
            "status": AlertStatus.ACTIVE,
            "created_at": timestamp,
            "updated_at": timestamp,
            "last_triggered": timestamp,
            "trigger_count": 1,
            "acknowledged_by": None,
            "acknowledged_at": None,
            "resolved_at": None,
            "resolved_by": None,
            "tags": result.get("tags", []),
        }

        # 添加到活跃告警
        self.active_alerts[alert_id] = alert

        # 添加到历史记录
        self.alert_history.append(alert.copy())

        # 更新规则触发时间
        self.alert_rules[rule_name]["last_triggered"] = timestamp
        self.alert_rules[rule_name]["trigger_count"] += 1

        # 发送通知
        self._send_notifications(alert, rule_info["notification_channels"])

        # 限制历史记录大小
        if len(self.alert_history) > self.max_history_size:
            self.alert_history = self.alert_history[-self.max_history_size :]

        self.logger.info(f"Alert created: {alert_id} - {rule_name}")

    def _send_notifications(self, alert: dict[str, Any], channels: list[str]):
        """发送通知"""
        for channel_name in channels:
            if channel_name in self.notification_channels:
                try:
                    notifier = self.notification_channels[channel_name]
                    notifier.send_notification(alert)
                except Exception as e:
                    self.logger.error(
                        f"Error sending notification via {channel_name}: {e}"
                    )

    def acknowledge_alert(self, alert_id: str, acknowledged_by: str):
        """确认告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert["status"] = AlertStatus.ACKNOWLEDGED
            alert["acknowledged_by"] = acknowledged_by
            alert["acknowledged_at"] = datetime.now()
            alert["updated_at"] = datetime.now()

            self.logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")

    def resolve_alert(self, alert_id: str, resolved_by: str):
        """解决告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert["status"] = AlertStatus.RESOLVED
            alert["resolved_by"] = resolved_by
            alert["resolved_at"] = datetime.now()
            alert["updated_at"] = datetime.now()

            # 从活跃告警中移除
            del self.active_alerts[alert_id]

            self.logger.info(f"Alert resolved: {alert_id} by {resolved_by}")

    def suppress_alert(self, rule_name: str, duration_minutes: int, reason: str = ""):
        """抑制告警"""
        expires_at = datetime.now() + timedelta(minutes=duration_minutes)

        self.suppression_rules[rule_name] = {
            "active": True,
            "expires_at": expires_at,
            "reason": reason,
            "created_at": datetime.now(),
        }

        self.logger.info(f"Alert rule {rule_name} suppressed until {expires_at}")

    def get_active_alerts(self) -> list[dict[str, Any]]:
        """获取活跃告警"""
        return list(self.active_alerts.values())

    def get_alert_history(self, hours: int = 24) -> list[dict[str, Any]]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            alert for alert in self.alert_history if alert["created_at"] >= cutoff_time
        ]

    def get_alert_statistics(self, hours: int = 24) -> dict[str, Any]:
        """获取告警统计"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_alerts = [
            alert for alert in self.alert_history if alert["created_at"] >= cutoff_time
        ]

        if not recent_alerts:
            return {}

        # 按级别统计
        level_counts = {}
        for alert in recent_alerts:
            level = alert["level"]
            level_counts[level] = level_counts.get(level, 0) + 1

        # 按规则统计
        rule_counts = {}
        for alert in recent_alerts:
            rule = alert["rule_name"]
            rule_counts[rule] = rule_counts.get(rule, 0) + 1

        # 按状态统计
        status_counts = {}
        for alert in recent_alerts:
            status = alert["status"]
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            "total_alerts": len(recent_alerts),
            "level_distribution": level_counts,
            "rule_distribution": rule_counts,
            "status_distribution": status_counts,
            "active_alerts_count": len(self.active_alerts),
            "period_hours": hours,
        }

    def get_alert_rules_status(self) -> dict[str, Any]:
        """获取告警规则状态"""
        status = {}

        for rule_name, rule_info in self.alert_rules.items():
            status[rule_name] = {
                "enabled": rule_info["enabled"],
                "level": rule_info["level"],
                "last_triggered": rule_info["last_triggered"],
                "trigger_count": rule_info["trigger_count"],
                "suppression_window": rule_info["suppression_window"],
                "aggregation_window": rule_info["aggregation_window"],
            }

        return status

    def export_alerts(self, filepath: str, hours: int = 24):
        """导出告警数据"""
        export_data = {
            "export_timestamp": datetime.now(),
            "period_hours": hours,
            "active_alerts": list(self.active_alerts.values()),
            "alert_history": self.get_alert_history(hours),
            "alert_rules": self.get_alert_rules_status(),
        }

        with open(filepath, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

    def reset(self):
        """重置告警管理器"""
        self.stop_monitoring()
        self.active_alerts.clear()
        self.alert_history.clear()
        self.suppression_rules.clear()
        self.alert_rules.clear()


class BaseNotifier:
    """通知器基类"""

    def __init__(self, config: dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", True)

    def send_notification(self, alert: dict[str, Any]):
        """发送通知"""
        if not self.enabled:
            return

        try:
            self._send(alert)
        except Exception as e:
            print(f"Error sending notification: {e}")

    def _send(self, alert: dict[str, Any]):
        """发送通知的具体实现"""
        raise NotImplementedError


class EmailNotifier(BaseNotifier):
    """邮件通知器"""

    def __init__(self, config: dict[str, Any]):
        super().__init__(config)
        self.smtp_server = config.get("smtp_server", "smtp.gmail.com")
        self.smtp_port = config.get("smtp_port", 587)
        self.username = config.get("username", "")
        self.password = config.get("password", "")
        self.from_email = config.get("from_email", self.username)
        self.to_emails = config.get("to_emails", [])

    def _send(self, alert: dict[str, Any]):
        """发送邮件通知"""
        if not self.to_emails:
            return

        # 创建邮件
        msg = MIMEMultipart()
        msg["From"] = self.from_email
        msg["To"] = ", ".join(self.to_emails)
        msg["Subject"] = f"[{alert['level'].upper()}] {alert['rule_name']}"

        # 邮件内容
        body = f"""
        Alert: {alert['rule_name']}
        Level: {alert['level']}
        Description: {alert['description']}
        Message: {alert['message']}
        Time: {alert['created_at']}
        
        Details:
        {json.dumps(alert['details'], indent=2)}
        """

        msg.attach(MIMEText(body, "plain"))

        # 发送邮件
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            server.starttls()
            server.login(self.username, self.password)
            server.send_message(msg)


class WebhookNotifier(BaseNotifier):
    """Webhook通知器"""

    def __init__(self, config: dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get("webhook_url", "")
        self.headers = config.get("headers", {"Content-Type": "application/json"})
        self.timeout = config.get("timeout", 30)

    def _send(self, alert: dict[str, Any]):
        """发送Webhook通知"""
        if not self.webhook_url:
            return

        # 准备数据
        payload = {
            "alert_id": alert["alert_id"],
            "rule_name": alert["rule_name"],
            "level": alert["level"].value,
            "description": alert["description"],
            "message": alert["message"],
            "details": alert["details"],
            "created_at": alert["created_at"].isoformat(),
            "status": alert["status"].value,
        }

        # 发送请求
        response = requests.post(
            self.webhook_url, json=payload, headers=self.headers, timeout=self.timeout
        )

        response.raise_for_status()


class SlackNotifier(BaseNotifier):
    """Slack通知器"""

    def __init__(self, config: dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get("webhook_url", "")
        self.channel = config.get("channel", "#alerts")
        self.username = config.get("username", "AlertBot")

    def _send(self, alert: dict[str, Any]):
        """发送Slack通知"""
        if not self.webhook_url:
            return

        # 准备消息
        color = {
            AlertLevel.INFO: "good",
            AlertLevel.WARNING: "warning",
            AlertLevel.ERROR: "danger",
            AlertLevel.CRITICAL: "danger",
        }.get(alert["level"], "warning")

        payload = {
            "channel": self.channel,
            "username": self.username,
            "attachments": [
                {
                    "color": color,
                    "title": f"[{alert['level'].upper()}] {alert['rule_name']}",
                    "text": alert["message"],
                    "fields": [
                        {
                            "title": "Description",
                            "value": alert["description"],
                            "short": True,
                        },
                        {
                            "title": "Time",
                            "value": alert["created_at"].strftime("%Y-%m-%d %H:%M:%S"),
                            "short": True,
                        },
                    ],
                    "footer": "Alert Manager",
                    "ts": int(alert["created_at"].timestamp()),
                }
            ],
        }

        # 发送请求
        response = requests.post(self.webhook_url, json=payload)
        response.raise_for_status()


class LogNotifier(BaseNotifier):
    """日志通知器"""

    def __init__(self, config: dict[str, Any]):
        super().__init__(config)
        self.logger = logging.getLogger("alerts")

    def _send(self, alert: dict[str, Any]):
        """发送日志通知"""
        level_map = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL,
        }

        log_level = level_map.get(alert["level"], logging.WARNING)

        message = f"Alert: {alert['rule_name']} - {alert['message']}"

        self.logger.log(log_level, message, extra={"alert": alert})
