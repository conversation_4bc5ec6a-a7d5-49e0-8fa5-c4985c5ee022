"""
Health Checker
"""

import socket
import threading
import time
from collections.abc import Callable
from datetime import datetime, timedelta
from enum import Enum
from typing import Any

import psutil
import requests


class HealthStatus(Enum):
    """健康状态枚举"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class HealthChecker:
    """健康检查器"""

    def __init__(self, config: dict[str, Any] = None):
        self.config = config or {}
        self.check_interval = config.get("check_interval", 60) if config else 60  # 秒
        self.timeout = config.get("timeout", 10) if config else 10  # 秒

        # 检查项
        self.checks = {}
        self.check_results = {}
        self.is_running = False
        self.check_thread = None

        # 历史记录
        self.health_history = []
        self.max_history_size = config.get("max_history_size", 1000) if config else 1000

        # 注册默认检查项
        self._register_default_checks()

    def _register_default_checks(self):
        """注册默认检查项"""
        # 系统资源检查
        self.register_check(
            "cpu_usage", self._check_cpu_usage, HealthStatus.WARNING, 80
        )
        self.register_check(
            "memory_usage", self._check_memory_usage, HealthStatus.WARNING, 80
        )
        self.register_check(
            "disk_usage", self._check_disk_usage, HealthStatus.WARNING, 80
        )

        # 网络连接检查
        self.register_check(
            "network_connectivity",
            self._check_network_connectivity,
            HealthStatus.CRITICAL,
        )

        # 数据连接检查
        self.register_check(
            "database_connection",
            self._check_database_connection,
            HealthStatus.CRITICAL,
        )

        # API服务检查
        self.register_check(
            "api_service", self._check_api_service, HealthStatus.WARNING
        )

    def register_check(
        self,
        name: str,
        check_func: Callable,
        failure_status: HealthStatus = HealthStatus.WARNING,
        **kwargs,
    ):
        """
        注册检查项

        Args:
            name: 检查项名称
            check_func: 检查函数
            failure_status: 失败状态
            **kwargs: 检查参数
        """
        self.checks[name] = {
            "func": check_func,
            "failure_status": failure_status,
            "kwargs": kwargs,
            "last_check": None,
            "enabled": True,
        }

    def unregister_check(self, name: str):
        """取消注册检查项"""
        if name in self.checks:
            del self.checks[name]

    def enable_check(self, name: str):
        """启用检查项"""
        if name in self.checks:
            self.checks[name]["enabled"] = True

    def disable_check(self, name: str):
        """禁用检查项"""
        if name in self.checks:
            self.checks[name]["enabled"] = False

    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            return

        self.is_running = True
        self.check_thread = threading.Thread(target=self._monitoring_loop)
        self.check_thread.daemon = True
        self.check_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.check_thread:
            self.check_thread.join(timeout=5)

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                self._run_all_checks()
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"Error in health check monitoring: {e}")
                time.sleep(self.check_interval)

    def _run_all_checks(self):
        """运行所有检查"""
        timestamp = datetime.now()
        overall_status = HealthStatus.HEALTHY

        for check_name, check_info in self.checks.items():
            if not check_info["enabled"]:
                continue

            try:
                # 执行检查
                result = check_info["func"](**check_info["kwargs"])

                # 更新检查结果
                check_result = {
                    "name": check_name,
                    "timestamp": timestamp,
                    "status": result["status"],
                    "message": result.get("message", ""),
                    "details": result.get("details", {}),
                    "response_time": result.get("response_time", 0),
                }

                self.check_results[check_name] = check_result

                # 更新整体状态
                if result["status"] == HealthStatus.CRITICAL:
                    overall_status = HealthStatus.CRITICAL
                elif (
                    result["status"] == HealthStatus.WARNING
                    and overall_status == HealthStatus.HEALTHY
                ):
                    overall_status = HealthStatus.WARNING

            except Exception as e:
                # 检查失败
                check_result = {
                    "name": check_name,
                    "timestamp": timestamp,
                    "status": check_info["failure_status"],
                    "message": f"Check failed: {str(e)}",
                    "details": {"error": str(e)},
                    "response_time": 0,
                }

                self.check_results[check_name] = check_result

                if check_info["failure_status"] == HealthStatus.CRITICAL:
                    overall_status = HealthStatus.CRITICAL

        # 记录健康历史
        health_record = {
            "timestamp": timestamp,
            "overall_status": overall_status,
            "checks": self.check_results.copy(),
        }

        self.health_history.append(health_record)

        # 限制历史记录大小
        if len(self.health_history) > self.max_history_size:
            self.health_history = self.health_history[-self.max_history_size :]

    def run_check(self, name: str) -> dict[str, Any] | None:
        """运行单个检查"""
        if name not in self.checks:
            return None

        check_info = self.checks[name]
        try:
            result = check_info["func"](**check_info["kwargs"])

            return {
                "name": name,
                "timestamp": datetime.now(),
                "status": result["status"],
                "message": result.get("message", ""),
                "details": result.get("details", {}),
                "response_time": result.get("response_time", 0),
            }

        except Exception as e:
            return {
                "name": name,
                "timestamp": datetime.now(),
                "status": check_info["failure_status"],
                "message": f"Check failed: {str(e)}",
                "details": {"error": str(e)},
                "response_time": 0,
            }

    def get_health_status(self) -> dict[str, Any]:
        """获取健康状态"""
        if not self.check_results:
            return {
                "overall_status": HealthStatus.UNKNOWN,
                "timestamp": datetime.now(),
                "checks": {},
            }

        # 确定整体状态
        overall_status = HealthStatus.HEALTHY
        for result in self.check_results.values():
            if result["status"] == HealthStatus.CRITICAL:
                overall_status = HealthStatus.CRITICAL
            elif (
                result["status"] == HealthStatus.WARNING
                and overall_status == HealthStatus.HEALTHY
            ):
                overall_status = HealthStatus.WARNING

        return {
            "overall_status": overall_status,
            "timestamp": datetime.now(),
            "checks": self.check_results.copy(),
        }

    def get_health_history(self, hours: int = 24) -> list[dict[str, Any]]:
        """获取健康历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            record
            for record in self.health_history
            if record["timestamp"] >= cutoff_time
        ]

    def get_check_statistics(self) -> dict[str, Any]:
        """获取检查统计"""
        if not self.health_history:
            return {}

        stats = {}

        for check_name in self.checks.keys():
            check_history = [
                record
                for record in self.health_history
                if check_name in record["checks"]
            ]

            if check_history:
                status_counts = {}
                for record in check_history:
                    status = record["checks"][check_name]["status"]
                    status_counts[status] = status_counts.get(status, 0) + 1

                stats[check_name] = {
                    "total_checks": len(check_history),
                    "status_counts": status_counts,
                    "uptime_percentage": status_counts.get(HealthStatus.HEALTHY, 0)
                    / len(check_history)
                    * 100,
                    "last_check": check_history[-1]["checks"][check_name]["timestamp"],
                }

        return stats

    # 检查函数
    def _check_cpu_usage(self, threshold: float = 80) -> dict[str, Any]:
        """检查CPU使用率"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)

            if cpu_percent > threshold:
                return {
                    "status": HealthStatus.WARNING,
                    "message": f"CPU usage is high: {cpu_percent:.1f}%",
                    "details": {"cpu_percent": cpu_percent, "threshold": threshold},
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"CPU usage is normal: {cpu_percent:.1f}%",
                    "details": {"cpu_percent": cpu_percent, "threshold": threshold},
                }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Failed to check CPU usage: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_memory_usage(self, threshold: float = 80) -> dict[str, Any]:
        """检查内存使用率"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            if memory_percent > threshold:
                return {
                    "status": HealthStatus.WARNING,
                    "message": f"Memory usage is high: {memory_percent:.1f}%",
                    "details": {
                        "memory_percent": memory_percent,
                        "available_memory": memory.available,
                        "total_memory": memory.total,
                        "threshold": threshold,
                    },
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"Memory usage is normal: {memory_percent:.1f}%",
                    "details": {
                        "memory_percent": memory_percent,
                        "available_memory": memory.available,
                        "total_memory": memory.total,
                        "threshold": threshold,
                    },
                }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Failed to check memory usage: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_disk_usage(
        self, threshold: float = 80, path: str = "/"
    ) -> dict[str, Any]:
        """检查磁盘使用率"""
        try:
            disk = psutil.disk_usage(path)
            disk_percent = disk.percent

            if disk_percent > threshold:
                return {
                    "status": HealthStatus.WARNING,
                    "message": f"Disk usage is high: {disk_percent:.1f}%",
                    "details": {
                        "disk_percent": disk_percent,
                        "free_space": disk.free,
                        "total_space": disk.total,
                        "path": path,
                        "threshold": threshold,
                    },
                }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": f"Disk usage is normal: {disk_percent:.1f}%",
                    "details": {
                        "disk_percent": disk_percent,
                        "free_space": disk.free,
                        "total_space": disk.total,
                        "path": path,
                        "threshold": threshold,
                    },
                }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Failed to check disk usage: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_network_connectivity(
        self, host: str = "*******", port: int = 53, timeout: int = 5
    ) -> dict[str, Any]:
        """检查网络连接"""
        try:
            start_time = time.time()
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            result = sock.connect_ex((host, port))
            sock.close()

            response_time = (time.time() - start_time) * 1000

            if result == 0:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "Network connectivity is OK",
                    "details": {
                        "host": host,
                        "port": port,
                        "response_time_ms": response_time,
                    },
                    "response_time": response_time,
                }
            else:
                return {
                    "status": HealthStatus.CRITICAL,
                    "message": "Network connectivity failed",
                    "details": {"host": host, "port": port, "error_code": result},
                    "response_time": response_time,
                }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Network check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_database_connection(
        self, connection_string: str = None
    ) -> dict[str, Any]:
        """检查数据库连接"""
        # 简化的数据库检查
        try:
            # 这里应该根据实际的数据库配置进行检查
            # 为示例目的，返回健康状态
            return {
                "status": HealthStatus.HEALTHY,
                "message": "Database connection is OK",
                "details": {"connection_string": connection_string or "default"},
            }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Database connection failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def _check_api_service(self, url: str = None, timeout: int = 10) -> dict[str, Any]:
        """检查API服务"""
        try:
            start_time = time.time()

            if url:
                response = requests.get(url, timeout=timeout)
                response_time = (time.time() - start_time) * 1000

                if response.status_code == 200:
                    return {
                        "status": HealthStatus.HEALTHY,
                        "message": "API service is OK",
                        "details": {
                            "url": url,
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                        },
                        "response_time": response_time,
                    }
                else:
                    return {
                        "status": HealthStatus.WARNING,
                        "message": f"API service returned {response.status_code}",
                        "details": {
                            "url": url,
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                        },
                        "response_time": response_time,
                    }
            else:
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "API service check skipped (no URL provided)",
                    "details": {},
                }

        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"API service check failed: {str(e)}",
                "details": {"error": str(e)},
            }

    def reset(self):
        """重置健康检查器"""
        self.stop_monitoring()
        self.check_results.clear()
        self.health_history.clear()
