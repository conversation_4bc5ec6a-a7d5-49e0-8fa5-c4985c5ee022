from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class Orderbook:
    """订单薄"""

    platform: str  # 交易平台
    symbol: str  # 交易对
    asks: list[list]  # 买盘数据 [[price, quantity], [...], ...]
    bids: list[list]  # 卖盘数据 [[price, quantity], [...], ...]
    timestamp: int  # 时间戳(毫秒)
    orderbooks: Any  # 原始数据


@dataclass
class Trade:
    """交易数据"""

    platform: str  # 交易平台
    symbol: str  # 交易对
    action: str  # 方向 BUY / SELL
    price: float  # 价格
    quantity: float  # 数量
    timestamp: int  # 时间戳(毫秒)
    trades: Any  # 原始数据


@dataclass
class Kline:
    """K线"""

    platform: str  # 交易平台
    symbol: str  # 交易对
    open: float | list  # 开盘价
    high: float | list  # 最高价
    low: float | list  # 最低价
    close: float | list  # 收盘价
    volume: float | list  # 成交量
    timestamp: float | list  # 时间戳(毫秒)
    interval: str  # 周期
    klines: Any  # 原始数据


@dataclass
class Ticker:
    """Ticker."""

    platform: str  # 平台
    symbol: str  # 交易对
    ask_price: float  # 卖一价格
    ask_quantity: float  # 卖一数量
    bid_price: float  # 买一价格
    bid_quantity: float  # 买一数量
    open: float  # 24小时开盘价
    high: float  # 24小时最高价
    low: float  # 24小时最低价
    close: float  # 最新价格
    volume: float  # 24小时成交量
    turnover: float  # 24小时成交额
    timestamp: int  # 时间戳(毫秒)
    tickers: Any  # 原始数据
