"""
Configuration Management Module

Handles loading and providing access to configuration files.
"""

import json
from pathlib import Path
from typing import Any, Optional


class ConfigManager:
    """Manages application configuration loading and access."""

    def __init__(self, config_path: str = "config.json"):
        self.config_path = Path(config_path)
        self._config: dict[str, Any] | None = None

    def load_config(self) -> dict[str, Any]:
        """Load configuration from JSON file."""
        if self._config is None:
            try:
                with open(self.config_path, encoding="utf-8") as f:
                    self._config = json.load(f)
            except FileNotFoundError:
                raise FileNotFoundError(
                    f"Configuration file not found: {self.config_path}"
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in configuration file: {e}")

        return self._config

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        config = self.load_config()
        keys = key.split(".")
        value = config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_platform_config(self, platform: str) -> dict[str, Any]:
        """Get platform-specific configuration."""
        return self.get(f"PLATFORMS.{platform}", {})

    def get_log_config(self) -> dict[str, Any]:
        """Get logging configuration."""
        return self.get("LOG", {})

    def get_dingtalk_config(self) -> str:
        """Get DingTalk webhook URL."""
        return self.get("DINGTALK", "")

    def get_proxy_config(self) -> str | None:
        """Get proxy configuration."""
        return self.get("PROXY")


# Global config instance
config = ConfigManager()
