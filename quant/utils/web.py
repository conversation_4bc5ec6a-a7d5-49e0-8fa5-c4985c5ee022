"""
Websockets Client.

Author: <PERSON><PERSON><PERSON><PERSON>
Email:  <EMAIL>
Date:   2022-04-07
"""

import json
import ssl
import threading

import websocket

from quant.config import config
from quant.quant import Quant
from quant.utils import logger
from quant.utils.decorator import method_locker
from quant.utils.ssl_config import ssl_config


class Websockets:

    def __init__(
        self,
        url,
        connected_callback=None,
        process_callback=None,
        process_binary_callback=None,
    ):
        """Websockets connection.

        Attributes:
                url: Websockets connection url.
                connected_callback: Callback function will be called after connected to Websockets server successfully.
                process_callback: Callback function will be called if any stream data receive from Websockets
                        connection, this function only callback `text/json` message. e.g.
                process_binary_callback: Callback function will be called if any stream data receive from Websockets
                        connection, this function only callback `binary` message. e.g.
        """
        self._url = url
        self._ws = None
        self._connected_callback = connected_callback
        self._process_callback = process_callback
        self._process_binary_callback = process_binary_callback

        threading.Thread(target=self._connect).start()

    @property
    def ws(self):
        return self._ws

    def dis_connect(self):
        """Disconnect websockets connection."""
        self._ws.close()

    def _on_error(self, error):
        logger.error("websockets ERROR:", error, caller=self)

    def _on_close(self):
        logger.warning("websockets CLOSED ...", caller=self)
        Quant.create_single_task(self.reconnect)

    def _on_open(self):
        if self._connected_callback:
            Quant.create_single_task(self._connected_callback)

    def _connect(self):
        logger.info("url:", self._url, caller=self)

        try:
            self._ws = websocket.WebSocketApp(
                url=self._url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open,
            )

            # Configure SSL options using centralized SSL config
            sslopt = ssl_config.get_websocket_sslopt()

            if config.proxy:
                proxy_host = config.proxy.split(":")[0]
                proxy_port = int(config.proxy.split(":")[1])
                self.ws.run_forever(
                    http_proxy_host=proxy_host,
                    http_proxy_port=proxy_port,
                    sslopt=sslopt
                )
            else:
                self.ws.run_forever(sslopt=sslopt)

        except websocket.WebSocketException:
            logger.error(
                "connect to Websockets server error! url:", self._url, caller=self
            )

    @method_locker(name="websockets_reconnect.lock")
    def reconnect(self):
        """Re-connect to Websockets server."""
        logger.warning("reconnecting to Websockets server right now!", caller=self)
        self.ws.close()
        self._connect()

    def _on_message(self, msg):
        if type(msg) == str:
            if self._process_callback:
                try:
                    data = json.loads(msg)
                except json.decoder.JSONDecodeError:
                    data = msg
                Quant.create_single_task(self._process_callback, data)
        elif type(msg) == bytes:
            if self._process_binary_callback:
                Quant.create_single_task(self._process_binary_callback, msg)

    def send(self, data):
        """Send message to Websockets server.

        Args:
                data: Message content, must be dict or string.
        Returns:
                If send successfully, return True, otherwise return False.
        """
        if not self.ws:
            logger.warning("Websockets connection not connected yet!", caller=self)
            return False
        if isinstance(data, dict):
            self.ws.send(json.dumps(data))
        elif isinstance(data, str):
            self.ws.send(data)
        else:
            logger.error("send message failed:", data, caller=self)
            return False
        logger.debug("send message:", data, caller=self)
        return True
