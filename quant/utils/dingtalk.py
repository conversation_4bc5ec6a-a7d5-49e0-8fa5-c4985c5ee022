"""
DingTalk Message Tool.

Author: <PERSON><PERSON><PERSON>tel
Email:  <EMAIL>
Date:   2022-02-19
"""

from quant.asset import Asset
from quant.config import config
from quant.order import Order
from quant.position import Position
from quant.utils import tools
from quant.utils.http_client import HttpRequests


class Dingtalk:

    @classmethod
    def text(cls, text, token: str = None):
        """Send text message."""
        params = {
            "msgtype": "text",
            "at": {"atMobiles": [""], "isAtAll": False},
            "text": {"content": "交易提醒:" + text},
        }
        headers = {"Content-Type": "application/json;charset=utf-8"}
        url = token or config.dingtalk
        try:
            response = HttpRequests.post(url, json=params, headers=headers).json()
            errcode = response.get("errcode")
            errmsg = response.get("errmsg")
            if errcode != 0:
                return None, errmsg
            return response, None
        except Exception as e:
            return None, str(e)

    @classmethod
    def markdown(cls, content, token: str = None):
        """Send markdown message."""
        url = token or config.dingtalk
        headers = {"Content-Type": "application/json"}
        params = {
            "msgtype": "markdown",
            "markdown": {"title": "交易提醒", "text": content},
        }
        try:
            response = HttpRequests.post(url, json=params, headers=headers).json()
            errcode = response.get("errcode")
            errmsg = response.get("errmsg")
            if errcode != 0:
                return None, errmsg
            return response, None
        except Exception as e:
            return None, str(e)

    @classmethod
    def send_order_message(cls, order: Order, token: str = None):
        """Send order message."""

        content = (
            "### 订单更新推送\n\n"
            f"> **交易平台:** {order.platform}\n\n"
            f"> **交易币对:** {order.symbol}\n\n"
            f"> **订单方向:** {order.action}\n\n"
            f"> **订单类型:** {order.order_type}\n\n"
            f"> **订单编号:** {order.order_no}\n\n"
            f"> **订单状态:** {order.status}\n\n"
            f"> **剩余数量:** {order.remain}\n\n"
            f"> **委托数量:** {order.quantity}\n\n"
            f"> **委托均价:** {order.price}\n\n"
            f"> **成交数量:** {order.filled_qty}\n\n"
            f"> **成交均价:** {order.avg_price}\n\n"
            f"> **手续费用:** {order.fee}\n\n"
            f"> **创建时间:** {tools.ts_to_datetime_str(order.timestamp / 1000)}\n\n"
            f"> **更新时间:** {tools.ts_to_datetime_str(order.utime / 1000)}"
        )

        success, error = cls.markdown(content=content, token=token)
        return success, error

    @classmethod
    def send_asset_message(cls, asset: Asset, token: str = None):
        """Send asset message."""

        content = (
            "### 资产更新推送\n\n"
            f"> **交易平台:** {asset.platform}\n\n"
            f"> **资产名称:** {asset.currency}\n\n"
            f"> **账户总额:** {asset.total}\n\n"
            f"> **冻结金额:** {asset.locked}\n\n"
            f"> **可用余额:** {asset.free}\n\n"
            f"> **更新时间:** {tools.ts_to_datetime_str(asset.timestamp / 1000)}"
        )

        success, error = cls.markdown(content=content, token=token)
        return success, error

    @classmethod
    def send_position_message(cls, position: Position, token: str = None):
        """Send position message."""

        content = (
            "### 持仓更新推送\n\n"
            f"> **交易平台:** {position.platform}\n\n"
            f"> **交易币对:** {position.symbol}\n\n"
            f"> **多头数量:** {position.long_quantity}\n\n"
            f"> **多头均价:** {position.long_avg_price}\n\n"
            f"> **空头数量:** {position.short_quantity}\n\n"
            f"> **空头均价:** {position.short_avg_price}\n\n"
            f"> **更新时间:** {tools.ts_to_datetime_str(position.utime / 1000)}"
        )

        success, error = cls.markdown(content=content, token=token)
        return success, error
