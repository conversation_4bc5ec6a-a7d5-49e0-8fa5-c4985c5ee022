"""
Structured JSON Logger Module

Provides structured JSON logging configuration for the application.
"""

import json
import logging
import sys
import atexit
import queue
from datetime import datetime
from logging.handlers import RotatingFile<PERSON><PERSON><PERSON>, TimedRotatingF<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, QueueListener
from typing import Any
from pathlib import Path
import time
from pathlib import Path
from typing import Any

from quant.config_manager import config


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging.

    保持 JSON 结构不变，同时尽量精简每条日志体积。
    """

    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            # 精简但保留定位信息
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add extra fields if available
        if hasattr(record, "extra") and record.extra:
            # 仅在 extra 为 dict 时合并，避免非 dict 导致序列化变大或报错
            if isinstance(record.extra, dict):
                log_entry.update(record.extra)
            else:
                log_entry["extra"] = str(record.extra)

        # Add exception info if available
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry, ensure_ascii=False)


def _cleanup_old_logs(log_dir: Path, base_stem: str, retention_days: int):
    """按保留天数清理过期日志文件。

    - 针对 TimedRotatingFileHandler 命名（app-YYYY-MM-DD.log）与
      RotatingFileHandler 命名（app.log.1、app.log.2）均适用，按 mtime 删除。
    """
    if retention_days is None or retention_days <= 0:
        return
    try:
        cutoff = time.time() - retention_days * 86400
        for f in log_dir.glob(f"{base_stem}*.log"):
            # 跳过当前主文件（例如 app.log / error.log）
            if f.name == f"{base_stem}.log":
                continue
            try:
                if f.stat().st_mtime < cutoff:
                    f.unlink(missing_ok=True)
            except Exception:
                # 忽略清理异常，避免影响主流程
                pass
    except Exception:
        pass


def setup_logging():
    """Set up logging configuration with configurable rotation and retention.

    Supported LOG config keys (config.json -> LOG):
    - level: INFO|DEBUG|... (default: INFO)
    - path: logs directory (default: ./logs)
    - name: base log filename (default: app.log)
    - console: bool, also log to stdout (default: true)
    - backup_count: int, number of files to keep (default: 7)
    - clear: bool, delete existing *.log files at startup (default: false)
    - rotation_mode: "size" | "time"  (default: "size")
    - max_bytes: int, for size mode (default: 20MB)
    - when: "S"|"M"|"H"|"D"|"W0-6" for time mode (default: "D")
    - interval: int, for time mode (default: 1)
    - utc: bool, use UTC time for time rotation (default: true)
    - async: bool, use QueueHandler/Listener for async logging (default: true)
    - queue_size: int, queue max size for async mode (default: 10000)
    - retention_days: int, auto delete logs older than N days (default: 14)
    """
    log_config = config.get_log_config()

    # Create logs directory if it doesn't exist
    log_path = Path(log_config.get("path", "./logs"))
    log_path.mkdir(parents=True, exist_ok=True)

    # Clear old logs on startup if requested (在创建 handler 之前执行更安全)
    if log_config.get("clear", False):
        for log_file in log_path.glob("*.log"):
            try:
                log_file.unlink()
            except Exception:
                pass

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_config.get("level", "INFO").upper()))

    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Prepare basic params
    base_filename = log_path / log_config.get("name", "app.log")
    base_stem = Path(base_filename).stem  # e.g., app / error

    rotation_mode = (log_config.get("rotation_mode") or "size").lower()
    backup_count = int(log_config.get("backup_count", 7))
    if backup_count <= 0:
        # backup_count=0 会导致不轮转，强制回退到安全默认值
        backup_count = 7

    async_mode = bool(log_config.get("async", True))
    queue_size = int(log_config.get("queue_size", 10000))
    retention_days = int(log_config.get("retention_days", 14))

    # Build file handler
    file_handler: logging.Handler | None = None
    if log_config.get("path"):
        if rotation_mode == "time":
            # Timed rotation
            when = log_config.get("when", "D")
            interval = int(log_config.get("interval", 1))
            utc = bool(log_config.get("utc", True))
            time_handler = TimedRotatingFileHandler(
                filename=str(base_filename),
                when=when,
                interval=interval,
                backupCount=backup_count,
                encoding="utf-8",
                utc=utc,
            )

            # 优化文件名：app-YYYY-MM-DD.log 或按小时 app-YYYY-MM-DD_HH.log
            if when.upper().startswith("H"):
                time_handler.suffix = "%Y-%m-%d_%H"
            else:
                time_handler.suffix = "%Y-%m-%d"

            def _namer(path: str) -> str:
                p = Path(path)
                name = p.name
                token = base_stem + ".log."
                if token in name:
                    date_part = name.split(token, 1)[-1]
                    return str(p.with_name(f"{base_stem}-{date_part}.log"))
                return str(p)

            time_handler.namer = _namer
            file_handler = time_handler
        else:
            # Size rotation (default)
            max_bytes = int(log_config.get("max_bytes", 20 * 1024 * 1024))
            if max_bytes <= 0:
                max_bytes = 20 * 1024 * 1024
            size_handler = RotatingFileHandler(
                filename=str(base_filename),
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding="utf-8",
            )
            file_handler = size_handler

    console_handler: logging.Handler | None = None
    if log_config.get("console", True):
        console_handler = logging.StreamHandler(sys.stdout)

    # Set formatter
    formatter = JSONFormatter()
    if file_handler:
        file_handler.setFormatter(formatter)
    if console_handler:
        console_handler.setFormatter(formatter)

    # Async or direct binding
    if async_mode:
        q: queue.Queue = queue.Queue(maxsize=queue_size)
        queue_handler = QueueHandler(q)
        root_logger.addHandler(queue_handler)

        # 将真实的输出 handler 交给 QueueListener
        handlers = tuple(h for h in [file_handler, console_handler] if h is not None)
        listener = QueueListener(q, *handlers, respect_handler_level=False)
        listener.start()

        # 确保正常退出
        atexit.register(listener.stop)
    else:
        if file_handler:
            root_logger.addHandler(file_handler)
        if console_handler:
            root_logger.addHandler(console_handler)

    # Cleanup old logs by retention days
    _cleanup_old_logs(log_path, base_stem, retention_days)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)


class TradeLogger:
    """Specialized logger for trade events."""

    def __init__(self):
        self.logger = get_logger("trading")

    def log_signal(self, signal_data: dict[str, Any]):
        """Log trading signal generation."""
        self.logger.info("Signal generated", extra={"signal_data": signal_data})

    def log_execution(self, execution_data: dict[str, Any]):
        """Log trade execution."""
        self.logger.info("Trade executed", extra={"execution_data": execution_data})

    def log_settlement(self, settlement_data: dict[str, Any]):
        """Log trade settlement."""
        self.logger.info("Trade settled", extra={"settlement_data": settlement_data})

    def log_error(self, error_data: dict[str, Any]):
        """Log trading errors."""
        self.logger.error("Trading error", extra={"error_data": error_data})


# Legacy functions for backward compatibility
def info(*args, **kwargs):
    """Legacy info function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.info(msg)


def warning(*args, **kwargs):
    """Legacy warning function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.warning(msg)


def debug(*args, **kwargs):
    """Legacy debug function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.debug(msg)


def error(*args, **kwargs):
    """Legacy error function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.error(msg)


def exception(*args, **kwargs):
    """Legacy exception function."""
    logger = get_logger("legacy")
    caller = kwargs.get("caller")
    if caller:
        msg = f"[{caller.__class__.__name__}] {' '.join(str(arg) for arg in args)}"
    else:
        msg = " ".join(str(arg) for arg in args)
    logger.exception(msg)


# Initialize logging on module import
setup_logging()

# Global logger instances
logger = get_logger(__name__)
trade_logger = TradeLogger()
