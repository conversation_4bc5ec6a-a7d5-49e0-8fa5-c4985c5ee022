"""
Data Storage.

Author: <PERSON><PERSON><PERSON><PERSON>
Date:   2020/07/09
"""

import csv

import pymongo

from quant.config import config
from quant.utils import logger

MONGODB_CLIENT = None


def initialize_mongodb_client(
    host: str = "localhost",
    port: int = 27017,
    username: str = None,
    password: str = None,
):
    """initialize_mongodb_client."""
    if config.mongodb:
        global MONGODB_CLIENT
        MONGODB_CLIENT = pymongo.MongoClient(
            host=host, port=port, serverSelectionTimeoutMS=3000
        )
        if username and password:
            MONGODB_CLIENT.admin.authenticate(
                username, password, mechanism="SCRAM-SHA-1"
            )
            logger.info("create mongodb connection pool.")


def show_current_db_connections():
    """show_current_db_connections."""
    db = MONGODB_CLIENT.db
    conn = db.command("serverStatus")["connections"]["current"]
    return conn


def mongodb_save(database, collection, data):
    """保存数据至mongodb
    :param database: 数据库名称
    :param collection: 集合名称
    :param data: 要存储的数据，字典格式
    """
    db = MONGODB_CLIENT[database]
    col = db[collection]
    col.insert_one(data)


def mongodb_read_data(database, collection):
    """读取mongodb数据库中某集合中的所有数据，并保存至一个列表中
    :param database: 数据库名称
    :param collection: 集合名称
    """
    db = MONGODB_CLIENT[database]
    col = db[collection]
    datalist = []
    for item in col.find():
        datalist.append([item])
    return datalist


def delete_mongodb_database(database):
    """删除mongodb的数据库
    :param database: 数据库名称
    """
    db = MONGODB_CLIENT[database]
    db.command("dropDatabase")


def delete_mongodb_collection(database, collection):
    """删除mongodb的集合
    :param database: 数据库名称
    :param collection: 集合名称
    """
    db = MONGODB_CLIENT[database]
    col = db[collection]
    col.drop()


def txt_save(content, filename, mode="a"):
    """
    保存数据至txt文件。
    :param content: 要保存的内容,必须为string格式
    :param filename:文件路径及名称
    :param mode:
    :return:
    """
    with open(filename, mode=mode, encoding="utf-8") as file:
        file.write(content + "\n")


def txt_read(filename):
    """
    读取txt文件中的数据。
    :param filename: 文件路径、文件名称。
    :return:返回一个包含所有文件内容的列表，其中元素均为string格式
    """
    with open(filename, encoding="utf-8") as file:
        content = file.readlines()
    for i in range(len(content)):
        content[i] = content[i][: len(content[i]) - 1]
        file.close()
    return content


def save_to_csv_file(data, path):
    """保存数据至csv文件
    :param data: 数据
    :param path: 路径
    """
    with open(path, mode="a", newline="", encoding="utf-8") as f:
        csv_writer = csv.writer(f)
        csv_writer.writerow(data)


def read_csv_file(path):
    """读取csv文件
    :param path: 路径
    """
    data = []
    reader = csv.reader(open(path, encoding="utf-8"))
    for l in reader:
        data.append(l)
    return data
