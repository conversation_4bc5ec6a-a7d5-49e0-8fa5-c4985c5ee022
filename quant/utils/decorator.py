"""
Multi threading locker decorator.

Author: <PERSON><PERSON><PERSON><PERSON>
Date:   2020/02/26
"""

import functools
import threading

METHOD_LOCKERS = {}


def method_locker(name, wait=True):
    """In order to share memory between different threads, we should use locker to lock our method,
        so that we can avoid some un-prediction actions.

    Args:
        name: locker name.
        wait: If waiting to be executed when the locker is locked? if True, waiting until to be executed, else return
              immediately (do not execute).
    """
    assert isinstance(name, str)

    def decorating_function(method):
        global METHOD_LOCKERS
        locker = METHOD_LOCKERS.get(name)
        if not locker:
            locker = threading.Lock()
            METHOD_LOCKERS[name] = locker

        @functools.wraps(method)
        def wrapper(*args, **kwargs):
            if not wait and locker.locked():
                return
            try:
                locker.acquire()
                return method(*args, **kwargs)
            finally:
                locker.release()

        return wrapper

    return decorating_function
