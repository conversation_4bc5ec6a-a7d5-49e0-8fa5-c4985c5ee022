"""
SSL Configuration Manager

Provides centralized SSL certificate verification configuration
for all network connections in the trading system.

Author: Claude
Date: 2025-08-04
"""

import os
import ssl
from typing import Optional

from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SSLConfigManager:
    """Manages SSL configuration for all network connections."""
    
    def __init__(self):
        self._ssl_context_cache = {}
    
    def should_disable_ssl_verification(self) -> bool:
        """Determine if SSL verification should be disabled."""
        return (
            os.getenv("ENVIRONMENT") == "development" or
            config.get("DEBUG", False) or
            os.getenv("DISABLE_SSL_VERIFY", "false").lower() == "true"
        )
    
    def get_ssl_context(self, purpose: str = "default") -> ssl.SSLContext:
        """Get configured SSL context for the given purpose."""
        cache_key = f"{purpose}_{self.should_disable_ssl_verification()}"
        
        if cache_key in self._ssl_context_cache:
            return self._ssl_context_cache[cache_key]
        
        # Create new SSL context
        if purpose == "default":
            ssl_context = ssl.create_default_context()
        elif purpose == "websocket":
            ssl_context = ssl.create_default_context()
        else:
            ssl_context = ssl.create_default_context()
        
        # Configure based on environment
        if self.should_disable_ssl_verification():
            logger.warning(f"SSL certificate verification disabled for {purpose}")
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
        else:
            logger.info(f"SSL certificate verification enabled for {purpose}")
            ssl_context.check_hostname = True
            ssl_context.verify_mode = ssl.CERT_REQUIRED
        
        # Cache the context
        self._ssl_context_cache[cache_key] = ssl_context
        return ssl_context
    
    def get_websocket_sslopt(self) -> dict:
        """Get SSL options for websocket-client library."""
        if self.should_disable_ssl_verification():
            logger.warning("SSL certificate verification disabled for websocket-client")
            return {
                "cert_reqs": ssl.CERT_NONE,
                "check_hostname": False
            }
        else:
            logger.info("SSL certificate verification enabled for websocket-client")
            return {
                "cert_reqs": ssl.CERT_REQUIRED,
                "check_hostname": True
            }
    
    def get_requests_ssl_config(self) -> dict:
        """Get SSL configuration for requests library."""
        if self.should_disable_ssl_verification():
            logger.warning("SSL certificate verification disabled for requests")
            return {
                "verify": False  # This disables SSL verification in requests
            }
        else:
            logger.info("SSL certificate verification enabled for requests")
            return {
                "verify": True  # This enables SSL verification in requests
            }
    
    def clear_cache(self):
        """Clear SSL context cache."""
        self._ssl_context_cache.clear()


# Global instance
ssl_config = SSLConfigManager()