"""
Analysis Engine Module

The brain of the trading system - implements all strategy logic.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Optional

import pandas as pd
import pandas_ta as ta

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.utils.logger import get_logger, trade_logger

logger = get_logger(__name__)


class MarketState(Enum):
    """Market state enumeration."""

    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"


class SignalDirection(Enum):
    """Signal direction enumeration."""

    LONG = "LONG"
    SHORT = "SHORT"


class AnalysisEngine:
    """Main analysis engine for trading signal generation."""

    def __init__(self):
        self.symbol = "BTCUSDT"
        self.confidence_threshold = 0.7
        self.min_bet_amount = 5
        self.max_bet_amount = 50

    async def analyze_market(self) -> dict[str, Any] | None:
        """Perform complete market analysis and generate signal if conditions are met."""
        try:
            # Get kline data
            klines = await binance_client.get_klines(interval="30m", limit=100)

            # Convert to DataFrame
            df = self._klines_to_dataframe(klines)

            # Calculate indicators
            df = self._calculate_indicators(df)

            # Analyze market state
            market_state = self._analyze_market_state(df)

            # Generate signal if conditions are met
            signal = self._generate_signal(df, market_state)

            if signal:
                # Save signal to database
                signal_id = db.save_trade_signal(signal)
                trade_logger.log_signal(signal)
                logger.info(
                    f"Signal generated: {signal['direction']} with confidence {signal['confidence_score']}"
                )

                return signal

            return None

        except Exception as e:
            logger.error(f"Error in market analysis: {e}")
            return None

    def _klines_to_dataframe(self, klines: list) -> pd.DataFrame:
        """Convert Binance klines to pandas DataFrame."""
        df = pd.DataFrame(
            klines,
            columns=[
                "timestamp",
                "open",
                "high",
                "low",
                "close",
                "volume",
                "close_time",
                "quote_asset_volume",
                "number_of_trades",
                "taker_buy_base_asset_volume",
                "taker_buy_quote_asset_volume",
                "ignore",
            ],
        )

        # Convert to numeric types
        numeric_columns = ["open", "high", "low", "close", "volume"]
        df[numeric_columns] = df[numeric_columns].astype(float)

        # Convert timestamp
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

        return df

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators."""
        # Moving averages
        df["sma_20"] = ta.sma(df["close"], length=20)
        df["sma_50"] = ta.sma(df["close"], length=50)
        df["ema_12"] = ta.ema(df["close"], length=12)
        df["ema_26"] = ta.ema(df["close"], length=26)

        # RSI
        df["rsi"] = ta.rsi(df["close"], length=14)

        # MACD
        macd = ta.macd(df["close"], fast=12, slow=26, signal=9)
        df["macd"] = macd["MACD_12_26_9"]
        df["macd_signal"] = macd["MACDs_12_26_9"]
        df["macd_histogram"] = macd["MACDh_12_26_9"]

        # Bollinger Bands
        bbands = ta.bbands(df["close"], length=20, std=2)
        df["bb_upper"] = bbands["BBU_20_2.0"]
        df["bb_middle"] = bbands["BBM_20_2.0"]
        df["bb_lower"] = bbands["BBL_20_2.0"]

        # Volume indicators
        df["volume_sma"] = ta.sma(df["volume"], length=20)

        return df

    def _analyze_market_state(self, df: pd.DataFrame) -> MarketState:
        """Analyze current market state."""
        latest = df.iloc[-1]

        # Trend analysis
        sma_trend = latest["sma_20"] > latest["sma_50"]
        price_above_sma = latest["close"] > latest["sma_20"]

        # Volatility analysis
        bb_width = (latest["bb_upper"] - latest["bb_lower"]) / latest["bb_middle"]

        # RSI analysis
        rsi_overbought = latest["rsi"] > 70
        rsi_oversold = latest["rsi"] < 30

        if sma_trend and price_above_sma and bb_width < 0.1:
            return MarketState.TRENDING_UP
        elif not sma_trend and not price_above_sma and bb_width < 0.1:
            return MarketState.TRENDING_DOWN
        elif bb_width > 0.15:
            return MarketState.VOLATILE
        else:
            return MarketState.RANGING

    def _generate_signal(
        self, df: pd.DataFrame, market_state: MarketState
    ) -> dict[str, Any] | None:
        """Generate trading signal based on analysis."""
        latest = df.iloc[-1]
        prev = df.iloc[-2]

        # Signal conditions
        long_conditions = []
        short_conditions = []

        # Trend following conditions
        if market_state == MarketState.TRENDING_UP:
            long_conditions.append("trend_following")
        elif market_state == MarketState.TRENDING_DOWN:
            short_conditions.append("trend_following")

        # RSI conditions
        if latest["rsi"] < 30 and prev["rsi"] >= 30:
            long_conditions.append("rsi_oversold")
        elif latest["rsi"] > 70 and prev["rsi"] <= 70:
            short_conditions.append("rsi_overbought")

        # MACD conditions
        if (
            latest["macd"] > latest["macd_signal"]
            and prev["macd"] <= prev["macd_signal"]
        ):
            long_conditions.append("macd_bullish")
        elif (
            latest["macd"] < latest["macd_signal"]
            and prev["macd"] >= prev["macd_signal"]
        ):
            short_conditions.append("macd_bearish")

        # Bollinger Bands conditions
        if latest["close"] <= latest["bb_lower"]:
            long_conditions.append("bb_lower_touch")
        elif latest["close"] >= latest["bb_upper"]:
            short_conditions.append("bb_upper_touch")

        # Determine signal direction and confidence
        signal_data = None

        if len(long_conditions) >= 2:
            confidence = min(len(long_conditions) * 0.25, 1.0)
            if confidence >= self.confidence_threshold:
                signal_data = {
                    "signal_timestamp": datetime.utcnow().isoformat() + "Z",
                    "symbol": self.symbol,
                    "direction": SignalDirection.LONG.value,
                    "entry_price": latest["close"],
                    "confidence_score": confidence,
                    "market_state": market_state.value,
                    "trigger_pattern": self._identify_pattern(df),
                    "confirmed_indicators": long_conditions,
                    "suggested_bet": self._calculate_bet_amount(confidence),
                    "decision_details": self._build_decision_details(
                        df, market_state, long_conditions
                    ),
                }

        elif len(short_conditions) >= 2:
            confidence = min(len(short_conditions) * 0.25, 1.0)
            if confidence >= self.confidence_threshold:
                signal_data = {
                    "signal_timestamp": datetime.utcnow().isoformat() + "Z",
                    "symbol": self.symbol,
                    "direction": SignalDirection.SHORT.value,
                    "entry_price": latest["close"],
                    "confidence_score": confidence,
                    "market_state": market_state.value,
                    "trigger_pattern": self._identify_pattern(df),
                    "confirmed_indicators": short_conditions,
                    "suggested_bet": self._calculate_bet_amount(confidence),
                    "decision_details": self._build_decision_details(
                        df, market_state, short_conditions
                    ),
                }

        return signal_data

    def _identify_pattern(self, df: pd.DataFrame) -> str:
        """Identify candlestick pattern."""
        latest = df.iloc[-1]

        # Simple pattern detection
        if latest["close"] > latest["open"] and latest["close"] > df.iloc[-2]["close"]:
            return "bullish_engulfing"
        elif (
            latest["close"] < latest["open"] and latest["close"] < df.iloc[-2]["close"]
        ):
            return "bearish_engulfing"
        else:
            return "continuation"

    def _calculate_bet_amount(self, confidence: float) -> float:
        """Calculate suggested bet amount based on confidence.

        Guarantees a strictly positive result for any tradable signal and clamps to
        [min_bet_amount, max_bet_amount].
        """
        # Defensive bounds
        try:
            min_amt = float(self.min_bet_amount)
        except Exception:
            min_amt = 5.0
        try:
            max_amt = float(self.max_bet_amount)
        except Exception:
            max_amt = max(50.0, min_amt)
        if min_amt <= 0:
            min_amt = 5.0
        if max_amt < min_amt:
            max_amt = min_amt

        try:
            c = float(confidence)
        except Exception:
            c = 0.0

        # Tiered mapping with clamping
        if c >= 0.9:
            amt = max_amt
        elif c >= 0.8:
            # Keep legacy 20 USDT but clamp to configured range
            amt = max(min_amt, min(20.0, max_amt))
        else:
            # For any tradable signal under this engine, fallback to min bet
            amt = min_amt

        # Final clamp and round
        amt = max(min_amt, min(amt, max_amt))
        return round(amt, 2)

    def _build_decision_details(
        self, df: pd.DataFrame, market_state: MarketState, conditions: list[str]
    ) -> dict[str, Any]:
        """Build comprehensive decision details."""
        latest = df.iloc[-1]

        return {
            "entry_minute": latest.name if hasattr(latest, "name") else "unknown",
            "market_state": market_state.value,
            "confidence_score": min(len(conditions) * 0.25, 1.0),
            "pattern": self._identify_pattern(df),
            "confirmed_indicators": conditions,
            "price_levels": {
                "current": latest["close"],
                "sma_20": latest["sma_20"],
                "sma_50": latest["sma_50"],
                "bb_upper": latest["bb_upper"],
                "bb_lower": latest["bb_lower"],
            },
            "indicators": {
                "rsi": latest["rsi"],
                "macd": latest["macd"],
                "macd_signal": latest["macd_signal"],
                "volume": latest["volume"],
            },
        }


# Global analysis engine instance
analysis_engine = AnalysisEngine()
