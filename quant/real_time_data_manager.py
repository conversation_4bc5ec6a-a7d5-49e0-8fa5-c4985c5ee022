"""
Real-time Data Stream Manager

Handles Binance WebSocket connections and real-time K-line data processing.
"""

import asyncio
import json
import ssl
import time
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional
import threading

import pandas as pd
import websockets

from quant.config_manager import config
from quant.data.collectors.kline_collector import KlineCollector
from quant.data.storage.timeseries import TimeSeriesStorage
from quant.recovery_manager import recovery_manager, RecoveryPriority
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class WebSocketManager:
    """Manages WebSocket connections for real-time data streaming."""

    def __init__(self):
        self.connections = {}
        self.connection_tasks = {}
        self.subscribers = {}
        self.is_running = False
        self.reconnect_attempts = {}
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5
        self._connection_lock = asyncio.Lock()
        self._recovery_enabled = True

    async def connect(self, stream_url: str, stream_name: str):
        """Connect to a WebSocket stream."""
        async with self._connection_lock:
            if stream_name in self.connections:
                logger.warning(f"Connection {stream_name} already exists")
                return True

            try:
                # Check if we're in development mode - be more strict
                import os
                env_debug = os.getenv("ENVIRONMENT") == "development"
                config_debug = config.get("DEBUG", False)
                
                # Only use mock data if explicitly in development mode
                if env_debug and config_debug:
                    logger.info(f"Using mock WebSocket for {stream_name} in development mode")
                    mock_connection = MockWebSocketConnection(stream_name)
                    self.connections[stream_name] = mock_connection
                    # Start mock connection task
                    self.connection_tasks[stream_name] = asyncio.create_task(
                        self._run_mock_connection(mock_connection, stream_name)
                    )
                    return True

                # Real WebSocket connection
                from quant.utils.ssl_config import ssl_config
                
                # Get configured SSL context
                ssl_context = ssl_config.get_ssl_context("websocket")
                
                websocket = await websockets.connect(
                    stream_url,
                    ping_interval=30,
                    ping_timeout=10,
                    close_timeout=1,
                    ssl=ssl_context
                )
                
                self.connections[stream_name] = websocket
                self.reconnect_attempts[stream_name] = 0
                logger.info(f"Connected to WebSocket stream: {stream_name}")

                # Start listening task
                self.connection_tasks[stream_name] = asyncio.create_task(
                    self._listen_to_stream(websocket, stream_name)
                )
                
                return True

            except Exception as e:
                logger.error(f"Failed to connect to WebSocket {stream_name}: {e}")
                await self._handle_reconnection(stream_name, stream_url)
                return False

    async def _listen_to_stream(self, websocket, stream_name: str):
        """Listen for messages from WebSocket stream."""
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._process_stream_data(data, stream_name)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON from {stream_name}: {e}")
                except Exception as e:
                    logger.error(f"Error processing data from {stream_name}: {e}")

        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"WebSocket connection closed: {stream_name}")
            await self._handle_reconnection(stream_name, self._get_stream_url(stream_name))
        except Exception as e:
            logger.error(f"Error listening to stream {stream_name}: {e}")
        finally:
            # Clean up connection and task
            if stream_name in self.connections:
                del self.connections[stream_name]
            if stream_name in self.connection_tasks:
                del self.connection_tasks[stream_name]

    async def _run_mock_connection(self, mock_connection, stream_name: str):
        """Run mock WebSocket connection."""
        try:
            async for data in mock_connection.data_generator:
                await self._process_stream_data(data, stream_name)
        except Exception as e:
            logger.error(f"Error in mock connection {stream_name}: {e}")
        finally:
            # Clean up connection and task
            if stream_name in self.connections:
                del self.connections[stream_name]
            if stream_name in self.connection_tasks:
                del self.connection_tasks[stream_name]

    async def _process_stream_data(self, data: Dict[str, Any], stream_name: str):
        """Process data received from WebSocket stream."""
        if stream_name in self.subscribers:
            for callback in self.subscribers[stream_name]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Error in subscriber callback for {stream_name}: {e}")

    def subscribe(self, stream_name: str, callback: Callable):
        """Subscribe to a WebSocket stream."""
        if stream_name not in self.subscribers:
            self.subscribers[stream_name] = []
        self.subscribers[stream_name].append(callback)
        logger.info(f"Subscribed to stream: {stream_name}")

    def unsubscribe(self, stream_name: str, callback: Callable):
        """Unsubscribe from a WebSocket stream."""
        if stream_name in self.subscribers:
            try:
                self.subscribers[stream_name].remove(callback)
                logger.info(f"Unsubscribed from stream: {stream_name}")
            except ValueError:
                logger.warning(f"Callback not found in subscribers for {stream_name}")

    async def _handle_reconnection(self, stream_name: str, stream_url: str):
        """Handle WebSocket reconnection with exponential backoff."""
        if stream_name not in self.reconnect_attempts:
            self.reconnect_attempts[stream_name] = 0

        if self.reconnect_attempts[stream_name] < self.max_reconnect_attempts:
            self.reconnect_attempts[stream_name] += 1
            
            # Calculate exponential backoff delay
            base_delay = 2.0
            max_delay = 300.0
            delay = min(base_delay ** self.reconnect_attempts[stream_name], max_delay)
            
            logger.info(f"Attempting to reconnect {stream_name} in {delay:.1f} seconds...")
            
            # Trigger recovery operation if enabled
            if self._recovery_enabled and self.reconnect_attempts[stream_name] >= 2:
                try:
                    await recovery_manager.trigger_recovery(
                        component="websocket",
                        operation_type="reconnection",
                        priority=RecoveryPriority.HIGH,
                        data={
                            "stream_name": stream_name,
                            "stream_url": stream_url,
                            "attempt": self.reconnect_attempts[stream_name],
                            "delay": delay
                        }
                    )
                except Exception as e:
                    logger.error(f"Error triggering recovery operation: {e}")
            
            await asyncio.sleep(delay)
            
            # Attempt reconnection
            try:
                success = await self.connect(stream_url, stream_name)
                if success:
                    self.reconnect_attempts[stream_name] = 0  # Reset counter on success
                    logger.info(f"Successfully reconnected to {stream_name}")
            except Exception as e:
                logger.error(f"Reconnection failed for {stream_name}: {e}")
        else:
            logger.error(f"Max reconnection attempts reached for {stream_name}")
            # Trigger critical recovery operation
            if self._recovery_enabled:
                try:
                    await recovery_manager.trigger_recovery(
                        component="websocket",
                        operation_type="critical_failure",
                        priority=RecoveryPriority.CRITICAL,
                        data={
                            "stream_name": stream_name,
                            "stream_url": stream_url,
                            "failed_attempts": self.reconnect_attempts[stream_name]
                        }
                    )
                except Exception as e:
                    logger.error(f"Error triggering critical recovery: {e}")
            
            # Clean up connection
            if stream_name in self.connections:
                del self.connections[stream_name]

    def _get_stream_url(self, stream_name: str) -> str:
        """Get WebSocket stream URL for given stream name."""
        base_url = "wss://stream.binance.com:9443/ws"
        
        if stream_name.startswith("kline_"):
            symbol = stream_name.replace("kline_", "")
            return f"{base_url}/{symbol.lower()}@kline_1m"
        elif stream_name.startswith("ticker_"):
            symbol = stream_name.replace("ticker_", "")
            return f"{base_url}/{symbol.lower()}@ticker"
        elif stream_name.startswith("depth_"):
            symbol = stream_name.replace("depth_", "")
            return f"{base_url}/{symbol.lower()}@depth"
        else:
            return f"{base_url}/{stream_name}"

    async def close_all(self):
        """Close all WebSocket connections."""
        # Cancel all connection tasks
        for stream_name, task in self.connection_tasks.items():
            try:
                task.cancel()
                logger.info(f"Cancelled connection task: {stream_name}")
            except Exception as e:
                logger.error(f"Error cancelling task {stream_name}: {e}")
        
        # Wait for tasks to complete
        if self.connection_tasks:
            await asyncio.gather(*self.connection_tasks.values(), return_exceptions=True)
        
        # Close all connections
        for stream_name, connection in self.connections.items():
            try:
                if hasattr(connection, 'close'):
                    await connection.close()
                logger.info(f"Closed WebSocket connection: {stream_name}")
            except Exception as e:
                logger.error(f"Error closing WebSocket {stream_name}: {e}")
        
        self.connections.clear()
        self.connection_tasks.clear()
        self.subscribers.clear()
        self.reconnect_attempts.clear()


class MockWebSocketConnection:
    """Mock WebSocket connection for development/testing."""

    def __init__(self, stream_name: str):
        self.stream_name = stream_name
        self.is_running = True
        self.data_generator = self._generate_mock_data()

    async def _generate_mock_data(self):
        """Generate mock data for testing."""
        import random
        
        while self.is_running:
            try:
                # Generate mock K-line data
                if self.stream_name.startswith("kline_"):
                    base_price = 55000 + random.uniform(-2000, 2000)
                    mock_data = {
                        "e": "24hrMiniTicker",
                        "E": int(time.time() * 1000),
                        "s": self.stream_name.replace("kline_", "").upper(),
                        "c": str(base_price),
                        "o": str(base_price + random.uniform(-100, 100)),
                        "h": str(base_price + random.uniform(0, 200)),
                        "l": str(base_price - random.uniform(0, 200)),
                        "v": str(random.uniform(100, 1000)),
                        "q": str(random.uniform(1000000, 10000000))
                    }
                else:
                    mock_data = {
                        "stream": self.stream_name,
                        "data": {
                            "e": "24hrMiniTicker",
                            "E": int(time.time() * 1000),
                            "s": "BTCUSDT",
                            "c": str(55000 + random.uniform(-2000, 2000)),
                            "o": str(55000 + random.uniform(-2000, 2000)),
                            "h": str(55000 + random.uniform(-2000, 2000)),
                            "l": str(55000 + random.uniform(-2000, 2000)),
                            "v": str(random.uniform(100, 1000)),
                            "q": str(random.uniform(1000000, 10000000))
                        }
                    }

                # Simulate network delay
                await asyncio.sleep(random.uniform(0.1, 1.0))
                yield mock_data

            except Exception as e:
                logger.error(f"Error generating mock data: {e}")
                break

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.is_running = False


class RealTimeDataManager:
    """Manages real-time data collection and processing."""

    def __init__(self):
        self.ws_manager = WebSocketManager()
        self.kline_collector = KlineCollector()
        self.storage = TimeSeriesStorage({"storage_path": "./data/market_data"})
        self.active_streams = {}
        self.data_processors = {}
        self.is_running = False
        
        # Performance optimizations
        self._timeframe_lock = asyncio.Lock()
        self._write_buffer = defaultdict(list)
        self._buffer_lock = asyncio.Lock()
        self._batch_size = 100
        self._flush_interval = 5.0  # seconds
        self._last_flush = time.time()
        
        # System metrics
        self._metrics = {
            'messages_processed': 0,
            'messages_failed': 0,
            'storage_operations': 0,
            'last_message_time': None,
            'start_time': datetime.now()
        }
        
        # Start background flush task
        self._flush_task = None

    async def initialize(self):
        """Initialize the real-time data manager."""
        try:
            # Initialize storage
            self.storage.connect()
            
            # Start background flush task
            self._flush_task = asyncio.create_task(self._background_flush())
            
            self.is_running = True
            logger.info("Real-time data manager initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize real-time data manager: {e}")
            return False

    async def start_kline_stream(self, symbol: str, interval: str = "1m"):
        """Start real-time K-line stream for a symbol."""
        stream_name = f"kline_{symbol}_{interval}"
        
        if stream_name in self.active_streams:
            logger.warning(f"Stream {stream_name} is already active")
            return

        try:
            # Subscribe to K-line data
            self.ws_manager.subscribe(stream_name, self._process_kline_data)
            
            # Start WebSocket connection
            stream_url = self.ws_manager._get_stream_url(stream_name)
            await self.ws_manager.connect(stream_url, stream_name)
            
            self.active_streams[stream_name] = {
                "symbol": symbol,
                "interval": interval,
                "start_time": datetime.now(),
                "data_count": 0
            }
            
            logger.info(f"Started K-line stream for {symbol} ({interval})")
            
        except Exception as e:
            logger.error(f"Failed to start K-line stream for {symbol}: {e}")

    async def _process_kline_data(self, data: Dict[str, Any]):
        """Process real-time K-line data."""
        try:
            # Update metrics
            self._metrics['messages_processed'] += 1
            self._metrics['last_message_time'] = datetime.now()
            
            # Extract K-line information
            if "k" in data:  # Full K-line format
                kline = data["k"]
                symbol = kline["s"]
                interval = kline["i"]
                is_closed = kline["x"]  # Is the K-line closed
                
                kline_data = {
                    "timestamp": datetime.fromtimestamp(kline["t"] / 1000),
                    "open": float(kline["o"]),
                    "high": float(kline["h"]),
                    "low": float(kline["l"]),
                    "close": float(kline["c"]),
                    "volume": float(kline["v"]),
                    "close_time": datetime.fromtimestamp(kline["T"] / 1000),
                    "quote_volume": float(kline["q"]),
                    "count": int(kline["n"]),
                    "taker_buy_volume": float(kline["V"]),
                    "taker_buy_quote_volume": float(kline["Q"]),
                    "ignore": 0
                }
                
            else:  # Mini ticker format
                symbol = data.get("s", "BTCUSDT")
                interval = "1m"
                is_closed = True
                
                current_price = float(data.get("c", 55000))
                kline_data = {
                    "timestamp": datetime.fromtimestamp(data.get("E", time.time()) / 1000),
                    "open": current_price,
                    "high": current_price,
                    "low": current_price,
                    "close": current_price,
                    "volume": float(data.get("v", 100)),
                    "close_time": datetime.fromtimestamp(data.get("E", time.time()) / 1000),
                    "quote_volume": float(data.get("q", 10000)),
                    "count": 1,
                    "taker_buy_volume": float(data.get("v", 100)) * 0.6,
                    "taker_buy_quote_volume": float(data.get("q", 10000)) * 0.6,
                    "ignore": 0
                }

            # Add to batch buffer instead of immediate storage
            table_name = f"kline_{symbol.lower()}_{interval}"
            await self._buffer_data(table_name, kline_data)

            # Update stream statistics
            stream_key = f"kline_{symbol}_{interval}"
            if stream_key in self.active_streams:
                self.active_streams[stream_key]["data_count"] += 1

            # Process with data processors
            if is_closed:
                await self._process_with_timeframes(symbol, interval, kline_data)

        except Exception as e:
            self._metrics['messages_failed'] += 1
            logger.error(f"Error processing K-line data: {e}")
            
            # Enhanced error handling with recovery
            if self._metrics['messages_failed'] % 10 == 0:  # Every 10 failures
                logger.warning(f"High error rate detected: {self._metrics['messages_failed']} failures")
                # Attempt recovery by flushing buffer
                try:
                    await self._flush_buffer()
                    logger.info("Recovery attempt: Buffer flushed")
                except Exception as recovery_error:
                    logger.error(f"Recovery failed: {recovery_error}")

    async def _process_with_timeframes(self, symbol: str, interval: str, kline_data: Dict[str, Any]):
        """Process data across multiple timeframes."""
        try:
            # Define timeframe hierarchy
            timeframe_hierarchy = {
                "1m": ["5m", "15m", "30m", "1h", "4h", "1d"],
                "5m": ["15m", "30m", "1h", "4h", "1d"],
                "15m": ["30m", "1h", "4h", "1d"],
                "30m": ["1h", "4h", "1d"],
                "1h": ["4h", "1d"],
                "4h": ["1d"]
            }

            if interval in timeframe_hierarchy:
                async with self._timeframe_lock:
                    for target_interval in timeframe_hierarchy[interval]:
                        await self._update_timeframe_data(symbol, target_interval, kline_data)

        except Exception as e:
            logger.error(f"Error processing timeframes: {e}")

    async def _update_timeframe_data(self, symbol: str, interval: str, new_kline: Dict[str, Any]):
        """Update higher timeframe data with new K-line."""
        try:
            table_name = f"kline_{symbol.lower()}_{interval}"
            
            # Load existing data for the timeframe
            existing_data = self.storage.load(
                table_name,
                start_time=datetime.now() - timedelta(hours=24)
            )

            if existing_data is None or existing_data.empty:
                # Initialize with new data
                import pandas as pd
                df = pd.DataFrame([new_kline])
                df.set_index("timestamp", inplace=True)
            else:
                # Aggregate or update existing data
                df = self._aggregate_timeframe_data(existing_data, new_kline, interval)

            # Save updated data
            self.storage.save(df, table_name)

        except Exception as e:
            logger.error(f"Error updating timeframe data for {interval}: {e}")

    def _aggregate_timeframe_data(self, existing_data: pd.DataFrame, new_kline: Dict[str, Any], interval: str) -> pd.DataFrame:
        """Aggregate data for higher timeframes."""
        try:
            # Add new data to existing data
            import pandas as pd
            new_df = pd.DataFrame([new_kline])
            new_df.set_index("timestamp", inplace=True)
            
            combined_data = pd.concat([existing_data, new_df]).sort_index()
            
            # Remove duplicates and keep latest
            combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
            
            # Resample based on interval
            interval_map = {
                "5m": "5T",
                "15m": "15T",
                "30m": "30T",
                "1h": "1H",
                "4h": "4H",
                "1d": "1D"
            }
            
            if interval in interval_map:
                resampled = combined_data.resample(interval_map[interval]).agg({
                    "open": "first",
                    "high": "max",
                    "low": "min",
                    "close": "last",
                    "volume": "sum",
                    "quote_volume": "sum",
                    "count": "sum",
                    "taker_buy_volume": "sum",
                    "taker_buy_quote_volume": "sum",
                    "ignore": "first"
                }).dropna()
                
                return resampled
            
            return combined_data

        except Exception as e:
            logger.error(f"Error aggregating timeframe data: {e}")
            return existing_data

    async def get_latest_data(self, symbol: str, interval: str = "1m", limit: int = 100) -> pd.DataFrame:
        """Get latest market data for a symbol and interval."""
        try:
            table_name = f"kline_{symbol.lower()}_{interval}"
            return self.storage.load(table_name, limit=limit)
        except Exception as e:
            logger.error(f"Error getting latest data for {symbol}: {e}")
            return None

    async def get_multi_timeframe_data(self, symbol: str, intervals: List[str] = None) -> Dict[str, pd.DataFrame]:
        """Get data for multiple timeframes."""
        if intervals is None:
            intervals = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
        
        data = {}
        for interval in intervals:
            try:
                df = await self.get_latest_data(symbol, interval, limit=100)
                if df is not None and not df.empty:
                    data[interval] = df
            except Exception as e:
                logger.error(f"Error getting {interval} data for {symbol}: {e}")
        
        return data

    def get_stream_status(self) -> Dict[str, Any]:
        """Get status of all active streams."""
        # Calculate error rate
        total_messages = self._metrics.get('messages_processed', 0)
        failed_messages = self._metrics.get('messages_failed', 0)
        error_rate = failed_messages / max(total_messages, 1)
        
        # Get failed connection count
        failed_connections = sum(attempts for attempts in self.ws_manager.reconnect_attempts.values())
        
        return {
            "active_streams": len(self.active_streams),
            "streams": self.active_streams.copy(),
            "storage_connected": self.storage.is_connected,
            "is_running": self.is_running,
            "error_rate": error_rate,
            "failed_connections": failed_connections,
            "recovery_enabled": self.ws_manager._recovery_enabled,
            "connection_status": {
                stream_name: {
                    "reconnect_attempts": self.ws_manager.reconnect_attempts.get(stream_name, 0),
                    "is_connected": stream_name in self.ws_manager.connections,
                    "data_count": stream_info.get("data_count", 0)
                }
                for stream_name, stream_info in self.active_streams.items()
            }
        }

    async def stop(self):
        """Stop the real-time data manager."""
        try:
            self.is_running = False
            
            # Cancel flush task
            if self._flush_task:
                self._flush_task.cancel()
                try:
                    await self._flush_task
                except asyncio.CancelledError:
                    pass
            
            # Flush remaining buffer
            await self._flush_buffer()
            
            await self.ws_manager.close_all()
            self.storage.disconnect()
            logger.info("Real-time data manager stopped")
        except Exception as e:
            logger.error(f"Error stopping real-time data manager: {e}")

    async def _buffer_data(self, table_name: str, kline_data: Dict[str, Any]):
        """Buffer data for batch processing."""
        async with self._buffer_lock:
            self._write_buffer[table_name].append(kline_data)
            
            # Check if we need to flush
            buffer_size = sum(len(data) for data in self._write_buffer.values())
            if buffer_size >= self._batch_size:
                await self._flush_buffer()

    async def _flush_buffer(self):
        """Flush buffered data to storage."""
        async with self._buffer_lock:
            if not self._write_buffer:
                return
            
            try:
                for table_name, data_list in self._write_buffer.items():
                    if data_list:
                        # Convert to DataFrame
                        df = pd.DataFrame(data_list)
                        df.set_index("timestamp", inplace=True)
                        
                        # Store data
                        self.storage.save(df, table_name)
                        self._metrics['storage_operations'] += 1
                
                # Clear buffer
                self._write_buffer.clear()
                self._last_flush = time.time()
                
            except Exception as e:
                logger.error(f"Error flushing buffer: {e}")

    async def _background_flush(self):
        """Background task to periodically flush buffer."""
        while self.is_running:
            try:
                # Check if it's time to flush
                if time.time() - self._last_flush >= self._flush_interval:
                    await self._flush_buffer()
                
                # Sleep for a short interval
                await asyncio.sleep(1.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in background flush task: {e}")

    def get_metrics(self) -> Dict[str, Any]:
        """Get system metrics."""
        uptime = datetime.now() - self._metrics['start_time']
        return {
            **self._metrics,
            'uptime_seconds': uptime.total_seconds(),
            'active_streams': len(self.active_streams),
            'buffer_size': sum(len(data) for data in self._write_buffer.values()),
            'is_running': self.is_running
        }


# Global instance
real_time_data_manager = RealTimeDataManager()