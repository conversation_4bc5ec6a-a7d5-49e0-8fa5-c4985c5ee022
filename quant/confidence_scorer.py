"""
Intelligent Confidence Scoring System

Provides sophisticated confidence scoring for trading signals based on multiple
technical indicators and market conditions with weighted analysis.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import pandas as pd
from datetime import datetime

from quant.analysis.technical_indicators import TechnicalIndicators, MarketRegime
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SignalStrength(Enum):
    """Signal strength enumeration."""
    WEAK = 1
    MEDIUM = 2
    STRONG = 3


@dataclass
class ConfidenceScore:
    """Confidence score breakdown."""
    overall_confidence: float
    trend_score: float
    momentum_score: float
    volatility_score: float
    volume_score: float
    market_regime_score: float
    indicator_scores: Dict[str, float]
    calculation_details: Dict[str, Any]
    timestamp: datetime


class ConfidenceScorer:
    """Intelligent confidence scoring system for trading signals."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize confidence scorer with configuration."""
        self.config = config or self._get_default_config()
        self.weights = self.config.get('weights', {})
        self.thresholds = self.config.get('thresholds', {})
        self.indicator_weights = self.config.get('indicator_weights', {})
        self.market_regime_adjustments = self.config.get('market_regime_adjustments', {})
        
        self.technical_indicators = TechnicalIndicators()
        self.logger = logger
        
        # Performance tracking
        self.calculation_times = []
        self.recent_scores = []
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for confidence scoring."""
        return {
            'weights': {
                'trend': 0.30,
                'momentum': 0.25,
                'volatility': 0.20,
                'volume': 0.15,
                'market_regime': 0.10
            },
            'thresholds': {
                'minimum': 0.6,
                'strong': 0.8,
                'maximum': 0.9
            },
            'indicator_weights': {
                'rsi': 0.8,
                'macd': 0.9,
                'bollinger_bands': 0.7,
                'volume_profile': 0.6,
                'adx': 0.8,
                'stochastic': 0.7,
                'williams_r': 0.6,
                'cci': 0.7,
                'mfi': 0.6
            },
            'market_regime_adjustments': {
                'bullish': 1.1,
                'bearish': 1.0,
                'sideways': 0.9,
                'volatile': 0.8
            }
        }
    
    def calculate_confidence(self, market_data: pd.DataFrame) -> ConfidenceScore:
        """
        Calculate comprehensive confidence score based on market data.
        
        Args:
            market_data: DataFrame with OHLCV data
            
        Returns:
            ConfidenceScore object with detailed breakdown
        """
        start_time = time.time()
        
        try:
            # Calculate all technical indicators
            indicators = self._calculate_indicators(market_data)
            
            # Calculate individual category scores
            trend_score = self._calculate_trend_score(indicators, market_data)
            momentum_score = self._calculate_momentum_score(indicators, market_data)
            volatility_score = self._calculate_volatility_score(indicators, market_data)
            volume_score = self._calculate_volume_score(indicators, market_data)
            market_regime_score = self._calculate_market_regime_score(indicators, market_data)
            
            # Get individual indicator scores
            indicator_scores = self._calculate_individual_indicator_scores(indicators, market_data)
            
            # Calculate weighted overall confidence
            overall_confidence = self._calculate_weighted_confidence(
                trend_score, momentum_score, volatility_score, 
                volume_score, market_regime_score
            )
            
            # Apply market regime adjustment
            market_regime = self._detect_market_regime(market_data)
            adjusted_confidence = self._apply_market_regime_adjustment(
                overall_confidence, market_regime
            )
            
            # Create calculation details
            calculation_details = {
                'market_regime': market_regime.value if market_regime else 'unknown',
                'weights_used': self.weights,
                'thresholds_used': self.thresholds,
                'adjustment_factor': self.market_regime_adjustments.get(
                    market_regime.value if market_regime else 'sideways', 1.0
                ),
                'raw_confidence': overall_confidence,
                'adjusted_confidence': adjusted_confidence
            }
            
            confidence_score = ConfidenceScore(
                overall_confidence=adjusted_confidence,
                trend_score=trend_score,
                momentum_score=momentum_score,
                volatility_score=volatility_score,
                volume_score=volume_score,
                market_regime_score=market_regime_score,
                indicator_scores=indicator_scores,
                calculation_details=calculation_details,
                timestamp=datetime.now()
            )
            
            # Track performance
            calculation_time = time.time() - start_time
            self._track_performance(calculation_time, confidence_score)
            
            return confidence_score
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence score: {e}")
            # Return minimum confidence on error
            return ConfidenceScore(
                overall_confidence=0.0,
                trend_score=0.0,
                momentum_score=0.0,
                volatility_score=0.0,
                volume_score=0.0,
                market_regime_score=0.0,
                indicator_scores={},
                calculation_details={'error': str(e)},
                timestamp=datetime.now()
            )
    
    def _calculate_indicators(self, market_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all technical indicators for confidence scoring."""
        indicators = {}
        
        try:
            # Trend indicators
            indicators['sma_20'] = self.technical_indicators.sma(market_data['close'], 20)
            indicators['sma_50'] = self.technical_indicators.sma(market_data['close'], 50)
            indicators['ema_12'] = self.technical_indicators.ema(market_data['close'], 12)
            indicators['ema_26'] = self.technical_indicators.ema(market_data['close'], 26)
            
            # MACD
            macd_result = self.technical_indicators.macd(
                market_data['close'], 12, 26, 9
            )
            indicators['macd_line'] = macd_result['macd']
            indicators['macd_signal'] = macd_result['signal']
            indicators['macd_histogram'] = macd_result['histogram']
            
            # Momentum indicators
            indicators['rsi'] = self.technical_indicators.rsi(market_data['close'], 14)
            indicators['stochastic'] = self.technical_indicators.stochastic(
                market_data['high'], market_data['low'], market_data['close'], 14, 3
            )
            indicators['williams_r'] = self.technical_indicators.williams_r(
                market_data['high'], market_data['low'], market_data['close'], 14
            )
            indicators['cci'] = self.technical_indicators.cci(
                market_data['high'], market_data['low'], market_data['close'], 14
            )
            indicators['mfi'] = self.technical_indicators.money_flow_index(
                market_data['high'], market_data['low'], market_data['close'], 
                market_data['volume'], 14
            )
            
            # Volatility indicators
            bb_result = self.technical_indicators.bollinger_bands(
                market_data['close'], 20, 2
            )
            indicators['bb_upper'] = bb_result['upper']
            indicators['bb_middle'] = bb_result['middle']
            indicators['bb_lower'] = bb_result['lower']
            indicators['atr'] = self.technical_indicators.atr(
                market_data['high'], market_data['low'], market_data['close'], 14
            )
            
            # Volume indicators
            # Create DataFrame for VWAP calculation
            vwap_data = pd.DataFrame({
                'high': market_data['high'],
                'low': market_data['low'],
                'close': market_data['close'],
                'volume': market_data['volume']
            })
            indicators['vwap'] = self.technical_indicators.vwap(vwap_data)
            indicators['volume_sma'] = self.technical_indicators.sma(market_data['volume'], 20)
            
            # ADX for trend strength
            try:
                adx_result = self.technical_indicators.adx(
                    market_data['high'], market_data['low'], market_data['close'], 14
                )
                if 'adx' in adx_result and len(adx_result['adx']) > 0:
                    indicators['adx'] = float(adx_result['adx'].iloc[-1])
                else:
                    indicators['adx'] = 0.0
            except Exception as e:
                self.logger.warning(f"Error calculating ADX: {e}")
                indicators['adx'] = 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
        
        return indicators
    
    def _safe_get_value(self, data, default=0.0):
        """Safely get value from pandas Series or return default."""
        if hasattr(data, 'iloc'):
            return data.iloc[-1]
        elif isinstance(data, (int, float)):
            return float(data)
        else:
            return default
    
    def _safe_get_slice(self, data, count):
        """Safely get slice from pandas Series or return empty list."""
        if hasattr(data, 'iloc'):
            return data.iloc[-count:]
        elif isinstance(data, (list, tuple)):
            return data[-count:]
        else:
            return []
    
    def _calculate_trend_score(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> float:
        """Calculate trend-based confidence score."""
        try:
            scores = []
            
            # SMA trend analysis
            if 'sma_20' in indicators and 'sma_50' in indicators:
                sma_20_latest = self._safe_get_value(indicators['sma_20'])
                sma_50_latest = self._safe_get_value(indicators['sma_50'])
                close_latest = self._safe_get_value(market_data['close'])
                
                # Price above SMAs
                if close_latest > sma_20_latest > sma_50_latest:
                    scores.append(0.8)
                elif close_latest > sma_20_latest:
                    scores.append(0.6)
                elif close_latest < sma_20_latest < sma_50_latest:
                    scores.append(0.2)
                else:
                    scores.append(0.4)
            
            # MACD trend analysis
            if 'macd_line' in indicators and 'macd_signal' in indicators:
                macd_latest = self._safe_get_value(indicators['macd_line'])
                signal_latest = self._safe_get_value(indicators['macd_signal'])
                histogram_latest = self._safe_get_value(indicators['macd_histogram'])
                
                if macd_latest > signal_latest and histogram_latest > 0:
                    scores.append(0.8)
                elif macd_latest > signal_latest:
                    scores.append(0.6)
                elif histogram_latest < 0:
                    scores.append(0.3)
                else:
                    scores.append(0.5)
            
            # ADX trend strength
            if 'adx' in indicators:
                adx_latest = self._safe_get_value(indicators['adx'])
                if adx_latest > 25:
                    scores.append(0.8)
                elif adx_latest > 20:
                    scores.append(0.6)
                else:
                    scores.append(0.4)
            
            return np.mean(scores) if scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating trend score: {e}")
            return 0.5
    
    def _calculate_momentum_score(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> float:
        """Calculate momentum-based confidence score."""
        try:
            scores = []
            
            # RSI analysis
            if 'rsi' in indicators:
                rsi_latest = self._safe_get_value(indicators['rsi'])
                if 30 < rsi_latest < 70:
                    scores.append(0.7)
                elif rsi_latest < 30:
                    scores.append(0.4)
                else:
                    scores.append(0.5)
            
            # Stochastic analysis
            if 'stochastic' in indicators:
                stoch_latest = self._safe_get_value(indicators['stochastic'])
                if 20 < stoch_latest < 80:
                    scores.append(0.7)
                elif stoch_latest < 20:
                    scores.append(0.4)
                else:
                    scores.append(0.5)
            
            # CCI analysis
            if 'cci' in indicators:
                cci_latest = self._safe_get_value(indicators['cci'])
                if -100 < cci_latest < 100:
                    scores.append(0.7)
                else:
                    scores.append(0.4)
            
            return np.mean(scores) if scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum score: {e}")
            return 0.5
    
    def _calculate_volatility_score(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> float:
        """Calculate volatility-based confidence score."""
        try:
            scores = []
            
            # Bollinger Bands analysis
            if 'bb_upper' in indicators and 'bb_lower' in indicators:
                bb_upper_latest = self._safe_get_value(indicators['bb_upper'])
                bb_lower_latest = self._safe_get_value(indicators['bb_lower'])
                close_latest = self._safe_get_value(market_data['close'])
                
                # Position within Bollinger Bands
                bb_position = (close_latest - bb_lower_latest) / (bb_upper_latest - bb_lower_latest)
                
                if 0.2 < bb_position < 0.8:
                    scores.append(0.7)
                elif bb_position < 0.1 or bb_position > 0.9:
                    scores.append(0.3)
                else:
                    scores.append(0.5)
            
            # ATR analysis (normalized by price)
            if 'atr' in indicators:
                atr_latest = self._safe_get_value(indicators['atr'])
                close_latest = self._safe_get_value(market_data['close'])
                atr_ratio = atr_latest / close_latest
                
                if atr_ratio < 0.02:
                    scores.append(0.7)
                elif atr_ratio < 0.04:
                    scores.append(0.5)
                else:
                    scores.append(0.3)
            
            return np.mean(scores) if scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility score: {e}")
            return 0.5
    
    def _calculate_volume_score(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> float:
        """Calculate volume-based confidence score."""
        try:
            scores = []
            
            # Volume analysis
            if 'volume_sma' in indicators:
                volume_latest = self._safe_get_value(market_data['volume'])
                volume_sma_latest = self._safe_get_value(indicators['volume_sma'])
                
                if volume_latest > volume_sma_latest * 1.2:
                    scores.append(0.8)
                elif volume_latest > volume_sma_latest:
                    scores.append(0.6)
                else:
                    scores.append(0.4)
            
            # MFI analysis
            if 'mfi' in indicators:
                mfi_latest = self._safe_get_value(indicators['mfi'])
                if 30 < mfi_latest < 70:
                    scores.append(0.7)
                elif mfi_latest < 30:
                    scores.append(0.4)
                else:
                    scores.append(0.5)
            
            return np.mean(scores) if scores else 0.5
            
        except Exception as e:
            self.logger.error(f"Error calculating volume score: {e}")
            return 0.5
    
    def _calculate_market_regime_score(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> float:
        """Calculate market regime-based confidence score."""
        try:
            market_regime = self._detect_market_regime(market_data)
            
            # Score based on market regime
            regime_scores = {
                MarketRegime.BULLISH: 0.8,
                MarketRegime.BEARISH: 0.6,
                MarketRegime.SIDEWAYS: 0.4,
                MarketRegime.VOLATILE: 0.3
            }
            
            return regime_scores.get(market_regime, 0.5)
            
        except Exception as e:
            self.logger.error(f"Error calculating market regime score: {e}")
            return 0.5
    
    def _calculate_individual_indicator_scores(self, indicators: Dict[str, Any], market_data: pd.DataFrame) -> Dict[str, float]:
        """Calculate individual indicator scores."""
        scores = {}
        
        try:
            # RSI score
            if 'rsi' in indicators:
                rsi_latest = self._safe_get_value(indicators['rsi'])
                if 40 < rsi_latest < 60:
                    scores['rsi'] = 0.8
                elif 30 < rsi_latest < 70:
                    scores['rsi'] = 0.6
                else:
                    scores['rsi'] = 0.3
            
            # MACD score
            if 'macd_histogram' in indicators:
                macd_hist_latest = self._safe_get_value(indicators['macd_histogram'])
                if macd_hist_latest > 0:
                    scores['macd'] = 0.7
                else:
                    scores['macd'] = 0.4
            
            # Bollinger Bands score
            if 'bb_upper' in indicators and 'bb_lower' in indicators:
                bb_upper_latest = self._safe_get_value(indicators['bb_upper'])
                bb_lower_latest = self._safe_get_value(indicators['bb_lower'])
                close_latest = self._safe_get_value(market_data['close'])
                
                bb_position = (close_latest - bb_lower_latest) / (bb_upper_latest - bb_lower_latest)
                if 0.3 < bb_position < 0.7:
                    scores['bollinger_bands'] = 0.7
                else:
                    scores['bollinger_bands'] = 0.4
            
            # Volume score
            if 'volume_sma' in indicators:
                volume_latest = self._safe_get_value(market_data['volume'])
                volume_sma_latest = self._safe_get_value(indicators['volume_sma'])
                volume_ratio = volume_latest / volume_sma_latest
                
                if volume_ratio > 1.2:
                    scores['volume_profile'] = 0.8
                elif volume_ratio > 0.8:
                    scores['volume_profile'] = 0.6
                else:
                    scores['volume_profile'] = 0.3
            
            # ADX score
            if 'adx' in indicators:
                adx_latest = self._safe_get_value(indicators['adx'])
                if adx_latest > 25:
                    scores['adx'] = 0.8
                elif adx_latest > 20:
                    scores['adx'] = 0.6
                else:
                    scores['adx'] = 0.4
            
        except Exception as e:
            self.logger.error(f"Error calculating individual indicator scores: {e}")
        
        return scores
    
    def _calculate_weighted_confidence(self, trend_score: float, momentum_score: float,
                                     volatility_score: float, volume_score: float,
                                     market_regime_score: float) -> float:
        """Calculate weighted overall confidence score."""
        return (
            trend_score * self.weights.get('trend', 0.30) +
            momentum_score * self.weights.get('momentum', 0.25) +
            volatility_score * self.weights.get('volatility', 0.20) +
            volume_score * self.weights.get('volume', 0.15) +
            market_regime_score * self.weights.get('market_regime', 0.10)
        )
    
    def _detect_market_regime(self, market_data: pd.DataFrame) -> MarketRegime:
        """Detect current market regime."""
        try:
            if len(market_data) < 50:
                return MarketRegime.SIDEWAYS
            
            close_prices = self._safe_get_slice(market_data['close'], 50)
            if hasattr(close_prices, 'pct_change'):
                returns = close_prices.pct_change().dropna()
                volatility = returns.std() if len(returns) > 0 else 0.02
                sma_20 = close_prices.rolling(20).mean().iloc[-1] if len(close_prices) >= 20 else self._safe_get_value(market_data['close'])
                sma_50 = close_prices.rolling(50).mean().iloc[-1] if len(close_prices) >= 50 else self._safe_get_value(market_data['close'])
                current_price = close_prices.iloc[-1]
            else:
                # Fallback for non-pandas data
                volatility = 0.02
                sma_20 = self._safe_get_value(market_data['close'])
                sma_50 = self._safe_get_value(market_data['close'])
                current_price = self._safe_get_value(market_data['close'])
            
            # Determine regime
            if volatility > 0.03:
                return MarketRegime.VOLATILE
            elif current_price > sma_20 > sma_50:
                return MarketRegime.BULLISH
            elif current_price < sma_20 < sma_50:
                return MarketRegime.BEARISH
            else:
                return MarketRegime.SIDEWAYS
                
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.SIDEWAYS
    
    def _apply_market_regime_adjustment(self, confidence: float, market_regime: MarketRegime) -> float:
        """Apply market regime adjustment to confidence score."""
        adjustment = self.market_regime_adjustments.get(market_regime.value, 1.0)
        adjusted = confidence * adjustment
        
        # Ensure confidence stays within bounds
        return max(0.0, min(1.0, adjusted))
    
    def _track_performance(self, calculation_time: float, confidence_score: ConfidenceScore):
        """Track performance metrics."""
        self.calculation_times.append(calculation_time)
        self.recent_scores.append(confidence_score)
        
        # Keep only recent performance data
        if len(self.calculation_times) > 100:
            self.calculation_times = self.calculation_times[-100:]
        if len(self.recent_scores) > 100:
            self.recent_scores = self.recent_scores[-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if not self.calculation_times:
            return {}
        
        return {
            'avg_calculation_time': np.mean(self.calculation_times),
            'max_calculation_time': np.max(self.calculation_times),
            'min_calculation_time': np.min(self.calculation_times),
            'recent_scores_count': len(self.recent_scores),
            'avg_confidence': np.mean([s.overall_confidence for s in self.recent_scores]) if self.recent_scores else 0
        }
    
    def get_signal_strength(self, confidence_score: float) -> SignalStrength:
        """Convert confidence score to signal strength."""
        if confidence_score >= self.thresholds.get('maximum', 0.9):
            return SignalStrength.STRONG
        elif confidence_score >= self.thresholds.get('strong', 0.8):
            return SignalStrength.MEDIUM
        elif confidence_score >= self.thresholds.get('minimum', 0.6):
            return SignalStrength.WEAK
        else:
            return SignalStrength.WEAK
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update confidence scorer configuration."""
        self.config.update(new_config)
        self.weights = self.config.get('weights', {})
        self.thresholds = self.config.get('thresholds', {})
        self.indicator_weights = self.config.get('indicator_weights', {})
        self.market_regime_adjustments = self.config.get('market_regime_adjustments', {})
        
        self.logger.info("Confidence scorer configuration updated")
    
    def adaptive_weight_adjustment(self, market_data: pd.DataFrame, performance_history: list = None):
        """
        Adaptively adjust confidence weights based on market conditions and performance.
        
        Args:
            market_data: Current market data
            performance_history: Historical performance data for weight optimization
        """
        try:
            # Detect current market regime
            market_regime = self._detect_market_regime(market_data)
            
            # Calculate market volatility
            returns = market_data['close'].pct_change().dropna()
            current_volatility = returns.std()
            
            # Adjust weights based on market conditions
            adjusted_weights = self.weights.copy()
            
            if market_regime == MarketRegime.VOLATILE:
                # In volatile markets, reduce trend weight, increase volatility weight
                adjusted_weights['trend'] *= 0.8
                adjusted_weights['volatility'] *= 1.3
                adjusted_weights['volume'] *= 1.2
                
            elif market_regime == MarketRegime.BULLISH:
                # In bullish markets, increase trend and momentum weights
                adjusted_weights['trend'] *= 1.2
                adjusted_weights['momentum'] *= 1.1
                adjusted_weights['market_regime'] *= 1.2
                
            elif market_regime == MarketRegime.BEARISH:
                # In bearish markets, increase momentum and volatility weights
                adjusted_weights['momentum'] *= 1.2
                adjusted_weights['volatility'] *= 1.3
                adjusted_weights['trend'] *= 0.7
                
            elif market_regime == MarketRegime.SIDEWAYS:
                # In sideways markets, increase volume and momentum weights
                adjusted_weights['volume'] *= 1.3
                adjusted_weights['momentum'] *= 1.1
                adjusted_weights['trend'] *= 0.8
            
            # Normalize weights to ensure they sum to 1.0
            total_weight = sum(adjusted_weights.values())
            if total_weight > 0:
                adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}
            
            # Apply performance-based adjustments if history is available
            if performance_history:
                adjusted_weights = self._apply_performance_based_adjustments(
                    adjusted_weights, performance_history
                )
            
            # Update the weights
            self.weights = adjusted_weights
            self.config['weights'] = adjusted_weights
            
            self.logger.info(f"Weights adjusted for {market_regime.value} market regime")
            
        except Exception as e:
            self.logger.error(f"Error in adaptive weight adjustment: {e}")
    
    def _apply_performance_based_adjustments(self, current_weights: Dict[str, float], 
                                           performance_history: list) -> Dict[str, float]:
        """Apply performance-based adjustments to weights."""
        try:
            if not performance_history or len(performance_history) < 10:
                return current_weights
            
            # Calculate recent performance by category
            category_performance = {
                'trend': [],
                'momentum': [],
                'volatility': [],
                'volume': [],
                'market_regime': []
            }
            
            for trade in performance_history[-50:]:  # Last 50 trades
                if trade.get('confidence_breakdown'):
                    breakdown = trade['confidence_breakdown']
                    outcome = 1 if trade.get('status') == 'WIN' else 0
                    
                    for category in category_performance.keys():
                        score = breakdown.get(f'{category}_score', 0.5)
                        weight = current_weights.get(category, 0.2)
                        # Weight the outcome by the category's contribution
                        category_performance[category].append(outcome * score * weight)
            
            # Calculate average performance by category
            avg_performance = {}
            for category, scores in category_performance.items():
                if scores:
                    avg_performance[category] = sum(scores) / len(scores)
                else:
                    avg_performance[category] = 0.5
            
            # Adjust weights based on performance
            adjusted_weights = current_weights.copy()
            total_adjustment = 0
            
            for category, performance in avg_performance.items():
                if performance > 0.6:  # Good performance
                    adjustment = 0.1 * (performance - 0.5) * 2
                    adjusted_weights[category] += adjustment
                    total_adjustment += adjustment
                elif performance < 0.4:  # Poor performance
                    adjustment = 0.1 * (0.5 - performance) * 2
                    adjusted_weights[category] -= adjustment
                    total_adjustment -= adjustment
            
            # Normalize weights
            if total_adjustment != 0:
                total_weight = sum(adjusted_weights.values())
                adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}
            
            return adjusted_weights
            
        except Exception as e:
            self.logger.error(f"Error applying performance-based adjustments: {e}")
            return current_weights
    
    def calculate_confidence_with_adaptation(self, market_data: pd.DataFrame, 
                                           performance_history: list = None) -> ConfidenceScore:
        """
        Calculate confidence score with adaptive weight adjustment.
        
        Args:
            market_data: Market data for analysis
            performance_history: Historical performance data
            
        Returns:
            ConfidenceScore with adaptive weights
        """
        # Store original weights
        original_weights = self.weights.copy()
        
        try:
            # Apply adaptive weight adjustment
            self.adaptive_weight_adjustment(market_data, performance_history)
            
            # Calculate confidence with adjusted weights
            confidence_score = self.calculate_confidence(market_data)
            
            # Add adaptation information to calculation details
            confidence_score.calculation_details['adaptive_weights'] = self.weights
            confidence_score.calculation_details['original_weights'] = original_weights
            confidence_score.calculation_details['weight_adjustment_applied'] = True
            
            return confidence_score
            
        except Exception as e:
            # Restore original weights on error
            self.weights = original_weights
            self.logger.error(f"Error in adaptive confidence calculation: {e}")
            # Fall back to regular calculation
            return self.calculate_confidence(market_data)
    
    def get_confidence_reliability_score(self, confidence_score: ConfidenceScore) -> float:
        """
        Calculate a reliability score for the confidence score based on various factors.
        
        Args:
            confidence_score: The confidence score to evaluate
            
        Returns:
            Reliability score between 0.0 and 1.0
        """
        try:
            reliability_factors = []
            
            # Factor 1: Consistency of individual indicator scores
            if confidence_score.indicator_scores:
                scores = list(confidence_score.indicator_scores.values())
                if len(scores) > 1:
                    # Lower standard deviation indicates higher consistency
                    score_std = np.std(scores)
                    consistency_score = max(0, 1 - (score_std * 2))  # Normalize to 0-1
                    reliability_factors.append(consistency_score)
            
            # Factor 2: Market regime clarity
            regime_score = confidence_score.market_regime_score
            regime_clarity = abs(regime_score - 0.5) * 2  # How far from neutral
            reliability_factors.append(regime_clarity)
            
            # Factor 3: Calculation time performance
            if hasattr(self, 'calculation_times') and self.calculation_times:
                avg_time = np.mean(self.calculation_times)
                time_efficiency = max(0, 1 - (avg_time / 0.2))  # Normalize to 200ms target
                reliability_factors.append(time_efficiency)
            
            # Factor 4: Confidence score extremity
            # Extreme scores (very high or very low) are often more reliable
            confidence_extremity = abs(confidence_score.overall_confidence - 0.5) * 2
            reliability_factors.append(confidence_extremity)
            
            # Calculate weighted reliability score
            if reliability_factors:
                # Give more weight to consistency and regime clarity
                weights = [0.3, 0.3, 0.2, 0.2]
                reliability = sum(f * w for f, w in zip(reliability_factors, weights))
                return max(0, min(1, reliability))
            else:
                return 0.5  # Default reliability
                
        except Exception as e:
            self.logger.error(f"Error calculating reliability score: {e}")
            return 0.5
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert confidence scorer to dictionary representation."""
        return {
            'config': self.config,
            'performance_stats': self.get_performance_stats()
        }
    
    def __str__(self) -> str:
        """String representation of confidence scorer."""
        return f"ConfidenceScorer(weights={self.weights}, thresholds={self.thresholds})"