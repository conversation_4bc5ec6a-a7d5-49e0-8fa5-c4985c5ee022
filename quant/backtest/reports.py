"""
Backtest Report Generator
"""

import base64
from datetime import datetime
from io import BytesIO
from typing import Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


class BacktestReport:
    """回测报告生成器"""

    def __init__(self, results: dict[str, Any]):
        self.results = results
        self.report_data = {}

    def generate_report(self) -> dict[str, Any]:
        """生成完整报告"""
        report = {
            "summary": self._generate_summary(),
            "performance": self._generate_performance_analysis(),
            "risk_analysis": self._generate_risk_analysis(),
            "trade_analysis": self._generate_trade_analysis(),
            "equity_analysis": self._generate_equity_analysis(),
            "strategy_comparison": self._generate_strategy_comparison(),
            "recommendations": self._generate_recommendations(),
            "timestamp": datetime.now(),
        }

        self.report_data = report
        return report

    def _generate_summary(self) -> dict[str, Any]:
        """生成摘要"""
        if "equity_curve" not in self.results or self.results["equity_curve"].empty:
            return {}

        equity_curve = self.results["equity_curve"]
        total_return = (
            equity_curve["total"].iloc[-1] - equity_curve["total"].iloc[0]
        ) / equity_curve["total"].iloc[0]

        summary = {
            "backtest_period": {
                "start": equity_curve.index[0].strftime("%Y-%m-%d"),
                "end": equity_curve.index[-1].strftime("%Y-%m-%d"),
                "duration_days": (equity_curve.index[-1] - equity_curve.index[0]).days,
            },
            "strategies_count": len(self.results.get("strategies", {})),
            "total_return": total_return,
            "annual_return": total_return
            * 365
            / (equity_curve.index[-1] - equity_curve.index[0]).days,
            "total_trades": len(self.results.get("trades", [])),
            "best_strategy": self._get_best_strategy(),
            "worst_strategy": self._get_worst_strategy(),
        }

        return summary

    def _generate_performance_analysis(self) -> dict[str, Any]:
        """生成绩效分析"""
        performance = {}

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            if "metrics" in strategy_results:
                metrics = strategy_results["metrics"]
                performance[strategy_name] = {
                    "total_return": metrics.get("total_return", 0),
                    "annual_return": metrics.get("annual_return", 0),
                    "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                    "max_drawdown": metrics.get("max_drawdown", 0),
                    "win_rate": metrics.get("win_rate", 0),
                    "profit_factor": metrics.get("profit_factor", 0),
                    "total_trades": metrics.get("total_trades", 0),
                }

        return performance

    def _generate_risk_analysis(self) -> dict[str, Any]:
        """生成风险分析"""
        risk_analysis = {}

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            if "equity_curve" in strategy_results:
                equity_curve = strategy_results["equity_curve"]

                # 计算风险指标
                returns = equity_curve.pct_change().dropna()
                volatility = returns.std() * np.sqrt(252)

                # VaR计算
                var_95 = returns.quantile(0.05)
                var_99 = returns.quantile(0.01)

                risk_analysis[strategy_name] = {
                    "volatility": volatility,
                    "var_95": var_95,
                    "var_99": var_99,
                    "max_drawdown": strategy_results.get("metrics", {}).get(
                        "max_drawdown", 0
                    ),
                    "calmar_ratio": strategy_results.get("metrics", {}).get(
                        "calmar_ratio", 0
                    ),
                    "sortino_ratio": strategy_results.get("metrics", {}).get(
                        "sortino_ratio", 0
                    ),
                }

        return risk_analysis

    def _generate_trade_analysis(self) -> dict[str, Any]:
        """生成交易分析"""
        trade_analysis = {}

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            trades = strategy_results.get("trades", [])

            if trades:
                # 计算交易统计
                trade_pnl = [t.get("pnl", 0) for t in trades]
                winning_trades = [pnl for pnl in trade_pnl if pnl > 0]
                losing_trades = [pnl for pnl in trade_pnl if pnl < 0]

                trade_analysis[strategy_name] = {
                    "total_trades": len(trades),
                    "winning_trades": len(winning_trades),
                    "losing_trades": len(losing_trades),
                    "avg_win": np.mean(winning_trades) if winning_trades else 0,
                    "avg_loss": np.mean(losing_trades) if losing_trades else 0,
                    "max_win": max(winning_trades) if winning_trades else 0,
                    "max_loss": min(losing_trades) if losing_trades else 0,
                    "profit_factor": (
                        abs(sum(winning_trades) / sum(losing_trades))
                        if losing_trades
                        else float("inf")
                    ),
                }

        return trade_analysis

    def _generate_equity_analysis(self) -> dict[str, Any]:
        """生成权益分析"""
        equity_analysis = {}

        if "equity_curve" in self.results:
            equity_curve = self.results["equity_curve"]

            # 计算统计指标
            returns = equity_curve["total"].pct_change().dropna()

            equity_analysis["overall"] = {
                "initial_equity": equity_curve["total"].iloc[0],
                "final_equity": equity_curve["total"].iloc[-1],
                "peak_equity": equity_curve["total"].max(),
                "lowest_equity": equity_curve["total"].min(),
                "volatility": returns.std() * np.sqrt(252),
                "positive_days": len(returns[returns > 0]),
                "negative_days": len(returns[returns < 0]),
                "best_day": returns.max(),
                "worst_day": returns.min(),
            }

        # 每个策略的权益分析
        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            if "equity_curve" in strategy_results:
                strategy_equity = strategy_results["equity_curve"]
                strategy_returns = strategy_equity.pct_change().dropna()

                equity_analysis[strategy_name] = {
                    "initial_equity": strategy_equity.iloc[0],
                    "final_equity": strategy_equity.iloc[-1],
                    "peak_equity": strategy_equity.max(),
                    "volatility": strategy_returns.std() * np.sqrt(252),
                    "max_drawdown": strategy_results.get("metrics", {}).get(
                        "max_drawdown", 0
                    ),
                }

        return equity_analysis

    def _generate_strategy_comparison(self) -> dict[str, Any]:
        """生成策略对比"""
        comparison = {
            "performance_ranking": self._rank_strategies_by_performance(),
            "risk_ranking": self._rank_strategies_by_risk(),
            "consistency_ranking": self._rank_strategies_by_consistency(),
            "correlation_matrix": self._calculate_strategy_correlations(),
        }

        return comparison

    def _generate_recommendations(self) -> list[str]:
        """生成建议"""
        recommendations = []

        # 基于回测结果生成建议
        if "strategies" in self.results:
            strategies = self.results["strategies"]

            # 获取最佳和最差策略
            best_strategy = self._get_best_strategy()
            worst_strategy = self._get_worst_strategy()

            if best_strategy:
                recommendations.append(
                    f"策略 '{best_strategy}' 表现最佳，建议重点优化和部署"
                )

            if worst_strategy:
                recommendations.append(
                    f"策略 '{worst_strategy}' 表现较差，建议重新评估或调整参数"
                )

            # 检查整体表现
            total_trades = len(self.results.get("trades", []))
            if total_trades < 50:
                recommendations.append("交易次数较少，建议延长回测周期或调整策略频率")

            # 检查风险控制
            for strategy_name, strategy_results in strategies.items():
                metrics = strategy_results.get("metrics", {})
                max_dd = metrics.get("max_drawdown", 0)

                if max_dd > 0.2:  # 20%以上回撤
                    recommendations.append(
                        f"策略 '{strategy_name}' 最大回撤较大（{max_dd:.1%}），建议加强风险控制"
                    )

        return recommendations

    def _get_best_strategy(self) -> str | None:
        """获取最佳策略"""
        strategies = self.results.get("strategies", {})
        if not strategies:
            return None

        best_strategy = None
        best_return = -float("inf")

        for strategy_name, strategy_results in strategies.items():
            total_return = strategy_results.get("return_rate", 0)
            if total_return > best_return:
                best_return = total_return
                best_strategy = strategy_name

        return best_strategy

    def _get_worst_strategy(self) -> str | None:
        """获取最差策略"""
        strategies = self.results.get("strategies", {})
        if not strategies:
            return None

        worst_strategy = None
        worst_return = float("inf")

        for strategy_name, strategy_results in strategies.items():
            total_return = strategy_results.get("return_rate", 0)
            if total_return < worst_return:
                worst_return = total_return
                worst_strategy = strategy_name

        return worst_strategy

    def _rank_strategies_by_performance(self) -> list[dict[str, Any]]:
        """按绩效排名策略"""
        rankings = []

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            metrics = strategy_results.get("metrics", {})

            # 综合评分
            score = (
                metrics.get("sharpe_ratio", 0) * 0.3
                + metrics.get("annual_return", 0) * 0.3
                + metrics.get("win_rate", 0) * 0.2
                + (1 - abs(metrics.get("max_drawdown", 0))) * 0.2
            )

            rankings.append(
                {
                    "strategy": strategy_name,
                    "score": score,
                    "total_return": metrics.get("total_return", 0),
                    "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                    "max_drawdown": metrics.get("max_drawdown", 0),
                }
            )

        return sorted(rankings, key=lambda x: x["score"], reverse=True)

    def _rank_strategies_by_risk(self) -> list[dict[str, Any]]:
        """按风险排名策略"""
        rankings = []

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            metrics = strategy_results.get("metrics", {})

            # 风险评分（越低越好）
            risk_score = (
                metrics.get("volatility", 0) * 0.4
                + metrics.get("max_drawdown", 0) * 0.4
                + (1 - metrics.get("calmar_ratio", 0)) * 0.2
            )

            rankings.append(
                {
                    "strategy": strategy_name,
                    "risk_score": risk_score,
                    "volatility": metrics.get("volatility", 0),
                    "max_drawdown": metrics.get("max_drawdown", 0),
                    "calmar_ratio": metrics.get("calmar_ratio", 0),
                }
            )

        return sorted(rankings, key=lambda x: x["risk_score"])

    def _rank_strategies_by_consistency(self) -> list[dict[str, Any]]:
        """按一致性排名策略"""
        rankings = []

        for strategy_name, strategy_results in self.results.get(
            "strategies", {}
        ).items():
            metrics = strategy_results.get("metrics", {})

            # 一致性评分
            consistency_score = (
                metrics.get("win_rate", 0) * 0.4
                + metrics.get("profit_factor", 0) * 0.3
                + metrics.get("sortino_ratio", 0) * 0.3
            )

            rankings.append(
                {
                    "strategy": strategy_name,
                    "consistency_score": consistency_score,
                    "win_rate": metrics.get("win_rate", 0),
                    "profit_factor": metrics.get("profit_factor", 0),
                    "sortino_ratio": metrics.get("sortino_ratio", 0),
                }
            )

        return sorted(rankings, key=lambda x: x["consistency_score"], reverse=True)

    def _calculate_strategy_correlations(self) -> dict[str, Any]:
        """计算策略相关性"""
        strategies = self.results.get("strategies", {})
        if len(strategies) < 2:
            return {}

        # 收集所有策略的权益曲线
        equity_curves = {}
        for strategy_name, strategy_results in strategies.items():
            if "equity_curve" in strategy_results:
                equity_curves[strategy_name] = strategy_results["equity_curve"]

        if len(equity_curves) < 2:
            return {}

        # 创建相关性矩阵
        correlation_matrix = pd.DataFrame(
            index=equity_curves.keys(), columns=equity_curves.keys()
        )

        for strategy1 in equity_curves:
            for strategy2 in equity_curves:
                if strategy1 == strategy2:
                    correlation_matrix.loc[strategy1, strategy2] = 1.0
                else:
                    returns1 = equity_curves[strategy1].pct_change().dropna()
                    returns2 = equity_curves[strategy2].pct_change().dropna()

                    # 对齐时间序列
                    aligned_returns = pd.concat([returns1, returns2], axis=1).dropna()

                    if len(aligned_returns) > 1:
                        correlation = aligned_returns.iloc[:, 0].corr(
                            aligned_returns.iloc[:, 1]
                        )
                        correlation_matrix.loc[strategy1, strategy2] = correlation
                    else:
                        correlation_matrix.loc[strategy1, strategy2] = 0.0

        return correlation_matrix.to_dict()

    def export_to_html(self, filepath: str):
        """导出HTML报告"""
        # 这里可以生成更详细的HTML报告
        # 为示例目的，简单保存报告数据
        import json

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(self.report_data, f, ensure_ascii=False, indent=2, default=str)

    def export_to_pdf(self, filepath: str):
        """导出PDF报告"""
        # 这里需要使用PDF生成库如ReportLab
        # 为示例目的，先保存为HTML
        self.export_to_html(filepath.replace(".pdf", ".html"))

    def generate_charts(self) -> dict[str, str]:
        """生成图表"""
        charts = {}

        try:
            # 权益曲线图
            if "equity_curve" in self.results:
                plt.figure(figsize=(12, 6))
                equity_curve = self.results["equity_curve"]

                for col in equity_curve.columns:
                    plt.plot(equity_curve.index, equity_curve[col], label=col)

                plt.title("Equity Curve")
                plt.xlabel("Date")
                plt.ylabel("Equity")
                plt.legend()
                plt.grid(True)

                # 转换为base64
                buffer = BytesIO()
                plt.savefig(buffer, format="png", dpi=150, bbox_inches="tight")
                buffer.seek(0)
                charts["equity_curve"] = base64.b64encode(buffer.read()).decode()
                plt.close()

            # 回撤图
            if "equity_curve" in self.results:
                plt.figure(figsize=(12, 6))
                equity_curve = self.results["equity_curve"]["total"]

                peak = equity_curve.expanding(min_periods=1).max()
                drawdown = (peak - equity_curve) / peak

                plt.fill_between(
                    drawdown.index, drawdown.values, 0, alpha=0.3, color="red"
                )
                plt.plot(drawdown.index, drawdown.values, color="red")
                plt.title("Drawdown")
                plt.xlabel("Date")
                plt.ylabel("Drawdown")
                plt.grid(True)

                buffer = BytesIO()
                plt.savefig(buffer, format="png", dpi=150, bbox_inches="tight")
                buffer.seek(0)
                charts["drawdown"] = base64.b64encode(buffer.read()).decode()
                plt.close()

        except Exception as e:
            print(f"Error generating charts: {e}")

        return charts
