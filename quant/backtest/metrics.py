"""
Backtest Metrics
"""

from typing import Any

import numpy as np
import pandas as pd


class BacktestMetrics:
    """回测指标计算器"""

    def __init__(self):
        self.risk_free_rate = 0.02  # 无风险利率

    def calculate_metrics(
        self, equity_curve: pd.Series, trades: list[dict[str, Any]] = None
    ) -> dict[str, Any]:
        """
        计算回测指标

        Args:
            equity_curve: 权益曲线
            trades: 交易记录

        Returns:
            指标字典
        """
        if equity_curve.empty:
            return {}

        metrics = {}

        # 收益率指标
        metrics.update(self._calculate_return_metrics(equity_curve))

        # 风险指标
        metrics.update(self._calculate_risk_metrics(equity_curve))

        # 交易指标
        if trades:
            metrics.update(self._calculate_trade_metrics(trades))

        # 综合指标
        metrics.update(self._calculate_comprehensive_metrics(equity_curve, trades))

        return metrics

    def _calculate_return_metrics(self, equity_curve: pd.Series) -> dict[str, float]:
        """计算收益率指标"""
        returns = equity_curve.pct_change().dropna()

        if len(returns) == 0:
            return {}

        total_return = (
            equity_curve.iloc[-1] - equity_curve.iloc[0]
        ) / equity_curve.iloc[0]

        # 年化收益率
        days = (equity_curve.index[-1] - equity_curve.index[0]).days
        years = days / 365.25
        annual_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0

        # 月度收益率
        monthly_returns = returns.resample("M").apply(lambda x: (1 + x).prod() - 1)
        monthly_return = monthly_returns.mean() if len(monthly_returns) > 0 else 0

        return {
            "total_return": total_return,
            "annual_return": annual_return,
            "monthly_return": monthly_return,
            "daily_return": returns.mean(),
            "cumulative_return": total_return,
        }

    def _calculate_risk_metrics(self, equity_curve: pd.Series) -> dict[str, float]:
        """计算风险指标"""
        returns = equity_curve.pct_change().dropna()

        if len(returns) == 0:
            return {}

        # 波动率
        volatility = returns.std() * np.sqrt(252)  # 年化波动率

        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(equity_curve)

        # VaR (Value at Risk)
        var_95 = returns.quantile(0.05)
        var_99 = returns.quantile(0.01)

        # CVaR (Conditional Value at Risk)
        cvar_95 = returns[returns <= var_95].mean()
        cvar_99 = returns[returns <= var_99].mean()

        return {
            "volatility": volatility,
            "max_drawdown": max_drawdown,
            "var_95": var_95,
            "var_99": var_99,
            "cvar_95": cvar_95,
            "cvar_99": cvar_99,
        }

    def _calculate_trade_metrics(
        self, trades: list[dict[str, Any]]
    ) -> dict[str, float]:
        """计算交易指标"""
        if not trades:
            return {}

        # 计算每笔交易的盈亏
        trade_pnl = []
        winning_trades = []
        losing_trades = []

        for trade in trades:
            pnl = trade.get("pnl", 0)
            trade_pnl.append(pnl)

            if pnl > 0:
                winning_trades.append(pnl)
            elif pnl < 0:
                losing_trades.append(pnl)

        total_trades = len(trades)
        winning_trades_count = len(winning_trades)

        # 基本指标
        win_rate = winning_trades_count / total_trades if total_trades > 0 else 0

        # 平均盈亏
        avg_win = np.mean(winning_trades) if winning_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0

        # 盈亏比
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float("inf")

        # 最大单笔盈利/亏损
        max_win = max(winning_trades) if winning_trades else 0
        max_loss = min(losing_trades) if losing_trades else 0

        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades_count,
            "losing_trades": total_trades - winning_trades_count,
            "win_rate": win_rate,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "max_win": max_win,
            "max_loss": max_loss,
        }

    def _calculate_comprehensive_metrics(
        self, equity_curve: pd.Series, trades: list[dict[str, Any]] = None
    ) -> dict[str, float]:
        """计算综合指标"""
        returns = equity_curve.pct_change().dropna()

        if len(returns) == 0:
            return {}

        # 夏普比率
        excess_returns = returns - self.risk_free_rate / 252
        sharpe_ratio = (
            excess_returns.mean() / returns.std() * np.sqrt(252)
            if returns.std() != 0
            else 0
        )

        # 索提诺比率
        downside_returns = returns[returns < 0]
        sortino_ratio = (
            excess_returns.mean() / downside_returns.std() * np.sqrt(252)
            if len(downside_returns) > 0 and downside_returns.std() != 0
            else 0
        )

        # 卡玛比率
        max_dd = self._calculate_max_drawdown(equity_curve)
        total_return = (
            equity_curve.iloc[-1] - equity_curve.iloc[0]
        ) / equity_curve.iloc[0]
        calmar_ratio = total_return / abs(max_dd) if max_dd != 0 else 0

        # 信息比率
        if trades:
            # 简化的信息比率计算
            trade_returns = [t.get("pnl", 0) for t in trades]
            if len(trade_returns) > 1:
                information_ratio = (
                    np.mean(trade_returns) / np.std(trade_returns)
                    if np.std(trade_returns) != 0
                    else 0
                )
            else:
                information_ratio = 0
        else:
            information_ratio = 0

        return {
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "calmar_ratio": calmar_ratio,
            "information_ratio": information_ratio,
        }

    def _calculate_max_drawdown(self, equity_curve: pd.Series) -> float:
        """计算最大回撤"""
        if len(equity_curve) < 2:
            return 0.0

        peak = equity_curve.expanding(min_periods=1).max()
        drawdown = (peak - equity_curve) / peak
        return drawdown.max()

    def calculate_drawdown_analysis(self, equity_curve: pd.Series) -> dict[str, Any]:
        """计算回撤分析"""
        if len(equity_curve) < 2:
            return {}

        peak = equity_curve.expanding(min_periods=1).max()
        drawdown = (peak - equity_curve) / peak

        # 找到所有回撤期
        is_drawdown = drawdown > 0
        drawdown_periods = []

        start_idx = None
        for i, is_dd in enumerate(is_drawdown):
            if is_dd and start_idx is None:
                start_idx = i
            elif not is_dd and start_idx is not None:
                drawdown_periods.append(
                    {
                        "start": equity_curve.index[start_idx],
                        "end": equity_curve.index[i],
                        "max_drawdown": drawdown.iloc[start_idx:i].max(),
                        "duration": i - start_idx,
                    }
                )
                start_idx = None

        return {
            "max_drawdown": drawdown.max(),
            "avg_drawdown": (
                drawdown[drawdown > 0].mean() if drawdown[drawdown > 0].any() else 0.0
            ),
            "drawdown_periods": len(drawdown_periods),
            "avg_drawdown_duration": (
                np.mean([p["duration"] for p in drawdown_periods])
                if drawdown_periods
                else 0.0
            ),
            "max_drawdown_duration": (
                max([p["duration"] for p in drawdown_periods])
                if drawdown_periods
                else 0.0
            ),
            "current_drawdown": drawdown.iloc[-1],
        }

    def calculate_monthly_returns(self, equity_curve: pd.Series) -> pd.DataFrame:
        """计算月度收益率"""
        returns = equity_curve.pct_change().dropna()
        monthly_returns = returns.resample("M").apply(lambda x: (1 + x).prod() - 1)

        return pd.DataFrame(
            {
                "month": monthly_returns.index.strftime("%Y-%m"),
                "return": monthly_returns.values,
            }
        )

    def calculate_rolling_metrics(
        self, equity_curve: pd.Series, window: int = 252
    ) -> pd.DataFrame:
        """计算滚动指标"""
        returns = equity_curve.pct_change().dropna()

        if len(returns) < window:
            return pd.DataFrame()

        # 滚动收益率
        rolling_return = returns.rolling(window=window).apply(
            lambda x: (1 + x).prod() - 1
        )

        # 滚动波动率
        rolling_volatility = returns.rolling(window=window).std() * np.sqrt(252)

        # 滚动夏普比率
        excess_returns = returns - self.risk_free_rate / 252
        rolling_sharpe = (
            excess_returns.rolling(window=window).mean()
            / returns.rolling(window=window).std()
            * np.sqrt(252)
        )

        # 滚动最大回撤
        rolling_max_dd = returns.rolling(window=window).apply(
            lambda x: self._calculate_max_drawdown(equity_curve.loc[x.index])
        )

        return pd.DataFrame(
            {
                "rolling_return": rolling_return,
                "rolling_volatility": rolling_volatility,
                "rolling_sharpe": rolling_sharpe,
                "rolling_max_drawdown": rolling_max_dd,
            }
        )

    def calculate_benchmark_metrics(
        self, equity_curve: pd.Series, benchmark_curve: pd.Series
    ) -> dict[str, float]:
        """计算与基准的对比指标"""
        if len(equity_curve) < 2 or len(benchmark_curve) < 2:
            return {}

        strategy_returns = equity_curve.pct_change().dropna()
        benchmark_returns = benchmark_curve.pct_change().dropna()

        # 对齐时间序列
        aligned_returns = pd.concat(
            [strategy_returns, benchmark_returns], axis=1
        ).dropna()

        if len(aligned_returns) < 2:
            return {}

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 相关系数
        correlation = strategy_returns.corr(benchmark_returns)

        # Beta
        covariance = strategy_returns.cov(benchmark_returns)
        benchmark_variance = benchmark_returns.var()
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0

        # Alpha
        strategy_mean_return = strategy_returns.mean()
        benchmark_mean_return = benchmark_returns.mean()
        alpha = strategy_mean_return - beta * benchmark_mean_return

        # 信息比率
        tracking_error = (strategy_returns - benchmark_returns).std()
        information_ratio = (
            (strategy_mean_return - benchmark_mean_return) / tracking_error
            if tracking_error > 0
            else 0
        )

        # 跟踪误差
        tracking_error_annual = tracking_error * np.sqrt(252)

        # 上行捕获率
        up_market = benchmark_returns > 0
        upside_capture = (
            (strategy_returns[up_market].mean() / benchmark_returns[up_market].mean())
            if up_market.any() and benchmark_returns[up_market].mean() > 0
            else 0
        )

        # 下行捕获率
        down_market = benchmark_returns < 0
        downside_capture = (
            (
                strategy_returns[down_market].mean()
                / benchmark_returns[down_market].mean()
            )
            if down_market.any() and benchmark_returns[down_market].mean() < 0
            else 0
        )

        return {
            "correlation": correlation,
            "beta": beta,
            "alpha": alpha * 252,  # 年化alpha
            "information_ratio": information_ratio * np.sqrt(252),
            "tracking_error": tracking_error_annual,
            "upside_capture": upside_capture,
            "downside_capture": downside_capture,
        }
