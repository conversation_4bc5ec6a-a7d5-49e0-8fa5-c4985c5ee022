"""
Backtest Broker
"""

from datetime import datetime
from enum import Enum
from typing import Any

import pandas as pd


class OrderStatus(Enum):
    """订单状态"""

    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class OrderType(Enum):
    """订单类型"""

    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"


class BacktestBroker:
    """回测经纪商"""

    def __init__(
        self,
        initial_capital: float = 100000,
        commission_rate: float = 0.001,
        slippage: float = 0.0001,
    ):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage

        # 持仓管理
        self.positions = {}
        self.position_history = []

        # 订单管理
        self.orders = {}
        self.order_history = []
        self.next_order_id = 1

        # 交易记录
        self.trades = []

        # 账户状态
        self.equity_curve = []
        self.margin_used = 0
        self.available_margin = initial_capital

    def buy(
        self,
        symbol: str,
        quantity: float,
        price: float,
        timestamp: datetime,
        order_type: OrderType = OrderType.MARKET,
    ) -> str:
        """
        买入

        Args:
            symbol: 交易标的
            quantity: 数量
            price: 价格
            timestamp: 时间戳
            order_type: 订单类型

        Returns:
            订单ID
        """
        order_id = f"order_{self.next_order_id}"
        self.next_order_id += 1

        # 创建订单
        order = {
            "order_id": order_id,
            "symbol": symbol,
            "type": "buy",
            "order_type": order_type,
            "quantity": quantity,
            "price": price,
            "timestamp": timestamp,
            "status": OrderStatus.PENDING,
            "filled_quantity": 0,
            "filled_price": 0,
            "commission": 0,
        }

        self.orders[order_id] = order

        # 执行订单
        self._execute_order(order)

        return order_id

    def sell(
        self,
        symbol: str,
        quantity: float,
        price: float,
        timestamp: datetime,
        order_type: OrderType = OrderType.MARKET,
    ) -> str:
        """
        卖出

        Args:
            symbol: 交易标的
            quantity: 数量
            price: 价格
            timestamp: 时间戳
            order_type: 订单类型

        Returns:
            订单ID
        """
        order_id = f"order_{self.next_order_id}"
        self.next_order_id += 1

        # 创建订单
        order = {
            "order_id": order_id,
            "symbol": symbol,
            "type": "sell",
            "order_type": order_type,
            "quantity": quantity,
            "price": price,
            "timestamp": timestamp,
            "status": OrderStatus.PENDING,
            "filled_quantity": 0,
            "filled_price": 0,
            "commission": 0,
        }

        self.orders[order_id] = order

        # 执行订单
        self._execute_order(order)

        return order_id

    def _execute_order(self, order: dict[str, Any]):
        """执行订单"""
        symbol = order["symbol"]
        quantity = order["quantity"]
        price = order["price"]

        # 计算实际成交价格（包含滑点）
        if order["type"] == "buy":
            actual_price = price * (1 + self.slippage)
        else:
            actual_price = price * (1 - self.slippage)

        # 计算交易成本
        trade_value = quantity * actual_price
        commission = trade_value * self.commission_rate

        # 检查资金是否足够
        if order["type"] == "buy":
            required_capital = trade_value + commission
            if required_capital > self.available_margin:
                order["status"] = OrderStatus.REJECTED
                order["rejection_reason"] = "Insufficient margin"
                return

        # 执行交易
        order["status"] = OrderStatus.FILLED
        order["filled_quantity"] = quantity
        order["filled_price"] = actual_price
        order["commission"] = commission

        # 更新持仓
        self._update_position(order)

        # 更新资金
        if order["type"] == "buy":
            self.available_margin -= trade_value + commission
        else:
            self.available_margin += trade_value - commission

        # 记录交易
        trade = {
            "timestamp": order["timestamp"],
            "symbol": symbol,
            "type": order["type"],
            "quantity": quantity,
            "price": actual_price,
            "commission": commission,
            "order_id": order["order_id"],
        }

        self.trades.append(trade)

        # 更新权益曲线
        self._update_equity_curve(order["timestamp"])

    def _update_position(self, order: dict[str, Any]):
        """更新持仓"""
        symbol = order["symbol"]
        quantity = order["filled_quantity"]
        price = order["filled_price"]

        if symbol not in self.positions:
            # 新建持仓
            self.positions[symbol] = {
                "symbol": symbol,
                "quantity": quantity if order["type"] == "buy" else -quantity,
                "entry_price": price,
                "entry_time": order["timestamp"],
                "market_value": quantity * price,
                "unrealized_pnl": 0,
                "realized_pnl": 0,
            }
        else:
            # 更新现有持仓
            position = self.positions[symbol]

            if order["type"] == "buy":
                # 增加持仓
                total_quantity = position["quantity"] + quantity
                total_value = (
                    position["quantity"] * position["entry_price"] + quantity * price
                )
                position["entry_price"] = total_value / total_quantity
                position["quantity"] = total_quantity
            else:
                # 减少持仓
                if position["quantity"] > quantity:
                    # 部分平仓
                    position["quantity"] -= quantity
                    position["realized_pnl"] += (
                        price - position["entry_price"]
                    ) * quantity
                else:
                    # 完全平仓
                    position["realized_pnl"] += (
                        price - position["entry_price"]
                    ) * position["quantity"]
                    position["quantity"] = 0

            # 更新市值
            position["market_value"] = position["quantity"] * price

        # 记录持仓历史
        self.position_history.append(
            {
                "timestamp": order["timestamp"],
                "symbol": symbol,
                "quantity": self.positions[symbol]["quantity"],
                "price": price,
                "action": order["type"],
            }
        )

    def _update_equity_curve(self, timestamp: datetime):
        """更新权益曲线"""
        current_equity = self.get_equity()

        self.equity_curve.append(
            {
                "timestamp": timestamp,
                "equity": current_equity,
                "cash": self.available_margin,
                "positions_value": current_equity - self.available_margin,
            }
        )

    def get_position(self, symbol: str) -> dict[str, Any] | None:
        """获取持仓"""
        return self.positions.get(symbol)

    def get_all_positions(self) -> dict[str, dict[str, Any]]:
        """获取所有持仓"""
        return self.positions.copy()

    def get_equity(self) -> float:
        """获取总权益"""
        return self.available_margin + sum(
            pos["market_value"] for pos in self.positions.values()
        )

    def get_available_capital(self) -> float:
        """获取可用资金"""
        return self.available_margin

    def get_margin_used(self) -> float:
        """获取已用保证金"""
        return self.margin_used

    def get_trade_history(self) -> list[dict[str, Any]]:
        """获取交易历史"""
        return self.trades.copy()

    def get_order_history(self) -> list[dict[str, Any]]:
        """获取订单历史"""
        return list(self.orders.values()) + self.order_history

    def get_equity_curve(self) -> pd.DataFrame:
        """获取权益曲线"""
        if not self.equity_curve:
            return pd.DataFrame()

        df = pd.DataFrame(self.equity_curve)
        df.set_index("timestamp", inplace=True)
        return df

    def get_performance_summary(self) -> dict[str, Any]:
        """获取绩效摘要"""
        total_trades = len(self.trades)
        if total_trades == 0:
            return {
                "total_trades": 0,
                "win_rate": 0,
                "total_return": 0,
                "current_equity": self.get_equity(),
            }

        # 计算盈亏
        buy_trades = [t for t in self.trades if t["type"] == "buy"]
        sell_trades = [t for t in self.trades if t["type"] == "sell"]

        # 计算已实现盈亏
        realized_pnl = sum(pos["realized_pnl"] for pos in self.positions.values())

        # 计算胜率
        winning_trades = sum(1 for t in self.trades if t.get("pnl", 0) > 0)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # 计算总收益
        total_return = (self.get_equity() - self.initial_capital) / self.initial_capital

        return {
            "total_trades": total_trades,
            "win_rate": win_rate,
            "total_return": total_return,
            "realized_pnl": realized_pnl,
            "current_equity": self.get_equity(),
            "initial_capital": self.initial_capital,
            "positions_count": len(
                [p for p in self.positions.values() if p["quantity"] != 0]
            ),
        }

    def update_market_prices(self, market_data: dict[str, float], timestamp: datetime):
        """更新市场价格"""
        for symbol, price in market_data.items():
            if symbol in self.positions:
                position = self.positions[symbol]
                if position["quantity"] != 0:
                    # 更新市值
                    position["market_value"] = position["quantity"] * price

                    # 更新未实现盈亏
                    if position["quantity"] > 0:
                        position["unrealized_pnl"] = (
                            price - position["entry_price"]
                        ) * position["quantity"]
                    else:
                        position["unrealized_pnl"] = (
                            position["entry_price"] - price
                        ) * abs(position["quantity"])

        # 更新权益曲线
        self._update_equity_curve(timestamp)

    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order["status"] == OrderStatus.PENDING:
                order["status"] = OrderStatus.CANCELLED
                self.order_history.append(order)
                del self.orders[order_id]
                return True
        return False

    def get_order(self, order_id: str) -> dict[str, Any] | None:
        """获取订单"""
        return self.orders.get(order_id)

    def get_status(self) -> dict[str, Any]:
        """获取状态"""
        return {
            "initial_capital": self.initial_capital,
            "current_equity": self.get_equity(),
            "available_margin": self.available_margin,
            "margin_used": self.margin_used,
            "positions_count": len(
                [p for p in self.positions.values() if p["quantity"] != 0]
            ),
            "pending_orders": len(
                [o for o in self.orders.values() if o["status"] == OrderStatus.PENDING]
            ),
            "total_trades": len(self.trades),
        }

    def reset(self):
        """重置经纪商"""
        self.current_capital = self.initial_capital
        self.positions.clear()
        self.position_history.clear()
        self.orders.clear()
        self.order_history.clear()
        self.trades.clear()
        self.equity_curve.clear()
        self.margin_used = 0
        self.available_margin = self.initial_capital
        self.next_order_id = 1
