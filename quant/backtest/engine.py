"""
Backtest Engine
"""

import logging
from datetime import datetime
from typing import Any

import pandas as pd

from ..strategies.base.strategy_base import StrategyBase
from .broker import BacktestBroker
from .metrics import BacktestMetrics


class BacktestEngine:
    """回测引擎"""

    def __init__(self, config: dict[str, Any] = None):
        self.config = config or {}
        self.initial_capital = (
            config.get("initial_capital", 100000) if config else 100000
        )
        self.commission_rate = config.get("commission_rate", 0.001) if config else 0.001
        self.slippage = config.get("slippage", 0.0001) if config else 0.0001

        # 组件
        self.broker = BacktestBroker(
            self.initial_capital, self.commission_rate, self.slippage
        )
        self.metrics_calculator = BacktestMetrics()

        # 状态
        self.strategies = {}
        self.is_running = False
        self.results = {}

        # 日志
        self.logger = logging.getLogger(__name__)

    def add_strategy(self, strategy: StrategyBase, name: str = None):
        """添加策略"""
        strategy_name = name or strategy.name
        self.strategies[strategy_name] = strategy
        self.logger.info(f"Added strategy: {strategy_name}")

    def run_backtest(
        self, data: pd.DataFrame, start_date: datetime = None, end_date: datetime = None
    ) -> dict[str, Any]:
        """
        运行回测

        Args:
            data: 历史数据
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            回测结果
        """
        if not self.strategies:
            raise ValueError("No strategies added for backtest")

        # 数据预处理
        processed_data = self._preprocess_data(data, start_date, end_date)

        if processed_data.empty:
            raise ValueError("No data available for backtest period")

        self.logger.info(f"Starting backtest with {len(self.strategies)} strategies")
        self.logger.info(
            f"Data range: {processed_data.index[0]} to {processed_data.index[-1]}"
        )

        # 初始化回测
        self._initialize_backtest()

        # 运行回测
        try:
            self.is_running = True
            results = self._execute_backtest(processed_data)
            self.is_running = False

            # 计算指标
            self._calculate_metrics(results)

            self.logger.info("Backtest completed successfully")
            return results

        except Exception as e:
            self.is_running = False
            self.logger.error(f"Backtest failed: {e}")
            raise

    def _preprocess_data(
        self, data: pd.DataFrame, start_date: datetime = None, end_date: datetime = None
    ) -> pd.DataFrame:
        """预处理数据"""
        processed_data = data.copy()

        # 确保时间索引
        if not isinstance(processed_data.index, pd.DatetimeIndex):
            raise ValueError("Data must have a datetime index")

        # 时间范围过滤
        if start_date:
            processed_data = processed_data[processed_data.index >= start_date]
        if end_date:
            processed_data = processed_data[processed_data.index <= end_date]

        # 数据清洗
        processed_data = processed_data.dropna()
        processed_data = processed_data.sort_index()

        return processed_data

    def _initialize_backtest(self):
        """初始化回测"""
        # 重置经纪商
        self.broker.reset()

        # 初始化所有策略
        for strategy_name, strategy in self.strategies.items():
            strategy.reset()
            strategy.start()

    def _execute_backtest(self, data: pd.DataFrame) -> dict[str, Any]:
        """执行回测"""
        results = {
            "strategies": {},
            "equity_curve": pd.DataFrame(),
            "trades": [],
            "summary": {},
        }

        # 初始化权益曲线
        equity_curve = pd.DataFrame(index=data.index)

        # 为每个策略运行回测
        for strategy_name, strategy in self.strategies.items():
            self.logger.info(f"Running backtest for strategy: {strategy_name}")

            strategy_results = self._run_single_strategy(strategy, data)
            results["strategies"][strategy_name] = strategy_results

            # 合并权益曲线
            equity_curve[strategy_name] = strategy_results["equity_curve"]

            # 收集交易记录
            results["trades"].extend(strategy_results["trades"])

        # 计算整体权益曲线
        equity_curve["total"] = equity_curve.mean(axis=1)
        results["equity_curve"] = equity_curve

        return results

    def _run_single_strategy(
        self, strategy: StrategyBase, data: pd.DataFrame
    ) -> dict[str, Any]:
        """运行单个策略回测"""
        # 创建策略专用的经纪商
        strategy_broker = BacktestBroker(
            self.initial_capital, self.commission_rate, self.slippage
        )

        # 初始化策略数据
        strategy_data = {
            "equity_curve": [],
            "trades": [],
            "positions": {},
            "signals": [],
        }

        # 逐个时间点处理
        for i, (timestamp, row) in enumerate(data.iterrows()):
            # 构建当前市场数据
            current_data = data.iloc[: i + 1]

            # 处理tick数据
            tick_data = {
                "symbol": data.columns[0] if len(data.columns) > 0 else "UNKNOWN",
                "price": row.get("price", row.iloc[0]) if len(row) > 0 else 0,
                "timestamp": timestamp,
                "volume": row.get("volume", 0),
                "high": row.get("high", row.get("price", 0)),
                "low": row.get("low", row.get("price", 0)),
                "open": row.get("open", row.get("price", 0)),
                "close": row.get("close", row.get("price", 0)),
            }

            # 更新策略
            strategy.on_tick(tick_data)

            # 处理K线数据
            if i > 0:
                strategy.on_bar(current_data)

            # 生成信号
            signals = strategy.generate_signals(current_data)
            strategy_data["signals"].extend(signals)

            # 执行交易
            self._execute_signals(strategy, signals, strategy_broker, tick_data)

            # 更新持仓
            self._update_positions(strategy, strategy_broker, tick_data)

            # 记录权益曲线
            current_equity = strategy_broker.get_equity()
            strategy_data["equity_curve"].append(
                {"timestamp": timestamp, "equity": current_equity}
            )

        # 收集交易记录
        strategy_data["trades"] = strategy_broker.get_trade_history()

        # 转换权益曲线为DataFrame
        equity_df = pd.DataFrame(strategy_data["equity_curve"])
        equity_df.set_index("timestamp", inplace=True)

        return {
            "equity_curve": equity_df,
            "trades": strategy_data["trades"],
            "signals": strategy_data["signals"],
            "final_equity": strategy_broker.get_equity(),
            "return_rate": (strategy_broker.get_equity() - self.initial_capital)
            / self.initial_capital,
        }

    def _execute_signals(
        self,
        strategy: StrategyBase,
        signals: list[dict[str, Any]],
        broker: BacktestBroker,
        tick_data: dict[str, Any],
    ):
        """执行交易信号"""
        for signal in signals:
            try:
                symbol = signal.get("symbol", "UNKNOWN")
                signal_type = signal.get("signal_type", "hold")
                quantity = signal.get("quantity", 0)
                price = signal.get("price", tick_data["price"])

                if signal_type == "buy":
                    # 买入信号
                    available_capital = broker.get_available_capital()
                    max_quantity = available_capital / price

                    if quantity > max_quantity:
                        quantity = max_quantity

                    if quantity > 0:
                        broker.buy(symbol, quantity, price, tick_data["timestamp"])

                elif signal_type == "sell":
                    # 卖出信号
                    position = broker.get_position(symbol)
                    if position and position["quantity"] > 0:
                        sell_quantity = min(quantity, position["quantity"])
                        broker.sell(
                            symbol, sell_quantity, price, tick_data["timestamp"]
                        )

            except Exception as e:
                self.logger.error(f"Error executing signal: {e}")

    def _update_positions(
        self, strategy: StrategyBase, broker: BacktestBroker, tick_data: dict[str, Any]
    ):
        """更新持仓"""
        # 获取当前价格
        current_price = tick_data["price"]

        # 更新所有持仓的市值
        positions = broker.get_all_positions()
        for symbol, position in positions.items():
            if position["quantity"] != 0:
                # 更新持仓市值
                market_value = position["quantity"] * current_price
                position["market_value"] = market_value

                # 更新未实现盈亏
                if position["quantity"] > 0:
                    unrealized_pnl = (
                        current_price - position["entry_price"]
                    ) * position["quantity"]
                else:
                    unrealized_pnl = (position["entry_price"] - current_price) * abs(
                        position["quantity"]
                    )

                position["unrealized_pnl"] = unrealized_pnl

    def _calculate_metrics(self, results: dict[str, Any]):
        """计算回测指标"""
        # 计算整体指标
        if "equity_curve" in results and not results["equity_curve"].empty:
            total_metrics = self.metrics_calculator.calculate_metrics(
                results["equity_curve"]["total"], results["trades"]
            )
            results["metrics"] = total_metrics

        # 计算每个策略的指标
        for strategy_name, strategy_results in results["strategies"].items():
            strategy_metrics = self.metrics_calculator.calculate_metrics(
                strategy_results["equity_curve"], strategy_results["trades"]
            )
            strategy_results["metrics"] = strategy_metrics

    def get_results(self) -> dict[str, Any]:
        """获取回测结果"""
        return self.results

    def save_results(self, filepath: str):
        """保存回测结果"""
        import pickle

        with open(filepath, "wb") as f:
            pickle.dump(self.results, f)

        self.logger.info(f"Results saved to {filepath}")

    def load_results(self, filepath: str):
        """加载回测结果"""
        import pickle

        with open(filepath, "rb") as f:
            self.results = pickle.load(f)

        self.logger.info(f"Results loaded from {filepath}")

    def get_strategy_comparison(self) -> pd.DataFrame:
        """获取策略对比"""
        if not self.results or "strategies" not in self.results:
            return pd.DataFrame()

        comparison_data = []

        for strategy_name, strategy_results in self.results["strategies"].items():
            if "metrics" in strategy_results:
                metrics = strategy_results["metrics"]
                comparison_data.append(
                    {
                        "strategy": strategy_name,
                        "total_return": metrics.get("total_return", 0),
                        "annual_return": metrics.get("annual_return", 0),
                        "sharpe_ratio": metrics.get("sharpe_ratio", 0),
                        "max_drawdown": metrics.get("max_drawdown", 0),
                        "win_rate": metrics.get("win_rate", 0),
                        "total_trades": metrics.get("total_trades", 0),
                    }
                )

        return pd.DataFrame(comparison_data)

    def reset(self):
        """重置回测引擎"""
        self.broker.reset()
        self.strategies.clear()
        self.results.clear()
        self.is_running = False

        for strategy in self.strategies.values():
            strategy.reset()

    def get_status(self) -> dict[str, Any]:
        """获取状态"""
        return {
            "is_running": self.is_running,
            "strategies_count": len(self.strategies),
            "broker_status": self.broker.get_status(),
            "results_available": bool(self.results),
        }
