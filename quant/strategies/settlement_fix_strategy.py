"""
结算修复策略 - 修正已结算信号的计算错误
"""

import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class SettlementFixStrategy:
    """结算修复策略类"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = db_path
    
    def recalculate_pnl(self, entry_price: float, exit_price: float, 
                       direction: str, suggested_bet: float) -> tuple[float, str]:
        """重新计算正确的盈亏和状态"""
        if direction == "LONG":
            # 做多：(出场价 - 入场价) / 入场价 * 投注金额
            price_change_pct = (exit_price - entry_price) / entry_price
            pnl = price_change_pct * suggested_bet
        else:  # SHORT
            # 做空：(入场价 - 出场价) / 入场价 * 投注金额
            price_change_pct = (entry_price - exit_price) / entry_price
            pnl = price_change_pct * suggested_bet
        
        status = "WIN" if pnl > 0 else "LOSS"
        return pnl, status
    
    def fix_all_settlements(self) -> Dict[str, Any]:
        """修复所有结算错误"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有已结算交易
            cursor.execute("""
                SELECT * FROM trade_history 
                WHERE status IN ('WIN', 'LOSS') 
                AND exit_price IS NOT NULL 
                AND entry_price IS NOT NULL
            """)
            
            trades = cursor.fetchall()
            fixed_count = 0
            
            for trade in trades:
                correct_pnl, correct_status = self.recalculate_pnl(
                    trade['entry_price'], 
                    trade['exit_price'],
                    trade['direction'], 
                    trade['suggested_bet']
                )
                
                # 检查是否需要修复
                if (abs(trade['pnl'] - correct_pnl) > 0.01 or 
                    trade['status'] != correct_status):
                    
                    cursor.execute("""
                        UPDATE trade_history 
                        SET pnl = ?, status = ?
                        WHERE id = ?
                    """, (correct_pnl, correct_status, trade['id']))
                    
                    fixed_count += 1
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'fixed_count': fixed_count,
                'total_trades': len(trades)
            }
            
        except Exception as e:
            logger.error(f"修复结算错误失败: {e}")
            return {'success': False, 'error': str(e)}
