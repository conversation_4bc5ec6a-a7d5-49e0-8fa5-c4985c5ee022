"""
Dollar Cost Averaging (DCA) Strategy
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any

import pandas as pd

from ..base.strategy_base import StrategyBase
from ..portfolio.position_manager import PositionManager, PositionType


class DCAStrategy(StrategyBase):
    """定投策略"""

    def __init__(self, name: str = "DCAStrategy", config: dict[str, Any] = None):
        super().__init__(name, config)

        # 定投参数
        self.investment_amount = config.get("investment_amount", 1000)  # 每次投资金额
        self.investment_frequency = config.get(
            "investment_frequency", "monthly"
        )  # 投资频率
        self.investment_day = config.get("investment_day", 1)  # 投资日期（月投）
        self.max_investments = config.get("max_investments", None)  # 最大投资次数

        # 投资标的管理
        self.symbols = config.get("symbols", [])  # 投资标的列表
        self.symbol_weights = config.get("symbol_weights", {})  # 标的权重

        # 状态变量
        self.position_manager = PositionManager(config.get("initial_capital", 100000))
        self.investment_count = 0
        self.last_investment_time = None
        self.investment_history = []

    def initialize(self):
        """初始化策略"""
        if not self.symbols:
            raise ValueError("Investment symbols must be specified")

        # 计算标的权重
        if not self.symbol_weights:
            equal_weight = 1.0 / len(self.symbols)
            self.symbol_weights = dict.fromkeys(self.symbols, equal_weight)

        # 验证权重总和
        total_weight = sum(self.symbol_weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValueError("Symbol weights must sum to 1.0")

    def on_tick(self, tick_data: dict[str, Any]):
        """处理实时行情数据"""
        symbol = tick_data.get("symbol")
        price = tick_data.get("price")
        timestamp = tick_data.get("timestamp", datetime.now())

        if not symbol or not price:
            return

        # 更新现有持仓
        market_data = {symbol: price}
        self.position_manager.update_positions(market_data, timestamp)

        # 检查是否需要执行定投
        if self._should_invest(timestamp):
            self._execute_investment(timestamp, {symbol: price})

    def on_bar(self, bar_data: pd.DataFrame):
        """处理K线数据"""
        # 获取所有标的价格
        prices = {}
        for symbol in self.symbols:
            if symbol in bar_data.columns:
                prices[symbol] = bar_data[symbol].iloc[-1]

        if prices:
            timestamp = bar_data.index[-1]

            # 更新持仓
            self.position_manager.update_positions(prices, timestamp)

            # 检查定投
            if self._should_invest(timestamp):
                self._execute_investment(timestamp, prices)

    def generate_signals(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """生成交易信号"""
        signals = []

        if self._should_invest(data.index[-1]):
            # 获取当前价格
            prices = {}
            for symbol in self.symbols:
                if symbol in data.columns:
                    prices[symbol] = data[symbol].iloc[-1]

            if prices:
                # 生成定投信号
                for symbol, weight in self.symbol_weights.items():
                    if symbol in prices:
                        amount = self.investment_amount * weight
                        quantity = amount / prices[symbol]

                        signals.append(
                            {
                                "symbol": symbol,
                                "signal_type": "buy",
                                "price": prices[symbol],
                                "quantity": quantity,
                                "timestamp": data.index[-1],
                                "reason": f"DCA investment #{self.investment_count + 1}",
                                "strength": 1,
                            }
                        )

        return signals

    def _should_invest(self, timestamp: datetime) -> bool:
        """判断是否应该执行定投"""
        # 检查是否达到最大投资次数
        if self.max_investments and self.investment_count >= self.max_investments:
            return False

        # 检查投资频率
        if self.last_investment_time is None:
            return True

        if self.investment_frequency == "daily":
            # 每日定投
            return (timestamp - self.last_investment_time) >= timedelta(days=1)

        elif self.investment_frequency == "weekly":
            # 每周定投
            return (timestamp - self.last_investment_time) >= timedelta(weeks=1)

        elif self.investment_frequency == "monthly":
            # 每月定投
            if timestamp.month != self.last_investment_time.month:
                return timestamp.day >= self.investment_day
            return False

        elif self.investment_frequency == "yearly":
            # 每年定投
            return (timestamp - self.last_investment_time) >= timedelta(days=365)

        return False

    def _execute_investment(self, timestamp: datetime, prices: dict[str, float]):
        """执行定投"""
        if not prices:
            return

        # 计算每个标的的投资金额
        investments = {}
        for symbol, weight in self.symbol_weights.items():
            if symbol in prices:
                amount = self.investment_amount * weight
                quantity = amount / prices[symbol]
                investments[symbol] = {
                    "amount": amount,
                    "quantity": quantity,
                    "price": prices[symbol],
                }

        # 执行投资
        for symbol, investment in investments.items():
            success = self.position_manager.open_position(
                symbol=symbol,
                position_type=PositionType.LONG,
                quantity=investment["quantity"],
                price=investment["price"],
                timestamp=timestamp,
            )

            if success:
                # 记录投资
                investment_record = {
                    "timestamp": timestamp,
                    "investment_number": self.investment_count + 1,
                    "symbol": symbol,
                    "amount": investment["amount"],
                    "quantity": investment["quantity"],
                    "price": investment["price"],
                    "total_invested": sum(r["amount"] for r in self.investment_history),
                }

                self.investment_history.append(investment_record)

        # 更新状态
        self.investment_count += 1
        self.last_investment_time = timestamp

    def get_investment_summary(self) -> dict[str, Any]:
        """获取定投摘要"""
        total_invested = sum(record["amount"] for record in self.investment_history)

        # 计算平均成本
        if self.investment_history:
            total_quantity = sum(
                record["quantity"] for record in self.investment_history
            )
            average_cost = total_invested / total_quantity if total_quantity > 0 else 0
        else:
            average_cost = 0

        # 计算当前价值
        current_value = 0
        positions = self.position_manager.get_all_positions()
        for symbol, position in positions.items():
            if position.status.value == "open":
                current_value += position.quantity * position.entry_price

        return {
            "investment_count": self.investment_count,
            "total_invested": total_invested,
            "average_cost": average_cost,
            "current_value": current_value,
            "unrealized_pnl": current_value - total_invested,
            "return_rate": (
                (current_value - total_invested) / total_invested
                if total_invested > 0
                else 0
            ),
            "portfolio_summary": self.position_manager.get_positions_summary(),
            "next_investment_time": self._get_next_investment_time(),
        }

    def _get_next_investment_time(self) -> datetime | None:
        """获取下次定投时间"""
        if self.max_investments and self.investment_count >= self.max_investments:
            return None

        if self.last_investment_time is None:
            return datetime.now()

        if self.investment_frequency == "daily":
            return self.last_investment_time + timedelta(days=1)
        elif self.investment_frequency == "weekly":
            return self.last_investment_time + timedelta(weeks=1)
        elif self.investment_frequency == "monthly":
            # 计算下个月的投资日期
            next_month = self.last_investment_time.replace(day=1) + timedelta(days=32)
            next_month = next_month.replace(day=min(self.investment_day, 28))
            return next_month
        elif self.investment_frequency == "yearly":
            return self.last_investment_time + timedelta(days=365)

        return None

    def get_investment_history(self) -> list[dict[str, Any]]:
        """获取投资历史"""
        return self.investment_history.copy()

    def reset(self):
        """重置策略"""
        super().reset()
        self.position_manager.reset()
        self.investment_count = 0
        self.last_investment_time = None
        self.investment_history = []
