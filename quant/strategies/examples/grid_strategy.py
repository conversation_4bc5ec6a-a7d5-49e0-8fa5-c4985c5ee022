"""
Grid Strategy
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import pandas as pd

from ..base.strategy_base import StrategyBase
from ..portfolio.position_manager import PositionManager, PositionType


class GridStrategy(StrategyBase):
    """网格策略"""

    def __init__(self, name: str = "GridStrategy", config: dict[str, Any] = None):
        super().__init__(name, config)

        # 网格参数
        self.grid_spacing = (
            config.get("grid_spacing", 0.02) if config else 0.02
        )  # 2%间距
        self.grid_levels = config.get("grid_levels", 10) if config else 10  # 10个网格
        self.base_price = config.get("base_price", None)  # 基准价格
        self.rebalance_threshold = config.get(
            "rebalance_threshold", 0.01
        )  # 1%再平衡阈值

        # 仓位管理
        self.position_manager = PositionManager(config.get("initial_capital", 100000))
        self.grid_positions = {}  # 价格 -> 仓位信息

        # 状态变量
        self.last_rebalance_time = None
        self.current_price = None

    def initialize(self):
        """初始化策略"""
        if self.base_price is None:
            raise ValueError("Base price must be set for grid strategy")

        # 初始化网格
        self._initialize_grid()

    def _initialize_grid(self):
        """初始化网格"""
        self.grid_positions = {}

        # 创建网格价格点
        for i in range(-self.grid_levels, self.grid_levels + 1):
            if i == 0:
                continue  # 跳过基准价格

            grid_price = self.base_price * (1 + i * self.grid_spacing)
            self.grid_positions[grid_price] = {
                "position_type": PositionType.LONG if i < 0 else PositionType.SHORT,
                "quantity": self._calculate_grid_quantity(),
                "filled": False,
                "entry_time": None,
            }

    def _calculate_grid_quantity(self) -> float:
        """计算网格仓位大小"""
        total_capital = self.position_manager.initial_capital
        capital_per_grid = total_capital / (self.grid_levels * 2)

        if self.base_price:
            return capital_per_grid / self.base_price
        return 0.0

    def on_tick(self, tick_data: dict[str, Any]):
        """处理实时行情数据"""
        symbol = tick_data.get("symbol")
        price = tick_data.get("price")
        timestamp = tick_data.get("timestamp", datetime.now())

        if not symbol or not price:
            return

        self.current_price = price

        # 更新现有持仓
        market_data = {symbol: price}
        self.position_manager.update_positions(market_data, timestamp)

        # 检查网格触发
        self._check_grid_triggers(symbol, price, timestamp)

        # 检查再平衡
        self._check_rebalance(timestamp)

    def on_bar(self, bar_data: pd.DataFrame):
        """处理K线数据"""
        if "price" not in bar_data.columns:
            return

        self.current_price = bar_data["price"].iloc[-1]

        # 可以基于K线数据进行更复杂的分析
        # 例如：波动率分析、趋势判断等

    def generate_signals(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """生成交易信号"""
        signals = []

        if len(data) < 2:
            return signals

        current_price = data["price"].iloc[-1]
        prev_price = data["price"].iloc[-2]

        # 检查价格穿越网格线
        for grid_price, grid_info in self.grid_positions.items():
            if grid_info["filled"]:
                continue

            # 检查是否触发网格
            if (prev_price < grid_price < current_price) or (
                prev_price > grid_price > current_price
            ):
                signals.append(
                    {
                        "symbol": data.get("symbol", "UNKNOWN"),
                        "signal_type": (
                            "buy"
                            if grid_info["position_type"] == PositionType.LONG
                            else "sell"
                        ),
                        "price": grid_price,
                        "quantity": grid_info["quantity"],
                        "timestamp": data.index[-1],
                        "reason": f"Grid trigger at {grid_price}",
                        "strength": 2,
                    }
                )

        return signals

    def _check_grid_triggers(self, symbol: str, price: float, timestamp: datetime):
        """检查网格触发"""
        for grid_price, grid_info in self.grid_positions.items():
            if grid_info["filled"]:
                continue

            # 检查是否达到网格价格
            if (
                grid_info["position_type"] == PositionType.LONG and price <= grid_price
            ) or (
                grid_info["position_type"] == PositionType.SHORT and price >= grid_price
            ):

                # 执行网格交易
                success = self.position_manager.open_position(
                    symbol=symbol,
                    position_type=grid_info["position_type"],
                    quantity=grid_info["quantity"],
                    price=price,
                    timestamp=timestamp,
                )

                if success:
                    grid_info["filled"] = True
                    grid_info["entry_time"] = timestamp
                    grid_info["entry_price"] = price

    def _check_rebalance(self, timestamp: datetime):
        """检查再平衡"""
        if self.last_rebalance_time is None:
            self.last_rebalance_time = timestamp
            return

        # 检查是否达到再平衡时间间隔
        time_diff = timestamp - self.last_rebalance_time
        if time_diff < timedelta(hours=1):  # 每小时检查一次
            return

        # 检查价格偏离基准价格的程度
        if self.current_price and self.base_price:
            price_deviation = (
                abs(self.current_price - self.base_price) / self.base_price
            )

            if price_deviation > self.rebalance_threshold:
                self._rebalance_grid(timestamp)
                self.last_rebalance_time = timestamp

    def _rebalance_grid(self, timestamp: datetime):
        """重新平衡网格"""
        # 更新基准价格为当前价格
        self.base_price = self.current_price

        # 平仓所有现有持仓
        positions = self.position_manager.get_all_positions()
        for symbol, position in positions.items():
            if position.status.value == "open":
                self.position_manager.close_position(
                    symbol=symbol, price=self.current_price, timestamp=timestamp
                )

        # 重新初始化网格
        self._initialize_grid()

    def get_grid_status(self) -> dict[str, Any]:
        """获取网格状态"""
        filled_grids = sum(1 for info in self.grid_positions.values() if info["filled"])
        total_grids = len(self.grid_positions)

        return {
            "base_price": self.base_price,
            "current_price": self.current_price,
            "grid_spacing": self.grid_spacing,
            "grid_levels": self.grid_levels,
            "filled_grids": filled_grids,
            "total_grids": total_grids,
            "fill_rate": filled_grids / total_grids if total_grids > 0 else 0,
            "portfolio_summary": self.position_manager.get_positions_summary(),
        }

    def reset(self):
        """重置策略"""
        super().reset()
        self.position_manager.reset()
        self.grid_positions = {}
        self.last_rebalance_time = None
        self.current_price = None
