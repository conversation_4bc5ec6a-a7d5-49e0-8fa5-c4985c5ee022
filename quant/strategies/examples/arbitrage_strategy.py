"""
Arbitrage Strategy
"""

from datetime import datetime, timed<PERSON>ta
from typing import Any

import pandas as pd

from ..base.strategy_base import StrategyBase
from ..portfolio.position_manager import PositionManager, PositionType
from ..signals.arbitrage_signals import ArbitrageSignalGenerator


class ArbitrageStrategy(StrategyBase):
    """套利策略"""

    def __init__(self, name: str = "ArbitrageStrategy", config: dict[str, Any] = None):
        super().__init__(name, config)

        # 套利参数
        self.spread_threshold = config.get("spread_threshold", 0.01)  # 价差阈值
        self.correlation_threshold = config.get(
            "correlation_threshold", 0.8
        )  # 相关性阈值
        self.min_profit_threshold = config.get(
            "min_profit_threshold", 0.005
        )  # 最小盈利阈值
        self.max_holding_period = config.get(
            "max_holding_period", timedelta(hours=24)
        )  # 最大持仓时间

        # 交易对配置
        self.trading_pairs = config.get("trading_pairs", [])  # 交易对列表
        self.pair_configs = config.get("pair_configs", {})  # 交易对配置

        # 组件
        self.position_manager = PositionManager(config.get("initial_capital", 100000))
        self.signal_generator = ArbitrageSignalGenerator(
            "ArbitrageSignalGenerator", config
        )

        # 状态变量
        self.active_arbitrages = {}  # 活跃套利
        self.arbitrage_history = []
        self.price_history = {}

    def initialize(self):
        """初始化策略"""
        if not self.trading_pairs:
            raise ValueError("Trading pairs must be specified")

        # 初始化价格历史
        for pair in self.trading_pairs:
            self.price_history[pair] = pd.DataFrame()

    def on_tick(self, tick_data: dict[str, Any]):
        """处理实时行情数据"""
        symbol = tick_data.get("symbol")
        price = tick_data.get("price")
        timestamp = tick_data.get("timestamp", datetime.now())

        if not symbol or not price:
            return

        # 更新价格历史
        self._update_price_history(symbol, price, timestamp)

        # 更新现有持仓
        market_data = {symbol: price}
        self.position_manager.update_positions(market_data, timestamp)

        # 检查套利机会
        self._check_arbitrage_opportunities(timestamp)

        # 检查套利平仓
        self._check_arbitrage_exits(timestamp)

    def on_bar(self, bar_data: pd.DataFrame):
        """处理K线数据"""
        timestamp = bar_data.index[-1]

        # 更新所有标的价格
        prices = {}
        for symbol in self._get_all_symbols():
            if symbol in bar_data.columns:
                prices[symbol] = bar_data[symbol].iloc[-1]
                self._update_price_history(symbol, prices[symbol], timestamp)

        if prices:
            # 更新持仓
            self.position_manager.update_positions(prices, timestamp)

            # 检查套利机会
            self._check_arbitrage_opportunities(timestamp)

            # 检查套利平仓
            self._check_arbitrage_exits(timestamp)

    def generate_signals(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """生成交易信号"""
        signals = []

        # 使用信号生成器
        arbitrage_signals = self.signal_generator.calculate(data)
        signals.extend(arbitrage_signals)

        return signals

    def _update_price_history(self, symbol: str, price: float, timestamp: datetime):
        """更新价格历史"""
        if symbol not in self.price_history:
            self.price_history[symbol] = pd.DataFrame()

        # 添加新价格
        new_data = pd.DataFrame({"price": [price]}, index=[timestamp])
        self.price_history[symbol] = pd.concat([self.price_history[symbol], new_data])

        # 限制历史长度
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol].iloc[-1000:]

    def _check_arbitrage_opportunities(self, timestamp: datetime):
        """检查套利机会"""
        for pair in self.trading_pairs:
            if len(pair) != 2:
                continue

            symbol1, symbol2 = pair

            # 检查是否已经有活跃套利
            pair_key = f"{symbol1}_{symbol2}"
            if pair_key in self.active_arbitrages:
                continue

            # 检查价格历史是否足够
            if (
                symbol1 not in self.price_history
                or symbol2 not in self.price_history
                or len(self.price_history[symbol1]) < 20
                or len(self.price_history[symbol2]) < 20
            ):
                continue

            # 计算套利机会
            opportunity = self._calculate_arbitrage_opportunity(
                symbol1, symbol2, timestamp
            )

            if (
                opportunity
                and opportunity["expected_profit"] > self.min_profit_threshold
            ):
                self._execute_arbitrage(opportunity, timestamp)

    def _calculate_arbitrage_opportunity(
        self, symbol1: str, symbol2: str, timestamp: datetime
    ) -> dict[str, Any] | None:
        """计算套利机会"""
        prices1 = self.price_history[symbol1]["price"]
        prices2 = self.price_history[symbol2]["price"]

        # 计算价差
        spread = prices1 - prices2
        spread_mean = spread.rolling(window=20).mean()
        spread_std = spread.rolling(window=20).std()

        if len(spread_mean) == 0 or spread_mean.iloc[-1] == 0:
            return None

        # 计算Z-score
        z_score = (spread.iloc[-1] - spread_mean.iloc[-1]) / spread_std.iloc[-1]

        # 计算相关性
        correlation = prices1.corr(prices2)

        # 检查套利条件
        if abs(z_score) > 2.0 and abs(correlation) > self.correlation_threshold:
            # 计算预期利润
            expected_reversion = spread_mean.iloc[-1] - spread.iloc[-1]
            expected_profit = abs(expected_reversion) / prices1.iloc[-1]

            return {
                "symbol1": symbol1,
                "symbol2": symbol2,
                "current_spread": spread.iloc[-1],
                "mean_spread": spread_mean.iloc[-1],
                "z_score": z_score,
                "correlation": correlation,
                "expected_profit": expected_profit,
                "direction": "long" if z_score < -2.0 else "short",
                "timestamp": timestamp,
            }

        return None

    def _execute_arbitrage(self, opportunity: dict[str, Any], timestamp: datetime):
        """执行套利"""
        symbol1 = opportunity["symbol1"]
        symbol2 = opportunity["symbol2"]
        direction = opportunity["direction"]

        # 获取当前价格
        price1 = self.price_history[symbol1]["price"].iloc[-1]
        price2 = self.price_history[symbol2]["price"].iloc[-1]

        # 计算仓位大小
        available_capital = self.position_manager.current_capital
        position_size = available_capital * 0.1  # 使用10%资金

        if direction == "long":
            # 做多价差：买入symbol1，卖出symbol2
            quantity1 = position_size / price1
            quantity2 = position_size / price2

            # 开仓
            success1 = self.position_manager.open_position(
                symbol=symbol1,
                position_type=PositionType.LONG,
                quantity=quantity1,
                price=price1,
                timestamp=timestamp,
            )

            success2 = self.position_manager.open_position(
                symbol=symbol2,
                position_type=PositionType.SHORT,
                quantity=quantity2,
                price=price2,
                timestamp=timestamp,
            )

            if success1 and success2:
                # 记录套利
                pair_key = f"{symbol1}_{symbol2}"
                self.active_arbitrages[pair_key] = {
                    "symbol1": symbol1,
                    "symbol2": symbol2,
                    "direction": direction,
                    "entry_spread": opportunity["current_spread"],
                    "entry_time": timestamp,
                    "entry_price1": price1,
                    "entry_price2": price2,
                    "quantity1": quantity1,
                    "quantity2": quantity2,
                    "position_size": position_size,
                }

        else:
            # 做空价差：卖出symbol1，买入symbol2
            quantity1 = position_size / price1
            quantity2 = position_size / price2

            # 开仓
            success1 = self.position_manager.open_position(
                symbol=symbol1,
                position_type=PositionType.SHORT,
                quantity=quantity1,
                price=price1,
                timestamp=timestamp,
            )

            success2 = self.position_manager.open_position(
                symbol=symbol2,
                position_type=PositionType.LONG,
                quantity=quantity2,
                price=price2,
                timestamp=timestamp,
            )

            if success1 and success2:
                # 记录套利
                pair_key = f"{symbol1}_{symbol2}"
                self.active_arbitrages[pair_key] = {
                    "symbol1": symbol1,
                    "symbol2": symbol2,
                    "direction": direction,
                    "entry_spread": opportunity["current_spread"],
                    "entry_time": timestamp,
                    "entry_price1": price1,
                    "entry_price2": price2,
                    "quantity1": quantity1,
                    "quantity2": quantity2,
                    "position_size": position_size,
                }

    def _check_arbitrage_exits(self, timestamp: datetime):
        """检查套利平仓"""
        for pair_key, arbitrage in list(self.active_arbitrages.items()):
            symbol1 = arbitrage["symbol1"]
            symbol2 = arbitrage["symbol2"]

            # 检查最大持仓时间
            if timestamp - arbitrage["entry_time"] > self.max_holding_period:
                self._close_arbitrage(pair_key, timestamp, "Time limit")
                continue

            # 检查价差回归
            if (
                symbol1 in self.price_history
                and symbol2 in self.price_history
                and len(self.price_history[symbol1]) > 0
                and len(self.price_history[symbol2]) > 0
            ):

                current_price1 = self.price_history[symbol1]["price"].iloc[-1]
                current_price2 = self.price_history[symbol2]["price"].iloc[-1]
                current_spread = current_price1 - current_price2

                # 检查是否达到盈利目标
                expected_profit = (
                    abs(current_spread - arbitrage["entry_spread"])
                    / arbitrage["entry_price1"]
                )

                if expected_profit > self.min_profit_threshold:
                    self._close_arbitrage(pair_key, timestamp, "Profit target")
                    continue

                # 检查止损
                if expected_profit < -self.min_profit_threshold:
                    self._close_arbitrage(pair_key, timestamp, "Stop loss")
                    continue

    def _close_arbitrage(self, pair_key: str, timestamp: datetime, reason: str):
        """平仓套利"""
        if pair_key not in self.active_arbitrages:
            return

        arbitrage = self.active_arbitrages[pair_key]
        symbol1 = arbitrage["symbol1"]
        symbol2 = arbitrage["symbol2"]

        # 获取当前价格
        current_price1 = self.price_history[symbol1]["price"].iloc[-1]
        current_price2 = self.price_history[symbol2]["price"].iloc[-1]

        # 计算盈亏
        if arbitrage["direction"] == "long":
            pnl1 = (current_price1 - arbitrage["entry_price1"]) * arbitrage["quantity1"]
            pnl2 = (arbitrage["entry_price2"] - current_price2) * arbitrage["quantity2"]
        else:
            pnl1 = (arbitrage["entry_price1"] - current_price1) * arbitrage["quantity1"]
            pnl2 = (current_price2 - arbitrage["entry_price2"]) * arbitrage["quantity2"]

        total_pnl = pnl1 + pnl2

        # 平仓
        self.position_manager.close_position(symbol1, current_price1, timestamp)
        self.position_manager.close_position(symbol2, current_price2, timestamp)

        # 记录套利历史
        arbitrage_record = {
            "pair_key": pair_key,
            "symbol1": symbol1,
            "symbol2": symbol2,
            "direction": arbitrage["direction"],
            "entry_time": arbitrage["entry_time"],
            "exit_time": timestamp,
            "entry_spread": arbitrage["entry_spread"],
            "exit_spread": current_price1 - current_price2,
            "pnl": total_pnl,
            "return_rate": total_pnl / arbitrage["position_size"],
            "reason": reason,
        }

        self.arbitrage_history.append(arbitrage_record)

        # 移除活跃套利
        del self.active_arbitrages[pair_key]

    def _get_all_symbols(self) -> list[str]:
        """获取所有交易标的"""
        symbols = set()
        for pair in self.trading_pairs:
            if isinstance(pair, (list, tuple)) and len(pair) == 2:
                symbols.add(pair[0])
                symbols.add(pair[1])
        return list(symbols)

    def get_arbitrage_summary(self) -> dict[str, Any]:
        """获取套利摘要"""
        total_pnl = sum(record["pnl"] for record in self.arbitrage_history)

        return {
            "active_arbitrages": len(self.active_arbitrages),
            "total_arbitrages": len(self.arbitrage_history),
            "total_pnl": total_pnl,
            "win_rate": (
                len([r for r in self.arbitrage_history if r["pnl"] > 0])
                / len(self.arbitrage_history)
                if self.arbitrage_history
                else 0
            ),
            "average_pnl": (
                total_pnl / len(self.arbitrage_history) if self.arbitrage_history else 0
            ),
            "portfolio_summary": self.position_manager.get_positions_summary(),
        }

    def get_arbitrage_history(self) -> list[dict[str, Any]]:
        """获取套利历史"""
        return self.arbitrage_history.copy()

    def reset(self):
        """重置策略"""
        super().reset()
        self.position_manager.reset()
        self.active_arbitrages = {}
        self.arbitrage_history = []
        self.price_history = {}
