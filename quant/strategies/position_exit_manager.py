"""
Position Exit Manager Module

管理持仓的多种退出条件：
1. 固定时间退出（10分钟）
2. 止盈止损退出
3. 信号反转退出
4. 强制平仓
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
import asyncio

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger
from quant.config_manager import config

logger = get_logger(__name__)


class PositionExitManager:
    """管理持仓退出策略"""
    
    def __init__(self):
        # 从配置加载参数
        exit_cfg = config.get("POSITION_EXIT", {}) or {}
        
        # 止盈止损参数
        self.take_profit_pct = float(exit_cfg.get("take_profit_pct", 0.02))  # 2%止盈
        self.stop_loss_pct = float(exit_cfg.get("stop_loss_pct", 0.01))  # 1%止损
        
        # 时间退出参数
        self.min_hold_minutes = int(exit_cfg.get("min_hold_minutes", 5))  # 最少持仓5分钟
        self.max_hold_minutes = int(exit_cfg.get("max_hold_minutes", 30))  # 最多持仓30分钟
        self.default_exit_minutes = int(exit_cfg.get("default_exit_minutes", 10))  # 默认10分钟退出
        
        # 信号反转退出
        self.enable_signal_reversal_exit = bool(exit_cfg.get("enable_signal_reversal_exit", True))
        
        # 检查频率
        self.check_interval_seconds = int(exit_cfg.get("check_interval_seconds", 10))  # 每10秒检查一次
        
        # 活跃监控的持仓
        self._monitored_positions: Dict[int, Dict[str, Any]] = {}
        self._monitoring_tasks: Dict[int, asyncio.Task] = {}
    
    async def start_monitoring(self, trade: Dict[str, Any]) -> None:
        """开始监控一个新的持仓"""
        trade_id = trade["id"]
        
        if trade_id in self._monitored_positions:
            logger.warning(f"Trade {trade_id} already being monitored")
            return
        
        # 记录持仓信息
        self._monitored_positions[trade_id] = {
            "trade": trade,
            "entry_time": datetime.utcnow(),
            "entry_price": float(trade["entry_price"]),
            "direction": trade["direction"],
            "symbol": trade.get("symbol", "BTCUSDT"),
            "size_usdt": float(trade.get("suggested_bet", 0)),
            "checks_count": 0,
            "exit_triggered": False
        }
        
        # 启动监控任务
        task = asyncio.create_task(self._monitor_position(trade_id))
        self._monitoring_tasks[trade_id] = task
        
        logger.info(f"Started monitoring trade {trade_id}: {trade['direction']} at ${trade['entry_price']}")
    
    async def _monitor_position(self, trade_id: int) -> None:
        """监控单个持仓的退出条件"""
        try:
            while trade_id in self._monitored_positions:
                position_info = self._monitored_positions[trade_id]
                
                if position_info["exit_triggered"]:
                    break
                
                # 增加检查计数
                position_info["checks_count"] += 1
                
                # 获取当前价格
                current_price = await binance_client.get_current_price()
                current_time = datetime.utcnow()
                
                # 计算持仓时间和盈亏
                time_held = (current_time - position_info["entry_time"]).total_seconds() / 60
                entry_price = position_info["entry_price"]
                direction = position_info["direction"]
                
                if direction == "LONG":
                    pnl_pct = (current_price - entry_price) / entry_price
                else:  # SHORT
                    pnl_pct = (entry_price - current_price) / entry_price
                
                # 检查退出条件
                exit_reason = None
                
                # 1. 检查止盈
                if pnl_pct >= self.take_profit_pct:
                    exit_reason = f"TAKE_PROFIT ({pnl_pct:.2%})"
                
                # 2. 检查止损
                elif pnl_pct <= -self.stop_loss_pct:
                    exit_reason = f"STOP_LOSS ({pnl_pct:.2%})"
                
                # 3. 检查最大持仓时间
                elif time_held >= self.max_hold_minutes:
                    exit_reason = f"MAX_TIME ({time_held:.1f} minutes)"
                
                # 4. 检查默认退出时间（10分钟）
                elif time_held >= self.default_exit_minutes and time_held >= self.min_hold_minutes:
                    # 只在没有明显趋势时退出
                    if abs(pnl_pct) < 0.005:  # 盈亏小于0.5%
                        exit_reason = f"DEFAULT_TIME ({time_held:.1f} minutes)"
                
                # 5. 检查信号反转（需要查询最新信号）
                if self.enable_signal_reversal_exit and time_held >= self.min_hold_minutes:
                    latest_signal = await self._check_signal_reversal(position_info["symbol"], direction)
                    if latest_signal:
                        exit_reason = f"SIGNAL_REVERSAL (new: {latest_signal})"
                
                # 执行退出
                if exit_reason:
                    logger.info(f"Triggering exit for trade {trade_id}: {exit_reason}")
                    position_info["exit_triggered"] = True
                    
                    # 执行平仓
                    await self._execute_exit(trade_id, current_price, exit_reason)
                    break
                
                # 记录监控日志（每10次检查记录一次）
                if position_info["checks_count"] % 10 == 0:
                    logger.debug(f"Monitoring trade {trade_id}: time={time_held:.1f}min, pnl={pnl_pct:.2%}, price=${current_price:,.2f}")
                
                # 等待下次检查
                await asyncio.sleep(self.check_interval_seconds)
                
        except Exception as e:
            logger.error(f"Error monitoring trade {trade_id}: {e}")
        finally:
            # 清理监控记录
            if trade_id in self._monitored_positions:
                del self._monitored_positions[trade_id]
            if trade_id in self._monitoring_tasks:
                del self._monitoring_tasks[trade_id]
    
    async def _check_signal_reversal(self, symbol: str, current_direction: str) -> Optional[str]:
        """检查是否有反向信号"""
        try:
            # 查询最近的信号
            recent_trades = db.get_recent_trades(symbol, limit=1)
            if recent_trades:
                latest_trade = recent_trades[0]
                latest_direction = latest_trade.get("direction")
                
                # 如果最新信号与当前持仓方向相反
                if latest_direction and latest_direction != current_direction:
                    signal_time = latest_trade.get("signal_timestamp")
                    if isinstance(signal_time, str):
                        signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
                    
                    # 确保信号是最近的（5分钟内）
                    if (datetime.utcnow() - signal_time).total_seconds() < 300:
                        return latest_direction
            
            return None
        except Exception as e:
            logger.error(f"Error checking signal reversal: {e}")
            return None
    
    async def _execute_exit(self, trade_id: int, exit_price: float, exit_reason: str) -> None:
        """执行平仓操作"""
        try:
            position_info = self._monitored_positions.get(trade_id)
            if not position_info:
                return
            
            trade = position_info["trade"]
            direction = position_info["direction"]
            size_usdt = position_info["size_usdt"]
            symbol = position_info["symbol"]
            
            # 执行平仓订单
            close_side = "SELL" if direction == "LONG" else "BUY"
            position_side = "LONG" if direction == "LONG" else "SHORT"
            
            try:
                close_order_resp = await binance_client.place_market_order_futures(
                    symbol=symbol,
                    side=close_side,
                    notional_usdt=size_usdt,
                    position_side=position_side,
                    reduce_only=True,
                )
                logger.info(f"Executed exit order for trade {trade_id}: {close_side} {size_usdt} USDT")
            except Exception as e:
                logger.error(f"Failed to execute exit order: {e}")
                close_order_resp = {"error": str(e)}
            
            # 计算盈亏
            entry_price = position_info["entry_price"]
            if direction == "LONG":
                pnl = (exit_price - entry_price) * (size_usdt / entry_price)
                pnl_pct = (exit_price - entry_price) / entry_price
            else:
                pnl = (entry_price - exit_price) * (size_usdt / entry_price)
                pnl_pct = (entry_price - exit_price) / entry_price
            
            # 更新数据库
            settlement_result = {
                "exit_price": exit_price,
                "exit_timestamp": datetime.utcnow(),
                "pnl": pnl,
                "pnl_percentage": pnl_pct,
                "status": "WIN" if pnl > 0 else "LOSS",
                "exit_reason": exit_reason,
                "exchange_response": close_order_resp
            }
            
            db.update_trade_result(trade_id, settlement_result)
            
            # 发送通知
            notification_manager.send_enhanced_settlement_notification({
                **settlement_result,
                "trade_id": trade_id,
                "symbol": symbol,
                "direction": direction,
                "entry_price": entry_price,
                "suggested_bet": size_usdt
            })
            
            logger.info(f"Trade {trade_id} exited: {exit_reason}, PnL: ${pnl:.2f} ({pnl_pct:.2%})")
            
        except Exception as e:
            logger.error(f"Error executing exit for trade {trade_id}: {e}")
    
    async def force_exit_all(self) -> None:
        """强制平仓所有持仓"""
        logger.warning("Force exiting all positions")
        
        tasks = []
        for trade_id in list(self._monitored_positions.keys()):
            current_price = await binance_client.get_current_price()
            tasks.append(self._execute_exit(trade_id, current_price, "FORCE_EXIT"))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def stop_monitoring(self, trade_id: int) -> None:
        """停止监控指定持仓"""
        if trade_id in self._monitoring_tasks:
            task = self._monitoring_tasks[trade_id]
            task.cancel()
            del self._monitoring_tasks[trade_id]
        
        if trade_id in self._monitored_positions:
            del self._monitored_positions[trade_id]
        
        logger.info(f"Stopped monitoring trade {trade_id}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "monitored_positions": len(self._monitored_positions),
            "active_tasks": len(self._monitoring_tasks),
            "positions": [
                {
                    "trade_id": trade_id,
                    "direction": info["direction"],
                    "entry_price": info["entry_price"],
                    "checks_count": info["checks_count"],
                    "exit_triggered": info["exit_triggered"]
                }
                for trade_id, info in self._monitored_positions.items()
            ]
        }


# 全局实例
position_exit_manager = PositionExitManager() 