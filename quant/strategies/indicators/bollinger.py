"""
Bollinger Bands Indicator
"""


import pandas as pd


class BollingerBands:
    """布林带指标"""

    @staticmethod
    def calculate(
        data: pd.Series, period: int = 20, std_dev: float = 2.0
    ) -> tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算布林带

        Args:
            data: 价格数据
            period: 移动平均周期
            std_dev: 标准差倍数

        Returns:
            (中轨, 上轨, 下轨)
        """
        # 计算中轨（简单移动平均）
        middle_band = data.rolling(window=period).mean()

        # 计算标准差
        rolling_std = data.rolling(window=period).std()

        # 计算上下轨
        upper_band = middle_band + (rolling_std * std_dev)
        lower_band = middle_band - (rolling_std * std_dev)

        return middle_band, upper_band, lower_band

    @staticmethod
    def get_signals(
        data: pd.Series,
        upper_band: pd.Series,
        lower_band: pd.Series,
        middle_band: pd.Series,
    ) -> pd.Series:
        """
        获取布林带信号

        Args:
            data: 价格数据
            upper_band: 上轨
            lower_band: 下轨
            middle_band: 中轨

        Returns:
            信号序列 (1: 买入, -1: 卖出, 0: 无信号)
        """
        signals = pd.Series(0, index=data.index)

        # 价格触及下轨 - 买入信号
        buy_signal = (data <= lower_band) & (data.shift(1) > lower_band.shift(1))
        signals[buy_signal] = 1

        # 价格触及上轨 - 卖出信号
        sell_signal = (data >= upper_band) & (data.shift(1) < upper_band.shift(1))
        signals[sell_signal] = -1

        return signals

    @staticmethod
    def get_bands_width(upper_band: pd.Series, lower_band: pd.Series) -> pd.Series:
        """
        计算带宽

        Args:
            upper_band: 上轨
            lower_band: 下轨

        Returns:
            带宽序列
        """
        middle_band = (upper_band + lower_band) / 2
        return (upper_band - lower_band) / middle_band

    @staticmethod
    def get_percent_b(
        data: pd.Series, upper_band: pd.Series, lower_band: pd.Series
    ) -> pd.Series:
        """
        计算%B指标

        Args:
            data: 价格数据
            upper_band: 上轨
            lower_band: 下轨

        Returns:
            %B指标序列
        """
        return (data - lower_band) / (upper_band - lower_band)

    @staticmethod
    def squeeze_signals(bands_width: pd.Series, threshold: float = 0.1) -> pd.Series:
        """
        挤压信号（带宽收缩后可能突破）

        Args:
            bands_width: 带宽序列
            threshold: 挤压阈值

        Returns:
            挤压信号序列 (1: 挤压结束, 0: 无信号)
        """
        signals = pd.Series(0, index=bands_width.index)

        # 带宽收缩到阈值以下后开始扩大
        squeeze_end = (bands_width > threshold) & (bands_width.shift(1) <= threshold)
        signals[squeeze_end] = 1

        return signals
