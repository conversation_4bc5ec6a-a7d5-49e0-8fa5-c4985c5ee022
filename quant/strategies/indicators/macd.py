"""
MACD (Moving Average Convergence Divergence) Indicator
"""


import pandas as pd


class MACD:
    """MACD指标"""

    @staticmethod
    def calculate(
        data: pd.Series,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ) -> tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算MACD

        Args:
            data: 价格数据
            fast_period: 快速EMA周期
            slow_period: 慢速EMA周期
            signal_period: 信号线周期

        Returns:
            (MACD线, 信号线, 柱状图)
        """
        # 计算快速和慢速EMA
        ema_fast = data.ewm(span=fast_period, adjust=False).mean()
        ema_slow = data.ewm(span=slow_period, adjust=False).mean()

        # 计算MACD线
        macd_line = ema_fast - ema_slow

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # 计算柱状图
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram

    @staticmethod
    def get_signals(
        macd_line: pd.Series, signal_line: pd.Series, histogram: pd.Series
    ) -> pd.Series:
        """
        获取MACD信号

        Args:
            macd_line: MACD线
            signal_line: 信号线
            histogram: 柱状图

        Returns:
            信号序列 (1: 金叉, -1: 死叉, 0: 无信号)
        """
        signals = pd.Series(0, index=macd_line.index)

        # 金叉：MACD线上穿信号线
        golden_cross = (macd_line > signal_line) & (
            macd_line.shift(1) <= signal_line.shift(1)
        )
        signals[golden_cross] = 1

        # 死叉：MACD线下穿信号线
        death_cross = (macd_line < signal_line) & (
            macd_line.shift(1) >= signal_line.shift(1)
        )
        signals[death_cross] = -1

        return signals

    @staticmethod
    def divergence(
        data: pd.Series, macd_line: pd.Series, period: int = 14
    ) -> pd.Series:
        """
        计算MACD背离信号

        Args:
            data: 价格数据
            macd_line: MACD线
            period: 检查周期

        Returns:
            背离信号序列 (1: 底背离, -1: 顶背离, 0: 无信号)
        """
        signals = pd.Series(0, index=data.index)

        # 简化的背离检测
        for i in range(period, len(data)):
            # 顶背离：价格创新高，MACD未创新高
            if (
                data.iloc[i] > data.iloc[i - period : i].max()
                and macd_line.iloc[i] < macd_line.iloc[i - period : i].max()
            ):
                signals.iloc[i] = -1

            # 底背离：价格创新低，MACD未创新低
            elif (
                data.iloc[i] < data.iloc[i - period : i].min()
                and macd_line.iloc[i] > macd_line.iloc[i - period : i].min()
            ):
                signals.iloc[i] = 1

        return signals
