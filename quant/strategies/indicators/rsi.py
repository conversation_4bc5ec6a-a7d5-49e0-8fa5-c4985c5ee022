"""
Relative Strength Index (RSI) Indicator
"""


import pandas as pd


class RSI:
    """相对强弱指数"""

    @staticmethod
    def calculate(data: pd.Series, period: int = 14) -> pd.Series:
        """
        计算RSI

        Args:
            data: 价格数据
            period: 周期

        Returns:
            RSI值序列
        """
        delta = data.diff()

        # 分离涨跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # 计算平均涨跌幅
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()

        # 计算RS
        rs = avg_gain / avg_loss

        # 计算RSI
        rsi = 100 - (100 / (1 + rs))

        return rsi

    @staticmethod
    def get_signals(
        rsi: pd.Series, oversold: int = 30, overbought: int = 70
    ) -> pd.Series:
        """
        获取RSI信号

        Args:
            rsi: RSI值序列
            oversold: 超卖线
            overbought: 超买线

        Returns:
            信号序列 (1: 超卖买入, -1: 超买卖出, 0: 无信号)
        """
        signals = pd.Series(0, index=rsi.index)

        # 超卖信号
        oversold_signal = (rsi < oversold) & (rsi.shift(1) >= oversold)
        signals[oversold_signal] = 1

        # 超买信号
        overbought_signal = (rsi > overbought) & (rsi.shift(1) <= overbought)
        signals[overbought_signal] = -1

        return signals

    @staticmethod
    def divergence(data: pd.Series, rsi: pd.Series, period: int = 14) -> pd.Series:
        """
        计算背离信号

        Args:
            data: 价格数据
            rsi: RSI值序列
            period: 检查周期

        Returns:
            背离信号序列 (1: 底背离, -1: 顶背离, 0: 无信号)
        """
        signals = pd.Series(0, index=data.index)

        # 简化的背离检测
        for i in range(period, len(data)):
            # 顶背离：价格创新高，RSI未创新高
            if (
                data.iloc[i] > data.iloc[i - period : i].max()
                and rsi.iloc[i] < rsi.iloc[i - period : i].max()
            ):
                signals.iloc[i] = -1

            # 底背离：价格创新低，RSI未创新低
            elif (
                data.iloc[i] < data.iloc[i - period : i].min()
                and rsi.iloc[i] > rsi.iloc[i - period : i].min()
            ):
                signals.iloc[i] = 1

        return signals
