"""
Moving Average Indicators
"""


import pandas as pd


class MA:
    """移动平均线"""

    @staticmethod
    def calculate(data: pd.Series, period: int = 20) -> pd.Series:
        """
        计算简单移动平均线

        Args:
            data: 价格数据
            period: 周期

        Returns:
            MA值序列
        """
        return data.rolling(window=period).mean()

    @staticmethod
    def crossover(fast_ma: pd.Series, slow_ma: pd.Series) -> pd.Series:
        """
        计算金叉死叉信号

        Args:
            fast_ma: 快速均线
            slow_ma: 慢速均线

        Returns:
            信号序列 (1: 金叉, -1: 死叉, 0: 无信号)
        """
        signals = pd.Series(0, index=fast_ma.index)

        # 金叉
        golden_cross = (fast_ma > slow_ma) & (fast_ma.shift(1) <= slow_ma.shift(1))
        signals[golden_cross] = 1

        # 死叉
        death_cross = (fast_ma < slow_ma) & (fast_ma.shift(1) >= slow_ma.shift(1))
        signals[death_cross] = -1

        return signals


class EMA:
    """指数移动平均线"""

    @staticmethod
    def calculate(data: pd.Series, period: int = 20) -> pd.Series:
        """
        计算指数移动平均线

        Args:
            data: 价格数据
            period: 周期

        Returns:
            EMA值序列
        """
        return data.ewm(span=period, adjust=False).mean()


class SMA:
    """简单移动平均线"""

    @staticmethod
    def calculate(data: pd.Series, period: int = 20) -> pd.Series:
        """
        计算简单移动平均线

        Args:
            data: 价格数据
            period: 周期

        Returns:
            SMA值序列
        """
        return data.rolling(window=period).mean()
