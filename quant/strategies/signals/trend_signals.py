"""
Trend Signal Generator
"""

from typing import Any

import numpy as np
import pandas as pd

from ..base.signal_base import SignalBase, SignalStrength, SignalType
from ..indicators import MA, MACD, RSI


class TrendSignalGenerator(SignalBase):
    """趋势信号生成器"""

    def __init__(
        self, name: str = "TrendSignalGenerator", config: dict[str, Any] = None
    ):
        super().__init__(name, config)
        self.ma_fast = config.get("ma_fast", 20) if config else 20
        self.ma_slow = config.get("ma_slow", 60) if config else 60
        self.rsi_period = config.get("rsi_period", 14) if config else 14
        self.macd_fast = config.get("macd_fast", 12) if config else 12
        self.macd_slow = config.get("macd_slow", 26) if config else 26
        self.macd_signal = config.get("macd_signal", 9) if config else 9

    def calculate(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """
        计算趋势信号

        Args:
            data: 市场数据，包含price列

        Returns:
            信号列表
        """
        signals = []

        if "price" not in data.columns:
            return signals

        prices = data["price"]

        # 计算移动平均线
        ma_fast = MA.calculate(prices, self.ma_fast)
        ma_slow = MA.calculate(prices, self.ma_slow)

        # 计算MACD
        macd_line, signal_line, histogram = MACD.calculate(
            prices, self.macd_fast, self.macd_slow, self.macd_signal
        )

        # 计算RSI
        rsi = RSI.calculate(prices, self.rsi_period)

        # 生成信号
        for i in range(1, len(data)):
            timestamp = data.index[i]
            current_price = prices.iloc[i]

            # MA交叉信号
            ma_signal = self._check_ma_crossover(ma_fast, ma_slow, i)
            if ma_signal:
                signals.append(
                    self.create_signal(
                        symbol=data.get("symbol", "UNKNOWN"),
                        signal_type=ma_signal,
                        strength=SignalStrength.MEDIUM,
                        price=current_price,
                        timestamp=timestamp,
                        reason=f"MA{self.ma_fast}/MA{self.ma_slow} crossover",
                    )
                )

            # MACD信号
            macd_signal = self._check_macd_signal(macd_line, signal_line, i)
            if macd_signal:
                signals.append(
                    self.create_signal(
                        symbol=data.get("symbol", "UNKNOWN"),
                        signal_type=macd_signal,
                        strength=SignalStrength.STRONG,
                        price=current_price,
                        timestamp=timestamp,
                        reason="MACD crossover",
                    )
                )

            # 趋势强度确认
            trend_strength = self._check_trend_strength(prices, ma_fast, ma_slow, i)
            if trend_strength and len(signals) > 0:
                # 强化最近信号
                signals[-1]["strength"] = SignalStrength.STRONG.value

        return signals

    def _check_ma_crossover(
        self, ma_fast: pd.Series, ma_slow: pd.Series, index: int
    ) -> SignalType | None:
        """检查MA交叉信号"""
        if (
            ma_fast.iloc[index] > ma_slow.iloc[index]
            and ma_fast.iloc[index - 1] <= ma_slow.iloc[index - 1]
        ):
            return SignalType.BUY
        elif (
            ma_fast.iloc[index] < ma_slow.iloc[index]
            and ma_fast.iloc[index - 1] >= ma_slow.iloc[index - 1]
        ):
            return SignalType.SELL
        return None

    def _check_macd_signal(
        self, macd_line: pd.Series, signal_line: pd.Series, index: int
    ) -> SignalType | None:
        """检查MACD信号"""
        if (
            macd_line.iloc[index] > signal_line.iloc[index]
            and macd_line.iloc[index - 1] <= signal_line.iloc[index - 1]
        ):
            return SignalType.BUY
        elif (
            macd_line.iloc[index] < signal_line.iloc[index]
            and macd_line.iloc[index - 1] >= signal_line.iloc[index - 1]
        ):
            return SignalType.SELL
        return None

    def _check_trend_strength(
        self, prices: pd.Series, ma_fast: pd.Series, ma_slow: pd.Series, index: int
    ) -> bool:
        """检查趋势强度"""
        # 简化的趋势强度检查
        if len(prices) < index + 5:
            return False

        recent_prices = prices.iloc[index - 5 : index + 1]
        price_slope = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]

        # 价格斜率和均线方向一致
        if price_slope > 0 and ma_fast.iloc[index] > ma_slow.iloc[index]:
            return True
        elif price_slope < 0 and ma_fast.iloc[index] < ma_slow.iloc[index]:
            return True

        return False
