"""
Arbitrage Signal Generator
"""

from typing import Any

import pandas as pd

from ..base.signal_base import SignalBase, SignalStrength, SignalType


class ArbitrageSignalGenerator(SignalBase):
    """套利信号生成器"""

    def __init__(
        self, name: str = "ArbitrageSignalGenerator", config: dict[str, Any] = None
    ):
        super().__init__(name, config)
        self.spread_threshold = config.get("spread_threshold", 0.01) if config else 0.01
        self.correlation_threshold = (
            config.get("correlation_threshold", 0.8) if config else 0.8
        )
        self.lookback_period = config.get("lookback_period", 20) if config else 20

    def calculate(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """
        计算套利信号

        Args:
            data: 市场数据，包含多个标的的价格数据

        Returns:
            信号列表
        """
        signals = []

        if len(data.columns) < 2:
            return signals

        # 获取所有价格列
        price_columns = [col for col in data.columns if "price" in col.lower()]

        if len(price_columns) < 2:
            return signals

        # 计算价差套利信号
        for i in range(len(price_columns)):
            for j in range(i + 1, len(price_columns)):
                symbol1 = price_columns[i]
                symbol2 = price_columns[j]

                spread_signals = self._calculate_spread_signals(data, symbol1, symbol2)
                signals.extend(spread_signals)

        return signals

    def _calculate_spread_signals(
        self, data: pd.DataFrame, symbol1: str, symbol2: str
    ) -> list[dict[str, Any]]:
        """计算价差套利信号"""
        signals = []

        prices1 = data[symbol1]
        prices2 = data[symbol2]

        # 计算价差
        spread = prices1 - prices2
        spread_mean = spread.rolling(window=self.lookback_period).mean()
        spread_std = spread.rolling(window=self.lookback_period).std()

        # 计算价差Z-score
        z_score = (spread - spread_mean) / spread_std

        # 生成信号
        for i in range(1, len(data)):
            timestamp = data.index[i]
            current_z = z_score.iloc[i]

            if pd.isna(current_z):
                continue

            # 价差过大 - 做空价差
            if current_z > 2.0:
                signals.append(
                    self.create_signal(
                        symbol=f"{symbol1}_{symbol2}",
                        signal_type=SignalType.SELL,
                        strength=SignalStrength.STRONG,
                        price=current_z,
                        timestamp=timestamp,
                        reason=f"Spread Z-score: {current_z:.2f}",
                        metadata={
                            "symbol1": symbol1,
                            "symbol2": symbol2,
                            "spread": spread.iloc[i],
                            "z_score": current_z,
                        },
                    )
                )

            # 价差过小 - 做多价差
            elif current_z < -2.0:
                signals.append(
                    self.create_signal(
                        symbol=f"{symbol1}_{symbol2}",
                        signal_type=SignalType.BUY,
                        strength=SignalStrength.STRONG,
                        price=current_z,
                        timestamp=timestamp,
                        reason=f"Spread Z-score: {current_z:.2f}",
                        metadata={
                            "symbol1": symbol1,
                            "symbol2": symbol2,
                            "spread": spread.iloc[i],
                            "z_score": current_z,
                        },
                    )
                )

            # 价差回归 - 平仓
            elif abs(current_z) < 0.5:
                # 检查之前是否有套利头寸
                recent_signals = [
                    s
                    for s in self.signal_history
                    if s["symbol"] == f"{symbol1}_{symbol2}" and abs(s["z_score"]) > 1.5
                ]

                if recent_signals:
                    signals.append(
                        self.create_signal(
                            symbol=f"{symbol1}_{symbol2}",
                            signal_type=(
                                SignalType.CLOSE_LONG
                                if recent_signals[-1]["signal_type"] == "buy"
                                else SignalType.CLOSE_SHORT
                            ),
                            strength=SignalStrength.MEDIUM,
                            price=current_z,
                            timestamp=timestamp,
                            reason=f"Spread normalized: {current_z:.2f}",
                            metadata={
                                "symbol1": symbol1,
                                "symbol2": symbol2,
                                "spread": spread.iloc[i],
                                "z_score": current_z,
                            },
                        )
                    )

        return signals

    def calculate_triangular_arbitrage(
        self, data: pd.DataFrame, symbols: list[str]
    ) -> list[dict[str, Any]]:
        """
        计算三角套利信号

        Args:
            data: 市场数据
            symbols: 三个交易标的

        Returns:
            三角套利信号列表
        """
        signals = []

        if len(symbols) != 3:
            return signals

        # 简化的三角套利检查
        # 这里需要实际的汇率/价格数据
        # 实际实现会更复杂，需要考虑交易费用等

        return signals

    def calculate_statistical_arbitrage(
        self, data: pd.DataFrame, symbols: list[str]
    ) -> list[dict[str, Any]]:
        """
        计算统计套利信号

        Args:
            data: 市场数据
            symbols: 交易标的列表

        Returns:
            统计套利信号列表
        """
        signals = []

        if len(symbols) < 2:
            return signals

        # 计算相关性
        prices = data[symbols]
        correlation_matrix = prices.corr()

        # 寻找高度相关的对
        for i in range(len(symbols)):
            for j in range(i + 1, len(symbols)):
                correlation = correlation_matrix.iloc[i, j]

                if abs(correlation) > self.correlation_threshold:
                    # 计算协整关系
                    coint_signals = self._check_cointegration(
                        data[symbols[i]], data[symbols[j]]
                    )
                    signals.extend(coint_signals)

        return signals

    def _check_cointegration(
        self, series1: pd.Series, series2: pd.Series
    ) -> list[dict[str, Any]]:
        """检查协整关系"""
        # 简化的协整检查
        # 实际实现需要使用统计库如statsmodels

        signals = []

        # 这里应该实现ADF检验等协整检验
        # 为示例目的，返回空列表

        return signals
