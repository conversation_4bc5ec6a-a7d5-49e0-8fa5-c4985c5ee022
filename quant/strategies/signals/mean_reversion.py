"""
Mean Reversion Signal Generator
"""

from typing import Any

import pandas as pd

from ..base.signal_base import SignalBase, SignalStrength, SignalType
from ..indicators import RSI, BollingerBands


class MeanReversionSignalGenerator(SignalBase):
    """均值回归信号生成器"""

    def __init__(
        self, name: str = "MeanReversionSignalGenerator", config: dict[str, Any] = None
    ):
        super().__init__(name, config)
        self.rsi_period = config.get("rsi_period", 14) if config else 14
        self.rsi_oversold = config.get("rsi_oversold", 30) if config else 30
        self.rsi_overbought = config.get("rsi_overbought", 70) if config else 70
        self.bb_period = config.get("bb_period", 20) if config else 20
        self.bb_std = config.get("bb_std", 2.0) if config else 2.0

    def calculate(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """
        计算均值回归信号

        Args:
            data: 市场数据，包含price列

        Returns:
            信号列表
        """
        signals = []

        if "price" not in data.columns:
            return signals

        prices = data["price"]

        # 计算RSI
        rsi = RSI.calculate(prices, self.rsi_period)

        # 计算布林带
        middle_band, upper_band, lower_band = BollingerBands.calculate(
            prices, self.bb_period, self.bb_std
        )

        # 生成信号
        for i in range(1, len(data)):
            timestamp = data.index[i]
            current_price = prices.iloc[i]

            # RSI超买超卖信号
            rsi_signal = self._check_rsi_signal(rsi, i)
            if rsi_signal:
                signals.append(
                    self.create_signal(
                        symbol=data.get("symbol", "UNKNOWN"),
                        signal_type=rsi_signal,
                        strength=SignalStrength.MEDIUM,
                        price=current_price,
                        timestamp=timestamp,
                        reason=f"RSI {rsi.iloc[i]:.1f} {'oversold' if rsi_signal == SignalType.BUY else 'overbought'}",
                    )
                )

            # 布林带信号
            bb_signal = self._check_bollinger_signal(prices, upper_band, lower_band, i)
            if bb_signal:
                signals.append(
                    self.create_signal(
                        symbol=data.get("symbol", "UNKNOWN"),
                        signal_type=bb_signal,
                        strength=SignalStrength.STRONG,
                        price=current_price,
                        timestamp=timestamp,
                        reason=f"Price {'below lower' if bb_signal == SignalType.BUY else 'above upper'} band",
                    )
                )

            # 均值回归确认
            if self._check_mean_reversion_confirmation(prices, middle_band, i):
                # 强化信号
                if signals:
                    signals[-1]["strength"] = SignalStrength.STRONG.value

        return signals

    def _check_rsi_signal(self, rsi: pd.Series, index: int) -> SignalType | None:
        """检查RSI信号"""
        current_rsi = rsi.iloc[index]
        prev_rsi = rsi.iloc[index - 1]

        # 超卖信号
        if current_rsi < self.rsi_oversold and prev_rsi >= self.rsi_oversold:
            return SignalType.BUY

        # 超买信号
        elif current_rsi > self.rsi_overbought and prev_rsi <= self.rsi_overbought:
            return SignalType.SELL

        return None

    def _check_bollinger_signal(
        self,
        prices: pd.Series,
        upper_band: pd.Series,
        lower_band: pd.Series,
        index: int,
    ) -> SignalType | None:
        """检查布林带信号"""
        current_price = prices.iloc[index]
        prev_price = prices.iloc[index - 1]

        # 价格触及下轨
        if (
            current_price <= lower_band.iloc[index]
            and prev_price > lower_band.iloc[index - 1]
        ):
            return SignalType.BUY

        # 价格触及上轨
        elif (
            current_price >= upper_band.iloc[index]
            and prev_price < upper_band.iloc[index - 1]
        ):
            return SignalType.SELL

        return None

    def _check_mean_reversion_confirmation(
        self, prices: pd.Series, middle_band: pd.Series, index: int
    ) -> bool:
        """检查均值回归确认"""
        if len(prices) < index + 3:
            return False

        # 检查价格是否开始向中轨回归
        recent_prices = prices.iloc[index - 2 : index + 1]
        recent_middle = middle_band.iloc[index - 2 : index + 1]

        # 价格与中轨的距离在缩小
        current_distance = abs(recent_prices.iloc[-1] - recent_middle.iloc[-1])
        prev_distance = abs(recent_prices.iloc[-2] - recent_middle.iloc[-2])

        return current_distance < prev_distance
