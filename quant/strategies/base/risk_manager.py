"""
Risk Manager Base Class
"""

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any

import pandas as pd


class RiskLevel(Enum):
    """风险等级枚举"""

    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class RiskType(Enum):
    """风险类型枚举"""

    POSITION_LIMIT = "position_limit"
    DRAW_DOWN = "draw_down"
    VOLATILITY = "volatility"
    LEVERAGE = "leverage"
    CONCENTRATION = "concentration"
    LIQUIDITY = "liquidity"


class RiskManager(ABC):
    """
    风控管理基类
    所有风控管理器都必须继承此类
    """

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.risk_logs = []
        self.risk_limits = self._init_risk_limits()

    def _init_risk_limits(self) -> dict[str, Any]:
        """初始化风险限制"""
        return {
            "max_position_size": self.config.get("max_position_size", 1.0),
            "max_drawdown": self.config.get("max_drawdown", 0.2),
            "max_leverage": self.config.get("max_leverage", 3.0),
            "max_concentration": self.config.get("max_concentration", 0.3),
            "min_liquidity": self.config.get("min_liquidity", 10000),
        }

    @abstractmethod
    def check_risk(
        self, portfolio_data: dict[str, Any], market_data: pd.DataFrame
    ) -> list[dict[str, Any]]:
        """
        检查风险

        Args:
            portfolio_data: 投资组合数据
            market_data: 市场数据

        Returns:
            风险列表
        """
        pass

    def check_position_limit(self, positions: dict[str, float]) -> list[dict[str, Any]]:
        """检查仓位限制"""
        risks = []
        max_size = self.risk_limits["max_position_size"]

        for symbol, size in positions.items():
            if abs(size) > max_size:
                risks.append(
                    {
                        "type": RiskType.POSITION_LIMIT,
                        "level": RiskLevel.HIGH,
                        "symbol": symbol,
                        "message": f"仓位 {size} 超过限制 {max_size}",
                        "timestamp": datetime.now(),
                    }
                )

        return risks

    def check_drawdown(self, equity_curve: pd.Series) -> list[dict[str, Any]]:
        """检查回撤"""
        risks = []
        max_dd = self.risk_limits["max_drawdown"]

        if len(equity_curve) > 1:
            peak = equity_curve.expanding(min_periods=1).max()
            drawdown = (peak - equity_curve) / peak
            current_dd = drawdown.iloc[-1]

            if current_dd > max_dd:
                risks.append(
                    {
                        "type": RiskType.DRAW_DOWN,
                        "level": (
                            RiskLevel.HIGH
                            if current_dd > max_dd * 1.5
                            else RiskLevel.MEDIUM
                        ),
                        "message": f"回撤 {current_dd:.2%} 超过限制 {max_dd:.2%}",
                        "timestamp": datetime.now(),
                    }
                )

        return risks

    def log_risk(self, risk: dict[str, Any]):
        """记录风险"""
        self.risk_logs.append(risk)

    def get_risk_logs(self, limit: int = None) -> list[dict[str, Any]]:
        """获取风险日志"""
        if limit:
            return self.risk_logs[-limit:]
        return self.risk_logs

    def update_config(self, new_config: dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        self.risk_limits = self._init_risk_limits()
