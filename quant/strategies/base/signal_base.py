"""
Signal Base Class
"""

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Optional

import pandas as pd
from quant.confidence_scorer import ConfidenceScorer, ConfidenceScore


class SignalType(Enum):
    """信号类型枚举"""

    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_LONG = "close_long"
    CLOSE_SHORT = "close_short"


class SignalStrength(Enum):
    """信号强度枚举"""

    WEAK = 1
    MEDIUM = 2
    STRONG = 3


class SignalBase(ABC):
    """
    信号基类
    所有信号生成器都必须继承此类
    """

    def __init__(self, name: str, config: dict[str, Any] = None, use_confidence_scoring: bool = True):
        self.name = name
        self.config = config or {}
        self.signal_history = []
        self.use_confidence_scoring = use_confidence_scoring
        
        # Initialize confidence scorer if enabled
        if self.use_confidence_scoring:
            self.confidence_scorer = ConfidenceScorer(config.get('confidence_scoring', {}))
        else:
            self.confidence_scorer = None

    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """
        计算信号

        Args:
            data: 市场数据

        Returns:
            信号列表
        """
        pass

    def create_signal(
        self,
        symbol: str,
        signal_type: SignalType,
        strength: SignalStrength,
        price: float,
        timestamp: datetime,
        reason: str = "",
        metadata: dict[str, Any] = None,
    ) -> dict[str, Any]:
        """
        创建信号

        Args:
            symbol: 交易标的
            signal_type: 信号类型
            strength: 信号强度
            price: 价格
            timestamp: 时间戳
            reason: 信号原因
            metadata: 额外信息

        Returns:
            信号字典
        """
        signal = {
            "symbol": symbol,
            "signal_type": signal_type.value,
            "strength": strength.value,
            "price": price,
            "timestamp": timestamp,
            "reason": reason,
            "metadata": metadata or {},
            "generator": self.name,
        }

        self.signal_history.append(signal)
        return signal

    def get_signal_history(self, limit: int = None) -> list[dict[str, Any]]:
        """获取信号历史"""
        if limit:
            return self.signal_history[-limit:]
        return self.signal_history

    def clear_history(self):
        """清空信号历史"""
        self.signal_history = []
    
    def calculate_confidence_score(self, market_data: pd.DataFrame) -> Optional[ConfidenceScore]:
        """
        计算置信度分数
        
        Args:
            market_data: 市场数据
            
        Returns:
            置信度分数对象或None
        """
        if not self.use_confidence_scoring or not self.confidence_scorer:
            return None
        
        try:
            return self.confidence_scorer.calculate_confidence(market_data)
        except Exception as e:
            # Log error but don't raise to maintain backward compatibility
            print(f"Error calculating confidence score: {e}")
            return None
    
    def create_signal_with_confidence(
        self,
        symbol: str,
        signal_type: SignalType,
        strength: SignalStrength,
        price: float,
        timestamp: datetime,
        reason: str = "",
        metadata: dict[str, Any] = None,
        confidence_score: Optional[ConfidenceScore] = None
    ) -> dict[str, Any]:
        """
        创建带置信度的信号
        
        Args:
            symbol: 交易标的
            signal_type: 信号类型
            strength: 信号强度
            price: 价格
            timestamp: 时间戳
            reason: 信号原因
            metadata: 额外信息
            confidence_score: 置信度分数对象
            
        Returns:
            信号字典
        """
        # Create base signal
        signal = self.create_signal(symbol, signal_type, strength, price, timestamp, reason, metadata)
        
        # Add confidence information if available
        if confidence_score:
            signal["confidence_score"] = confidence_score.overall_confidence
            signal["confidence_breakdown"] = {
                "trend_score": confidence_score.trend_score,
                "momentum_score": confidence_score.momentum_score,
                "volatility_score": confidence_score.volatility_score,
                "volume_score": confidence_score.volume_score,
                "market_regime_score": confidence_score.market_regime_score,
                "indicator_scores": confidence_score.indicator_scores,
                "market_regime": confidence_score.calculation_details.get('market_regime', 'unknown')
            }
            signal["signal_strength"] = self.confidence_scorer.get_signal_strength(confidence_score.overall_confidence).value if self.confidence_scorer else strength.value
        else:
            signal["confidence_score"] = 0.5  # Default confidence
            signal["signal_strength"] = strength.value
        
        return signal
    
    def update_confidence_config(self, new_config: dict[str, Any]):
        """
        更新置信度评分配置
        
        Args:
            new_config: 新的配置字典
        """
        if self.confidence_scorer:
            self.confidence_scorer.update_config(new_config)
    
    def get_confidence_performance_stats(self) -> dict[str, Any]:
        """
        获取置信度评分性能统计
        
        Returns:
            性能统计字典
        """
        if self.confidence_scorer:
            return self.confidence_scorer.get_performance_stats()
        return {}
