"""
Strategy Base Class
"""

from abc import ABC, abstractmethod
from typing import Any

import pandas as pd


class StrategyBase(ABC):
    """
    策略基类
    所有策略都必须继承此类并实现相应方法
    """

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.positions = {}
        self.orders = []
        self.signals = []
        self.is_running = False

    @abstractmethod
    def initialize(self):
        """策略初始化"""
        pass

    @abstractmethod
    def on_tick(self, tick_data: dict[str, Any]):
        """处理实时行情数据"""
        pass

    @abstractmethod
    def on_bar(self, bar_data: pd.DataFrame):
        """处理K线数据"""
        pass

    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> list[dict[str, Any]]:
        """生成交易信号"""
        pass

    def start(self):
        """启动策略"""
        self.is_running = True
        self.initialize()

    def stop(self):
        """停止策略"""
        self.is_running = False

    def get_status(self) -> dict[str, Any]:
        """获取策略状态"""
        return {
            "name": self.name,
            "is_running": self.is_running,
            "positions": self.positions,
            "orders_count": len(self.orders),
            "signals_count": len(self.signals),
        }

    def reset(self):
        """重置策略状态"""
        self.positions = {}
        self.orders = []
        self.signals = []
        self.is_running = False
