"""
Risk Controller
"""

from datetime import datetime
from typing import Any

import pandas as pd

from ..base.risk_manager import RiskLevel, RiskManager, RiskType


class RiskController(RiskManager):
    """风险控制器"""

    def __init__(self, name: str = "RiskController", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.stop_loss_ratio = config.get("stop_loss_ratio", 0.05) if config else 0.05
        self.take_profit_ratio = (
            config.get("take_profit_ratio", 0.15) if config else 0.15
        )
        self.max_positions = config.get("max_positions", 10) if config else 10
        self.max_position_size = config.get("max_position_size", 0.1) if config else 0.1
        self.max_drawdown = config.get("max_drawdown", 0.2) if config else 0.2

    def check_risk(
        self, portfolio_data: dict[str, Any], market_data: pd.DataFrame
    ) -> list[dict[str, Any]]:
        """
        检查风险

        Args:
            portfolio_data: 投资组合数据
            market_data: 市场数据

        Returns:
            风险列表
        """
        risks = []

        # 检查仓位限制
        position_risks = self._check_position_limits(portfolio_data)
        risks.extend(position_risks)

        # 检查回撤
        drawdown_risks = self._check_drawdown(portfolio_data)
        risks.extend(drawdown_risks)

        # 检查止损止盈
        stop_risks = self._check_stop_conditions(portfolio_data, market_data)
        risks.extend(stop_risks)

        # 检查集中度
        concentration_risks = self._check_concentration(portfolio_data)
        risks.extend(concentration_risks)

        # 记录风险
        for risk in risks:
            self.log_risk(risk)

        return risks

    def _check_position_limits(
        self, portfolio_data: dict[str, Any]
    ) -> list[dict[str, Any]]:
        """检查仓位限制"""
        risks = []

        positions = portfolio_data.get("positions", {})
        total_capital = portfolio_data.get("total_capital", 0)

        # 检查最大持仓数量
        if len(positions) > self.max_positions:
            risks.append(
                {
                    "type": RiskType.POSITION_LIMIT,
                    "level": RiskLevel.MEDIUM,
                    "message": f"持仓数量 {len(positions)} 超过限制 {self.max_positions}",
                    "timestamp": datetime.now(),
                }
            )

        # 检查单个持仓大小
        for symbol, position in positions.items():
            position_size = abs(position.get("size", 0))
            if position_size > self.max_position_size * total_capital:
                risks.append(
                    {
                        "type": RiskType.POSITION_LIMIT,
                        "level": RiskLevel.HIGH,
                        "symbol": symbol,
                        "message": f"持仓大小 {position_size} 超过限制 {self.max_position_size * total_capital}",
                        "timestamp": datetime.now(),
                    }
                )

        return risks

    def _check_drawdown(self, portfolio_data: dict[str, Any]) -> list[dict[str, Any]]:
        """检查回撤"""
        risks = []

        equity_curve = portfolio_data.get("equity_curve", pd.Series())
        if len(equity_curve) > 1:
            peak = equity_curve.expanding(min_periods=1).max()
            drawdown = (peak - equity_curve) / peak
            current_dd = drawdown.iloc[-1]

            if current_dd > self.max_drawdown:
                risks.append(
                    {
                        "type": RiskType.DRAW_DOWN,
                        "level": (
                            RiskLevel.CRITICAL
                            if current_dd > self.max_drawdown * 1.5
                            else RiskLevel.HIGH
                        ),
                        "message": f"回撤 {current_dd:.2%} 超过限制 {self.max_drawdown:.2%}",
                        "timestamp": datetime.now(),
                    }
                )

        return risks

    def _check_stop_conditions(
        self, portfolio_data: dict[str, Any], market_data: pd.DataFrame
    ) -> list[dict[str, Any]]:
        """检查止损止盈条件"""
        risks = []

        positions = portfolio_data.get("positions", {})

        for symbol, position in positions.items():
            if position.get("status") != "open":
                continue

            entry_price = position.get("entry_price", 0)
            current_price = market_data.get(symbol, {}).get("price", 0)

            if current_price == 0 or entry_price == 0:
                continue

            # 计算盈亏比例
            if position.get("type") == "long":
                pnl_ratio = (current_price - entry_price) / entry_price
            else:
                pnl_ratio = (entry_price - current_price) / entry_price

            # 检查止损
            if pnl_ratio <= -self.stop_loss_ratio:
                risks.append(
                    {
                        "type": RiskType.POSITION_LIMIT,
                        "level": RiskLevel.HIGH,
                        "symbol": symbol,
                        "message": f"触发止损，盈亏比例 {pnl_ratio:.2%}",
                        "timestamp": datetime.now(),
                        "action": "close_position",
                    }
                )

            # 检查止盈
            elif pnl_ratio >= self.take_profit_ratio:
                risks.append(
                    {
                        "type": RiskType.POSITION_LIMIT,
                        "level": RiskLevel.MEDIUM,
                        "symbol": symbol,
                        "message": f"触发止盈，盈亏比例 {pnl_ratio:.2%}",
                        "timestamp": datetime.now(),
                        "action": "close_position",
                    }
                )

        return risks

    def _check_concentration(
        self, portfolio_data: dict[str, Any]
    ) -> list[dict[str, Any]]:
        """检查集中度风险"""
        risks = []

        positions = portfolio_data.get("positions", {})
        total_capital = portfolio_data.get("total_capital", 0)

        if total_capital == 0:
            return risks

        # 计算每个持仓的占比
        position_values = {}
        for symbol, position in positions.items():
            if position.get("status") == "open":
                size = position.get("size", 0)
                current_price = position.get("current_price", 0)
                value = abs(size * current_price)
                position_values[symbol] = value / total_capital

        # 检查集中度
        for symbol, ratio in position_values.items():
            if ratio > self.max_position_size:
                risks.append(
                    {
                        "type": RiskType.CONCENTRATION,
                        "level": RiskLevel.MEDIUM,
                        "symbol": symbol,
                        "message": f"持仓集中度 {ratio:.2%} 超过限制 {self.max_position_size:.2%}",
                        "timestamp": datetime.now(),
                    }
                )

        return risks

    def calculate_position_size(
        self,
        symbol: str,
        available_capital: float,
        price: float,
        risk_per_trade: float = 0.02,
    ) -> float:
        """
        计算建议仓位大小

        Args:
            symbol: 交易标的
            available_capital: 可用资金
            price: 价格
            risk_per_trade: 每笔交易风险比例

        Returns:
            建议仓位大小
        """
        # 基于风险计算仓位大小
        risk_amount = available_capital * risk_per_trade

        # 假设止损距离为价格的5%
        stop_distance = price * self.stop_loss_ratio

        if stop_distance > 0:
            position_size = risk_amount / stop_distance
        else:
            position_size = 0

        # 确保不超过最大仓位限制
        max_size = (available_capital * self.max_position_size) / price
        position_size = min(position_size, max_size)

        return position_size

    def get_risk_summary(self) -> dict[str, Any]:
        """获取风险摘要"""
        recent_risks = self.get_risk_logs(limit=100)

        risk_summary = {
            "total_risks": len(recent_risks),
            "high_risks": len(
                [r for r in recent_risks if r.get("level") == RiskLevel.HIGH]
            ),
            "critical_risks": len(
                [r for r in recent_risks if r.get("level") == RiskLevel.CRITICAL]
            ),
            "risk_types": {},
            "recent_actions": [],
        }

        # 统计风险类型
        for risk in recent_risks:
            risk_type = risk.get("type", "unknown")
            risk_summary["risk_types"][risk_type] = (
                risk_summary["risk_types"].get(risk_type, 0) + 1
            )

            if risk.get("action"):
                risk_summary["recent_actions"].append(risk["action"])

        return risk_summary
