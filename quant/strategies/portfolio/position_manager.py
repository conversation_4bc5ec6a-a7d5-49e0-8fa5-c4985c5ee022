"""
Position Manager
"""

from datetime import datetime
from enum import Enum
from typing import Any

import pandas as pd


class PositionType(Enum):
    """持仓类型"""

    LONG = "long"
    SHORT = "short"
    CLOSED = "closed"


class PositionStatus(Enum):
    """持仓状态"""

    OPEN = "open"
    CLOSED = "closed"
    PENDING = "pending"


class Position:
    """持仓类"""

    def __init__(
        self,
        symbol: str,
        position_type: PositionType,
        quantity: float,
        entry_price: float,
        entry_time: datetime,
    ):
        self.symbol = symbol
        self.position_type = position_type
        self.quantity = quantity
        self.entry_price = entry_price
        self.entry_time = entry_time
        self.exit_price = None
        self.exit_time = None
        self.status = PositionStatus.OPEN
        self.realized_pnl = 0.0
        self.unrealized_pnl = 0.0

    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.status == PositionStatus.OPEN:
            if self.position_type == PositionType.LONG:
                self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
            else:
                self.unrealized_pnl = (self.entry_price - current_price) * self.quantity

    def close_position(self, exit_price: float, exit_time: datetime):
        """平仓"""
        self.exit_price = exit_price
        self.exit_time = exit_time
        self.status = PositionStatus.CLOSED

        if self.position_type == PositionType.LONG:
            self.realized_pnl = (exit_price - self.entry_price) * self.quantity
        else:
            self.realized_pnl = (self.entry_price - exit_price) * self.quantity

        self.unrealized_pnl = 0.0

    def get_duration(self) -> pd.Timedelta | None:
        """获取持仓时间"""
        if self.exit_time:
            return self.exit_time - self.entry_time
        return None

    def get_return_rate(self) -> float | None:
        """获取收益率"""
        if self.exit_price:
            if self.position_type == PositionType.LONG:
                return (self.exit_price - self.entry_price) / self.entry_price
            else:
                return (self.entry_price - self.exit_price) / self.entry_price
        return None


class PositionManager:
    """仓位管理器"""

    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}  # symbol -> Position
        self.position_history = []
        self.trade_log = []

    def open_position(
        self,
        symbol: str,
        position_type: PositionType,
        quantity: float,
        price: float,
        timestamp: datetime,
    ) -> bool:
        """
        开仓

        Args:
            symbol: 交易标的
            position_type: 持仓类型
            quantity: 数量
            price: 价格
            timestamp: 时间戳

        Returns:
            是否成功开仓
        """
        if (
            symbol in self.positions
            and self.positions[symbol].status == PositionStatus.OPEN
        ):
            return False

        # 检查资金是否足够
        required_capital = quantity * price
        if required_capital > self.current_capital:
            return False

        position = Position(symbol, position_type, quantity, price, timestamp)
        self.positions[symbol] = position
        self.position_history.append(position)

        # 记录交易
        self.trade_log.append(
            {
                "timestamp": timestamp,
                "symbol": symbol,
                "action": "open",
                "type": position_type.value,
                "quantity": quantity,
                "price": price,
                "capital": required_capital,
            }
        )

        return True

    def close_position(self, symbol: str, price: float, timestamp: datetime) -> bool:
        """
        平仓

        Args:
            symbol: 交易标的
            price: 价格
            timestamp: 时间戳

        Returns:
            是否成功平仓
        """
        if (
            symbol not in self.positions
            or self.positions[symbol].status != PositionStatus.OPEN
        ):
            return False

        position = self.positions[symbol]
        position.close_position(price, timestamp)

        # 更新资金
        self.current_capital += position.realized_pnl

        # 记录交易
        self.trade_log.append(
            {
                "timestamp": timestamp,
                "symbol": symbol,
                "action": "close",
                "type": position.position_type.value,
                "quantity": position.quantity,
                "price": price,
                "pnl": position.realized_pnl,
                "capital": self.current_capital,
            }
        )

        return True

    def update_positions(self, market_data: dict[str, float], timestamp: datetime):
        """
        更新所有持仓

        Args:
            market_data: 市场数据
            timestamp: 时间戳
        """
        for symbol, position in self.positions.items():
            if position.status == PositionStatus.OPEN and symbol in market_data:
                position.update_unrealized_pnl(market_data[symbol])

    def get_total_unrealized_pnl(self) -> float:
        """获取总未实现盈亏"""
        return sum(
            pos.unrealized_pnl
            for pos in self.positions.values()
            if pos.status == PositionStatus.OPEN
        )

    def get_total_realized_pnl(self) -> float:
        """获取总已实现盈亏"""
        return sum(pos.realized_pnl for pos in self.position_history)

    def get_positions_summary(self) -> dict[str, Any]:
        """获取持仓摘要"""
        open_positions = [
            pos for pos in self.positions.values() if pos.status == PositionStatus.OPEN
        ]

        return {
            "total_capital": self.current_capital,
            "initial_capital": self.initial_capital,
            "total_return": self.current_capital - self.initial_capital,
            "total_return_rate": (self.current_capital - self.initial_capital)
            / self.initial_capital,
            "open_positions_count": len(open_positions),
            "total_positions_count": len(self.position_history),
            "unrealized_pnl": self.get_total_unrealized_pnl(),
            "realized_pnl": self.get_total_realized_pnl(),
            "win_rate": self._calculate_win_rate(),
        }

    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        closed_positions = [
            pos for pos in self.position_history if pos.status == PositionStatus.CLOSED
        ]
        if not closed_positions:
            return 0.0

        winners = sum(1 for pos in closed_positions if pos.realized_pnl > 0)
        return winners / len(closed_positions)

    def get_position_by_symbol(self, symbol: str) -> Position | None:
        """根据标的获取持仓"""
        return self.positions.get(symbol)

    def get_all_positions(self) -> dict[str, Position]:
        """获取所有持仓"""
        return self.positions.copy()

    def get_trade_log(self) -> list[dict[str, Any]]:
        """获取交易记录"""
        return self.trade_log.copy()

    def reset(self):
        """重置仓位管理器"""
        self.positions = {}
        self.position_history = []
        self.trade_log = []
        self.current_capital = self.initial_capital
