"""
Performance Analyzer
"""

from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd
from scipy import stats


class PerformanceAnalyzer:
    """绩效分析器"""

    def __init__(self):
        self.trades = []
        self.equity_curve = pd.Series()
        self.benchmark_data = pd.Series()

    def add_trade(self, trade: dict[str, Any]):
        """添加交易记录"""
        self.trades.append(trade)

    def set_equity_curve(self, equity_curve: pd.Series):
        """设置权益曲线"""
        self.equity_curve = equity_curve

    def set_benchmark(self, benchmark_data: pd.Series):
        """设置基准数据"""
        self.benchmark_data = benchmark_data

    def calculate_returns(self) -> dict[str, float]:
        """计算收益率指标"""
        if len(self.equity_curve) < 2:
            return {}

        returns = self.equity_curve.pct_change().dropna()

        metrics = {
            "total_return": (self.equity_curve.iloc[-1] - self.equity_curve.iloc[0])
            / self.equity_curve.iloc[0],
            "annual_return": returns.mean() * 252,
            "monthly_return": returns.mean() * 21,
            "daily_return": returns.mean(),
            "volatility": returns.std() * np.sqrt(252),
            "sharpe_ratio": self._calculate_sharpe_ratio(returns),
            "sortino_ratio": self._calculate_sortino_ratio(returns),
            "max_drawdown": self._calculate_max_drawdown(),
            "calmar_ratio": self._calculate_calmar_ratio(),
            "win_rate": self._calculate_win_rate(),
            "profit_factor": self._calculate_profit_factor(),
            "average_win": self._calculate_average_win(),
            "average_loss": self._calculate_average_loss(),
        }

        return metrics

    def _calculate_sharpe_ratio(
        self, returns: pd.Series, risk_free_rate: float = 0.02
    ) -> float:
        """计算夏普比率"""
        if returns.std() == 0:
            return 0.0

        excess_returns = returns - risk_free_rate / 252
        return excess_returns.mean() / returns.std() * np.sqrt(252)

    def _calculate_sortino_ratio(
        self, returns: pd.Series, risk_free_rate: float = 0.02
    ) -> float:
        """计算索提诺比率"""
        excess_returns = returns - risk_free_rate / 252
        downside_returns = returns[returns < 0]

        if len(downside_returns) == 0:
            return float("inf")

        downside_std = downside_returns.std()
        if downside_std == 0:
            return float("inf")

        return excess_returns.mean() / downside_std * np.sqrt(252)

    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.equity_curve) < 2:
            return 0.0

        peak = self.equity_curve.expanding(min_periods=1).max()
        drawdown = (peak - self.equity_curve) / peak
        return drawdown.max()

    def _calculate_calmar_ratio(self) -> float:
        """计算卡玛比率"""
        max_dd = self._calculate_max_drawdown()
        if max_dd == 0:
            return float("inf")

        total_return = (
            self.equity_curve.iloc[-1] - self.equity_curve.iloc[0]
        ) / self.equity_curve.iloc[0]
        return total_return / max_dd

    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if not self.trades:
            return 0.0

        winners = sum(1 for trade in self.trades if trade.get("pnl", 0) > 0)
        return winners / len(self.trades)

    def _calculate_profit_factor(self) -> float:
        """计算盈亏比"""
        if not self.trades:
            return 0.0

        gross_profit = sum(
            trade.get("pnl", 0) for trade in self.trades if trade.get("pnl", 0) > 0
        )
        gross_loss = sum(
            abs(trade.get("pnl", 0)) for trade in self.trades if trade.get("pnl", 0) < 0
        )

        if gross_loss == 0:
            return float("inf")

        return gross_profit / gross_loss

    def _calculate_average_win(self) -> float:
        """计算平均盈利"""
        winning_trades = [
            trade.get("pnl", 0) for trade in self.trades if trade.get("pnl", 0) > 0
        ]
        return np.mean(winning_trades) if winning_trades else 0.0

    def _calculate_average_loss(self) -> float:
        """计算平均亏损"""
        losing_trades = [
            trade.get("pnl", 0) for trade in self.trades if trade.get("pnl", 0) < 0
        ]
        return np.mean(losing_trades) if losing_trades else 0.0

    def calculate_trade_statistics(self) -> dict[str, Any]:
        """计算交易统计"""
        if not self.trades:
            return {}

        pnls = [trade.get("pnl", 0) for trade in self.trades]

        stats = {
            "total_trades": len(self.trades),
            "winning_trades": sum(1 for pnl in pnls if pnl > 0),
            "losing_trades": sum(1 for pnl in pnls if pnl < 0),
            "average_trade": np.mean(pnls),
            "median_trade": np.median(pnls),
            "largest_win": max(pnls),
            "largest_loss": min(pnls),
            "std_dev_trades": np.std(pnls) if len(pnls) > 1 else 0.0,
            "skewness": stats.skew(pnls) if len(pnls) > 2 else 0.0,
            "kurtosis": stats.kurtosis(pnls) if len(pnls) > 3 else 0.0,
        }

        return stats

    def calculate_drawdown_analysis(self) -> dict[str, Any]:
        """计算回撤分析"""
        if len(self.equity_curve) < 2:
            return {}

        peak = self.equity_curve.expanding(min_periods=1).max()
        drawdown = (peak - self.equity_curve) / peak

        # 找到所有回撤期
        is_drawdown = drawdown > 0
        drawdown_periods = []

        start_idx = None
        for i, is_dd in enumerate(is_drawdown):
            if is_dd and start_idx is None:
                start_idx = i
            elif not is_dd and start_idx is not None:
                drawdown_periods.append(
                    {
                        "start": self.equity_curve.index[start_idx],
                        "end": self.equity_curve.index[i],
                        "max_drawdown": drawdown.iloc[start_idx:i].max(),
                        "duration": i - start_idx,
                    }
                )
                start_idx = None

        analysis = {
            "max_drawdown": drawdown.max(),
            "avg_drawdown": (
                drawdown[drawdown > 0].mean() if drawdown[drawdown > 0].any() else 0.0
            ),
            "drawdown_periods": len(drawdown_periods),
            "avg_drawdown_duration": (
                np.mean([p["duration"] for p in drawdown_periods])
                if drawdown_periods
                else 0.0
            ),
            "max_drawdown_duration": (
                max([p["duration"] for p in drawdown_periods])
                if drawdown_periods
                else 0.0
            ),
            "current_drawdown": drawdown.iloc[-1],
        }

        return analysis

    def calculate_benchmark_comparison(self) -> dict[str, float]:
        """计算与基准的对比"""
        if len(self.equity_curve) < 2 or len(self.benchmark_data) < 2:
            return {}

        strategy_returns = self.equity_curve.pct_change().dropna()
        benchmark_returns = self.benchmark_data.pct_change().dropna()

        # 对齐时间序列
        aligned_returns = pd.concat(
            [strategy_returns, benchmark_returns], axis=1
        ).dropna()

        if len(aligned_returns) < 2:
            return {}

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 计算相关系数
        correlation = strategy_returns.corr(benchmark_returns)

        # 计算beta
        covariance = strategy_returns.cov(benchmark_returns)
        benchmark_variance = benchmark_returns.var()
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0.0

        # 计算alpha
        strategy_mean_return = strategy_returns.mean()
        benchmark_mean_return = benchmark_returns.mean()
        alpha = strategy_mean_return - beta * benchmark_mean_return

        # 计算信息比率
        tracking_error = (strategy_returns - benchmark_returns).std()
        information_ratio = (
            (strategy_mean_return - benchmark_mean_return) / tracking_error
            if tracking_error > 0
            else 0.0
        )

        return {
            "correlation": correlation,
            "beta": beta,
            "alpha": alpha * 252,  # 年化alpha
            "information_ratio": information_ratio * np.sqrt(252),
            "tracking_error": tracking_error * np.sqrt(252),
            "upside_capture": self._calculate_upside_capture(
                strategy_returns, benchmark_returns
            ),
            "downside_capture": self._calculate_downside_capture(
                strategy_returns, benchmark_returns
            ),
        }

    def _calculate_upside_capture(
        self, strategy_returns: pd.Series, benchmark_returns: pd.Series
    ) -> float:
        """计算上行捕获率"""
        up_periods = benchmark_returns > 0
        if up_periods.sum() == 0:
            return 0.0

        strategy_up = strategy_returns[up_periods].mean()
        benchmark_up = benchmark_returns[up_periods].mean()

        return strategy_up / benchmark_up if benchmark_up > 0 else 0.0

    def _calculate_downside_capture(
        self, strategy_returns: pd.Series, benchmark_returns: pd.Series
    ) -> float:
        """计算下行捕获率"""
        down_periods = benchmark_returns < 0
        if down_periods.sum() == 0:
            return 0.0

        strategy_down = strategy_returns[down_periods].mean()
        benchmark_down = benchmark_returns[down_periods].mean()

        return strategy_down / benchmark_down if benchmark_down < 0 else 0.0

    def generate_performance_report(self) -> dict[str, Any]:
        """生成绩效报告"""
        report = {
            "summary": self.calculate_returns(),
            "trade_statistics": self.calculate_trade_statistics(),
            "drawdown_analysis": self.calculate_drawdown_analysis(),
            "benchmark_comparison": self.calculate_benchmark_comparison(),
            "timestamp": datetime.now(),
        }

        return report

    def reset(self):
        """重置分析器"""
        self.trades = []
        self.equity_curve = pd.Series()
        self.benchmark_data = pd.Series()
