"""
批量结算处理器
解决信号堆积问题，批量处理所有待结算的交易信号
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from quant.database_manager import DatabaseManager
from quant.binance_client import BinanceClient
from quant.notification_manager import NotificationManager
from quant.utils.logger import setup_logger

logger = setup_logger(__name__)

class BatchSettlementProcessor:
    """批量结算处理器"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.binance_client = BinanceClient()
        self.notification_manager = NotificationManager()
        self.current_kline_number = self._get_current_kline_number()
        
    def _get_current_kline_number(self) -> int:
        """获取当前K线序号（基于30分钟间隔）"""
        try:
            now = datetime.utcnow()
            start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
            minutes_since_start = (now - start_of_day).total_seconds() / 60
            kline_number = int(minutes_since_start / 30) + 1
            return max(1, min(kline_number, 48))
        except Exception as e:
            logger.error(f"获取当前K线序号失败: {e}")
            return 48  # 默认返回最后一个K线
    
    def _calculate_signal_kline_number(self, signal_timestamp: datetime) -> int:
        """计算信号的K线序号"""
        try:
            if isinstance(signal_timestamp, str):
                signal_timestamp = datetime.fromisoformat(signal_timestamp.replace('Z', '+00:00'))
            
            start_of_day = signal_timestamp.replace(hour=0, minute=0, second=0, microsecond=0)
            minutes_since_start = (signal_timestamp - start_of_day).total_seconds() / 60
            kline_number = int(minutes_since_start / 30) + 1
            return max(1, min(kline_number, 48))
        except Exception as e:
            logger.error(f"计算信号K线序号失败: {e}")
            return 1
    
    async def analyze_settlement_backlog(self) -> Dict[str, Any]:
        """分析结算积压情况"""
        try:
            logger.info("🔍 分析结算积压情况...")
            
            pending_trades = self.db.get_pending_trades()
            current_time = datetime.utcnow()
            
            backlog_analysis = {
                "total_pending": len(pending_trades),
                "current_kline": self.current_kline_number,
                "backlog_details": [],
                "oldest_signal": None,
                "newest_signal": None,
                "max_delay_minutes": 0,
                "kline_gap": 0
            }
            
            if not pending_trades:
                logger.info("✅ 没有发现待结算的交易")
                return backlog_analysis
            
            # 分析每个待结算交易
            for trade in pending_trades:
                signal_time = trade["signal_timestamp"]
                if isinstance(signal_time, str):
                    signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
                
                time_since_signal = (current_time - signal_time).total_seconds() / 60
                signal_kline = self._calculate_signal_kline_number(signal_time)
                
                trade_info = {
                    "trade_id": trade["id"],
                    "signal_kline": signal_kline,
                    "signal_time": signal_time.isoformat(),
                    "delay_minutes": time_since_signal,
                    "direction": trade["direction"],
                    "entry_price": trade["entry_price"],
                    "confidence_score": trade.get("confidence_score", 0.0)
                }
                
                backlog_analysis["backlog_details"].append(trade_info)
                
                # 更新统计信息
                if backlog_analysis["oldest_signal"] is None or signal_time < datetime.fromisoformat(backlog_analysis["oldest_signal"]):
                    backlog_analysis["oldest_signal"] = signal_time.isoformat()
                
                if backlog_analysis["newest_signal"] is None or signal_time > datetime.fromisoformat(backlog_analysis["newest_signal"]):
                    backlog_analysis["newest_signal"] = signal_time.isoformat()
                
                backlog_analysis["max_delay_minutes"] = max(backlog_analysis["max_delay_minutes"], time_since_signal)
            
            # 计算K线间隔
            if backlog_analysis["backlog_details"]:
                oldest_kline = min(detail["signal_kline"] for detail in backlog_analysis["backlog_details"])
                backlog_analysis["kline_gap"] = self.current_kline_number - oldest_kline
            
            logger.info(f"📊 积压分析完成: {len(pending_trades)}个待结算交易，最大延迟{backlog_analysis['max_delay_minutes']:.1f}分钟")
            return backlog_analysis
            
        except Exception as e:
            logger.error(f"分析结算积压失败: {e}")
            return {"error": str(e)}
    
    async def batch_settle_all_pending(self) -> Dict[str, Any]:
        """批量结算所有待结算交易"""
        try:
            logger.info("🚀 开始批量结算所有待结算交易...")
            
            # 先分析积压情况
            backlog_analysis = await self.analyze_settlement_backlog()
            if backlog_analysis.get("total_pending", 0) == 0:
                return {"message": "没有待结算的交易", "settled_count": 0}
            
            # 获取当前市场价格
            current_price = await self.binance_client.get_current_price()
            logger.info(f"💰 当前BTC价格: ${current_price:,.2f}")
            
            pending_trades = self.db.get_pending_trades()
            settlement_results = []
            successful_settlements = 0
            failed_settlements = 0
            
            # 按时间顺序处理交易
            sorted_trades = sorted(pending_trades, key=lambda x: x["signal_timestamp"])
            
            for i, trade in enumerate(sorted_trades, 1):
                logger.info(f"⚡ 处理交易 {i}/{len(sorted_trades)}: ID {trade['id']}")
                
                try:
                    settlement_result = await self._settle_trade_with_current_price(
                        trade, current_price
                    )
                    
                    if settlement_result:
                        # 更新数据库
                        self.db.update_trade_result(trade["id"], settlement_result)
                        settlement_results.append(settlement_result)
                        successful_settlements += 1
                        
                        # 发送结算通知
                        await self._send_batch_settlement_notification(settlement_result, i, len(sorted_trades))
                        
                        logger.info(f"✅ 交易 {trade['id']} 结算成功: {settlement_result['status']}")
                    else:
                        failed_settlements += 1
                        logger.error(f"❌ 交易 {trade['id']} 结算失败")
                        
                except Exception as e:
                    failed_settlements += 1
                    logger.error(f"❌ 结算交易 {trade['id']} 时发生错误: {e}")
                
                # 添加小延迟避免过快处理
                await asyncio.sleep(0.1)
            
            # 生成批量结算报告
            batch_result = {
                "total_processed": len(sorted_trades),
                "successful_settlements": successful_settlements,
                "failed_settlements": failed_settlements,
                "settlement_results": settlement_results,
                "current_price": current_price,
                "processing_time": datetime.utcnow().isoformat(),
                "kline_sync_status": "completed"
            }
            
            logger.info(f"🎉 批量结算完成: {successful_settlements}成功, {failed_settlements}失败")
            
            # 发送批量结算完成通知
            await self._send_batch_completion_notification(batch_result)
            
            return batch_result
            
        except Exception as e:
            logger.error(f"批量结算失败: {e}")
            return {"error": str(e)}
    
    async def _settle_trade_with_current_price(self, trade: Dict[str, Any], 
                                             current_price: float) -> Optional[Dict[str, Any]]:
        """使用当前价格结算交易"""
        try:
            current_time = datetime.utcnow()
            signal_time = trade["signal_timestamp"]
            if isinstance(signal_time, str):
                signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
            
            # 计算时间差
            time_since_signal = (current_time - signal_time).total_seconds() / 60
            
            # 使用当前市场价格作为退出价格
            exit_price = current_price
            entry_price = trade["entry_price"]
            direction = trade["direction"]
            suggested_bet = trade["suggested_bet"]
            
            # 计算盈亏
            if direction == "LONG":
                pnl = ((exit_price - entry_price) * suggested_bet) / entry_price
            else:  # SHORT
                pnl = ((entry_price - exit_price) * suggested_bet) / entry_price
            
            pnl_percentage = (pnl / suggested_bet) * 100 if suggested_bet > 0 else 0
            status = "WIN" if pnl > 0 else "LOSS"
            
            # 计算信号K线序号
            signal_kline = self._calculate_signal_kline_number(signal_time)
            
            settlement_data = {
                "trade_id": trade["id"],
                "status": status,
                "exit_price": exit_price,
                "exit_timestamp": current_time.isoformat(),
                "pnl": pnl,
                "pnl_percentage": pnl_percentage,
                "entry_price": entry_price,
                "direction": direction,
                "signal_timestamp": signal_time.isoformat(),
                "signal_kline": f"{signal_kline}/48",
                "settlement_kline": f"{self.current_kline_number}/48",
                "delay_minutes": time_since_signal,
                "settlement_type": "batch_settlement",
                "confidence_score": trade.get("confidence_score", 0.0),
                "market_state": trade.get("market_state", "Unknown")
            }
            
            return settlement_data
            
        except Exception as e:
            logger.error(f"结算交易 {trade['id']} 失败: {e}")
            return None
    
    async def _send_batch_settlement_notification(self, settlement_result: Dict[str, Any], 
                                                current: int, total: int):
        """发送批量结算通知"""
        try:
            # 只在特定间隔发送通知，避免通知过多
            if current % 5 == 0 or current == total:
                message = f"📊 批量结算进度 ({current}/{total})\n"
                message += f"🔢 信号: {settlement_result['signal_kline']}\n"
                message += f"📈 方向: {settlement_result['direction']}\n"
                message += f"💰 结果: {settlement_result['status']}\n"
                message += f"💵 盈亏: ${settlement_result['pnl']:.2f}\n"
                
                await self.notification_manager.send_message(message)
        except Exception as e:
            logger.error(f"发送批量结算通知失败: {e}")
    
    async def _send_batch_completion_notification(self, batch_result: Dict[str, Any]):
        """发送批量结算完成通知"""
        try:
            message = "🎉 批量结算完成报告\n\n"
            message += f"📊 处理总数: {batch_result['total_processed']}\n"
            message += f"✅ 成功结算: {batch_result['successful_settlements']}\n"
            message += f"❌ 失败结算: {batch_result['failed_settlements']}\n"
            message += f"💰 当前价格: ${batch_result['current_price']:,.2f}\n"
            message += f"🔄 K线同步: 已完成\n"
            message += f"⏰ 处理时间: {batch_result['processing_time']}\n\n"
            message += "📈 系统已恢复实时结算状态"
            
            await self.notification_manager.send_message(message)
        except Exception as e:
            logger.error(f"发送批量完成通知失败: {e}")
    
    async def verify_settlement_status(self) -> Dict[str, Any]:
        """验证结算状态"""
        try:
            logger.info("🔍 验证结算状态...")
            
            # 检查是否还有待结算交易
            pending_trades = self.db.get_pending_trades()
            
            # 获取最近的结算记录
            recent_settlements = self.db.get_recent_settlements(limit=10)
            
            verification_result = {
                "remaining_pending": len(pending_trades),
                "recent_settlements_count": len(recent_settlements),
                "current_kline": self.current_kline_number,
                "system_status": "synchronized" if len(pending_trades) == 0 else "pending_trades_remain",
                "verification_time": datetime.utcnow().isoformat()
            }
            
            if recent_settlements:
                latest_settlement = recent_settlements[0]
                verification_result["latest_settlement"] = {
                    "trade_id": latest_settlement.get("id"),
                    "settlement_time": latest_settlement.get("exit_timestamp"),
                    "status": latest_settlement.get("status")
                }
            
            logger.info(f"✅ 验证完成: {verification_result['system_status']}")
            return verification_result
            
        except Exception as e:
            logger.error(f"验证结算状态失败: {e}")
            return {"error": str(e)}

async def main():
    """主函数 - 执行批量结算"""
    try:
        processor = BatchSettlementProcessor()
        
        print("🚀 启动批量结算处理器...")
        print("=" * 60)
        
        # 1. 分析积压情况
        print("📊 步骤1: 分析结算积压情况")
        backlog_analysis = await processor.analyze_settlement_backlog()
        
        if backlog_analysis.get("total_pending", 0) == 0:
            print("✅ 没有发现待结算的交易，系统状态正常")
            return
        
        print(f"⚠️ 发现 {backlog_analysis['total_pending']} 个待结算交易")
        print(f"📈 当前K线: {backlog_analysis['current_kline']}/48")
        print(f"⏰ 最大延迟: {backlog_analysis['max_delay_minutes']:.1f} 分钟")
        print(f"📊 K线间隔: {backlog_analysis['kline_gap']} 个K线")
        
        # 2. 执行批量结算
        print("\n🔄 步骤2: 执行批量结算")
        batch_result = await processor.batch_settle_all_pending()
        
        if "error" in batch_result:
            print(f"❌ 批量结算失败: {batch_result['error']}")
            return
        
        print(f"✅ 批量结算完成:")
        print(f"   - 处理总数: {batch_result['total_processed']}")
        print(f"   - 成功结算: {batch_result['successful_settlements']}")
        print(f"   - 失败结算: {batch_result['failed_settlements']}")
        
        # 3. 验证结算状态
        print("\n🔍 步骤3: 验证结算状态")
        verification = await processor.verify_settlement_status()
        
        print(f"📊 验证结果:")
        print(f"   - 剩余待结算: {verification['remaining_pending']}")
        print(f"   - 系统状态: {verification['system_status']}")
        print(f"   - 当前K线: {verification['current_kline']}/48")
        
        print("\n🎉 批量结算处理完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 批量结算处理失败: {e}")
        logger.error(f"批量结算处理失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
