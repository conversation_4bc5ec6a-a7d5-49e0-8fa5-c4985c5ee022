"""
System Health Monitoring Module

Handles comprehensive system health monitoring including metrics collection,
health checks, and alert generation.
"""

import json
import os
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any

import psutil
from sqlalchemy import text

from quant.utils.logger import get_logger


@dataclass
class SystemMetrics:
    """System metrics data structure."""

    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_total_mb: float
    disk_usage_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int
    load_average: float | None
    uptime_seconds: float


@dataclass
class HealthCheckResult:
    """Health check result data structure."""

    component: str
    status: str  # "healthy", "warning", "critical"
    message: str
    timestamp: str
    metrics: dict[str, Any] | None = None


@dataclass
class AlertThreshold:
    """Alert threshold configuration."""

    warning: float
    critical: float


class SystemMonitor:
    """Monitors system health and performance metrics."""

    def __init__(self, config_path: str = "config.json"):
        self.logger = get_logger(__name__)
        self.start_time = time.time()
        self.last_network_stats = None

        # Alert thresholds
        self.thresholds = {
            "cpu_usage": AlertThreshold(0.8, 0.9),
            "memory_usage": AlertThreshold(0.85, 0.95),
            "disk_usage": AlertThreshold(0.9, 0.95),
            "api_latency": AlertThreshold(5.0, 10.0),
            "websocket_disconnections": AlertThreshold(3, 5),
            "database_connections": AlertThreshold(0.8, 0.9),
            "error_rate": AlertThreshold(0.05, 0.1),
        }

        # Health check history
        self.health_history: list[HealthCheckResult] = []
        self.max_history_size = 100

        # Load configuration
        self._load_config(config_path)

    def _load_config(self, config_path: str):
        """Load monitoring configuration."""
        try:
            with open(config_path, encoding="utf-8") as f:
                config = json.load(f)

            monitoring_config = config.get("SYSTEM_MONITORING", {})

            if monitoring_config.get("enabled", True):
                # Update thresholds from config
                alert_thresholds = monitoring_config.get("alert_thresholds", {})
                for key, threshold in alert_thresholds.items():
                    if key in self.thresholds:
                        if isinstance(threshold, dict):
                            # Nested configuration with warning/critical keys
                            self.thresholds[key].warning = threshold.get(
                                "warning", self.thresholds[key].warning
                            )
                            self.thresholds[key].critical = threshold.get(
                                "critical", self.thresholds[key].critical
                            )
                        elif isinstance(threshold, (int, float)):
                            # Flat configuration - use as warning level, critical as warning * 1.2
                            self.thresholds[key].warning = float(threshold)
                            self.thresholds[key].critical = float(threshold) * 1.2

            self.logger.info("System monitoring configuration loaded successfully")

        except Exception as e:
            self.logger.error(f"Error loading monitoring configuration: {e}")

    def collect_system_metrics(self) -> SystemMetrics:
        """Collect comprehensive system metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / 1024 / 1024
            memory_total_mb = memory.total / 1024 / 1024

            # Disk metrics
            disk = psutil.disk_usage("/")
            disk_usage_percent = disk.percent
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            disk_total_gb = disk.total / 1024 / 1024 / 1024

            # Network metrics
            network = psutil.net_io_counters()
            network_sent_mb = network.bytes_sent / 1024 / 1024
            network_recv_mb = network.bytes_recv / 1024 / 1024

            # Process and thread counts
            process_count = len(psutil.pids())
            current_process = psutil.Process()
            thread_count = current_process.num_threads()

            # Load average (Unix-like systems)
            load_average = None
            try:
                load_avg = os.getloadavg()
                load_average = load_avg[0]  # 1-minute load average
            except (AttributeError, OSError):
                # Not available on Windows
                pass

            # Uptime
            uptime_seconds = time.time() - self.start_time

            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_total_mb=memory_total_mb,
                disk_usage_percent=disk_usage_percent,
                disk_used_gb=disk_used_gb,
                disk_total_gb=disk_total_gb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                thread_count=thread_count,
                load_average=load_average,
                uptime_seconds=uptime_seconds,
            )

        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            # Return default metrics
            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_total_mb=0.0,
                disk_usage_percent=0.0,
                disk_used_gb=0.0,
                disk_total_gb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                process_count=0,
                thread_count=0,
                load_average=None,
                uptime_seconds=0.0,
            )

    def check_health_alerts(self, metrics: SystemMetrics) -> list[HealthCheckResult]:
        """Generate health alerts based on threshold violations."""
        alerts = []

        # CPU usage check
        if metrics.cpu_percent >= self.thresholds["cpu_usage"].critical:
            alerts.append(
                HealthCheckResult(
                    component="CPU",
                    status="critical",
                    message=f"CPU usage critical: {metrics.cpu_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={"cpu_percent": metrics.cpu_percent},
                )
            )
        elif metrics.cpu_percent >= self.thresholds["cpu_usage"].warning:
            alerts.append(
                HealthCheckResult(
                    component="CPU",
                    status="warning",
                    message=f"CPU usage high: {metrics.cpu_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={"cpu_percent": metrics.cpu_percent},
                )
            )

        # Memory usage check
        if metrics.memory_percent >= self.thresholds["memory_usage"].critical:
            alerts.append(
                HealthCheckResult(
                    component="Memory",
                    status="critical",
                    message=f"Memory usage critical: {metrics.memory_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={
                        "memory_percent": metrics.memory_percent,
                        "memory_used_mb": metrics.memory_used_mb,
                    },
                )
            )
        elif metrics.memory_percent >= self.thresholds["memory_usage"].warning:
            alerts.append(
                HealthCheckResult(
                    component="Memory",
                    status="warning",
                    message=f"Memory usage high: {metrics.memory_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={
                        "memory_percent": metrics.memory_percent,
                        "memory_used_mb": metrics.memory_used_mb,
                    },
                )
            )

        # Disk usage check
        if metrics.disk_usage_percent >= self.thresholds["disk_usage"].critical:
            alerts.append(
                HealthCheckResult(
                    component="Disk",
                    status="critical",
                    message=f"Disk usage critical: {metrics.disk_usage_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={
                        "disk_usage_percent": metrics.disk_usage_percent,
                        "disk_used_gb": metrics.disk_used_gb,
                    },
                )
            )
        elif metrics.disk_usage_percent >= self.thresholds["disk_usage"].warning:
            alerts.append(
                HealthCheckResult(
                    component="Disk",
                    status="warning",
                    message=f"Disk usage high: {metrics.disk_usage_percent:.1f}%",
                    timestamp=metrics.timestamp,
                    metrics={
                        "disk_usage_percent": metrics.disk_usage_percent,
                        "disk_used_gb": metrics.disk_used_gb,
                    },
                )
            )

        # Process count check
        if metrics.process_count > 1000:  # Arbitrary high threshold
            alerts.append(
                HealthCheckResult(
                    component="System",
                    status="warning",
                    message=f"High process count: {metrics.process_count}",
                    timestamp=metrics.timestamp,
                    metrics={"process_count": metrics.process_count},
                )
            )

        return alerts

    def check_websocket_health(self, websocket_manager) -> HealthCheckResult:
        """Check WebSocket connection health."""
        try:
            # Get WebSocket status from the real-time data manager
            status = websocket_manager.get_stream_status()

            active_streams = status.get("active_streams", 0)
            failed_connections = status.get("failed_connections", 0)
            error_rate = status.get("error_rate", 0.0)

            # Check disconnection rate
            if (
                failed_connections
                >= self.thresholds["websocket_disconnections"].critical
            ):
                return HealthCheckResult(
                    component="WebSocket",
                    status="critical",
                    message=f"WebSocket disconnections critical: {failed_connections}",
                    timestamp=datetime.now().isoformat(),
                    metrics={
                        "failed_connections": failed_connections,
                        "active_streams": active_streams,
                    },
                )
            elif (
                failed_connections
                >= self.thresholds["websocket_disconnections"].warning
            ):
                return HealthCheckResult(
                    component="WebSocket",
                    status="warning",
                    message=f"WebSocket disconnections high: {failed_connections}",
                    timestamp=datetime.now().isoformat(),
                    metrics={
                        "failed_connections": failed_connections,
                        "active_streams": active_streams,
                    },
                )

            # Check error rate
            if error_rate >= self.thresholds["error_rate"].critical:
                return HealthCheckResult(
                    component="WebSocket",
                    status="critical",
                    message=f"WebSocket error rate critical: {error_rate:.2%}",
                    timestamp=datetime.now().isoformat(),
                    metrics={
                        "error_rate": error_rate,
                        "active_streams": active_streams,
                    },
                )
            elif error_rate >= self.thresholds["error_rate"].warning:
                return HealthCheckResult(
                    component="WebSocket",
                    status="warning",
                    message=f"WebSocket error rate high: {error_rate:.2%}",
                    timestamp=datetime.now().isoformat(),
                    metrics={
                        "error_rate": error_rate,
                        "active_streams": active_streams,
                    },
                )

            return HealthCheckResult(
                component="WebSocket",
                status="healthy",
                message=f"WebSocket healthy: {active_streams} active streams",
                timestamp=datetime.now().isoformat(),
                metrics={"active_streams": active_streams, "error_rate": error_rate},
            )

        except Exception as e:
            self.logger.error(f"Error checking WebSocket health: {e}")
            return HealthCheckResult(
                component="WebSocket",
                status="critical",
                message=f"WebSocket health check failed: {str(e)}",
                timestamp=datetime.now().isoformat(),
            )

    def check_database_health(self, database_manager) -> HealthCheckResult:
        """Check database health and performance."""
        try:
            # Test database connection
            start_time = time.time()

            # Try to execute a simple query
            with database_manager.get_session() as session:
                session.execute(text("SELECT 1"))

            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds

            # Check response time
            if response_time > 1000:  # 1 second
                return HealthCheckResult(
                    component="Database",
                    status="critical",
                    message=f"Database response time critical: {response_time:.0f}ms",
                    timestamp=datetime.now().isoformat(),
                    metrics={"response_time_ms": response_time},
                )
            elif response_time > 500:  # 500ms
                return HealthCheckResult(
                    component="Database",
                    status="warning",
                    message=f"Database response time slow: {response_time:.0f}ms",
                    timestamp=datetime.now().isoformat(),
                    metrics={"response_time_ms": response_time},
                )

            return HealthCheckResult(
                component="Database",
                status="healthy",
                message=f"Database healthy: {response_time:.0f}ms response time",
                timestamp=datetime.now().isoformat(),
                metrics={"response_time_ms": response_time},
            )

        except Exception as e:
            self.logger.error(f"Error checking database health: {e}")
            return HealthCheckResult(
                component="Database",
                status="critical",
                message=f"Database health check failed: {str(e)}",
                timestamp=datetime.now().isoformat(),
            )

    def run_comprehensive_health_check(
        self, websocket_manager=None, database_manager=None
    ) -> dict[str, Any]:
        """Run comprehensive health check for all system components."""
        try:
            # Collect system metrics
            system_metrics = self.collect_system_metrics()

            # Check system health
            system_alerts = self.check_health_alerts(system_metrics)

            # Check component health
            component_results = {}

            # WebSocket health
            if websocket_manager:
                websocket_health = self.check_websocket_health(websocket_manager)
                component_results["websocket"] = websocket_health

            # Database health
            if database_manager:
                database_health = self.check_database_health(database_manager)
                component_results["database"] = database_health

            # Compile results
            all_alerts = system_alerts + [
                result
                for result in component_results.values()
                if result.status != "healthy"
            ]

            # Store in history
            for alert in all_alerts:
                self.health_history.append(alert)

            # Limit history size
            if len(self.health_history) > self.max_history_size:
                self.health_history = self.health_history[-self.max_history_size :]

            # Determine overall system health
            critical_alerts = [
                alert for alert in all_alerts if alert.status == "critical"
            ]
            warning_alerts = [
                alert for alert in all_alerts if alert.status == "warning"
            ]

            if critical_alerts:
                overall_status = "critical"
            elif warning_alerts:
                overall_status = "warning"
            else:
                overall_status = "healthy"

            return {
                "overall_status": overall_status,
                "timestamp": datetime.now().isoformat(),
                "system_metrics": asdict(system_metrics),
                "component_health": {
                    k: asdict(v) for k, v in component_results.items()
                },
                "alerts": [asdict(alert) for alert in all_alerts],
                "summary": {
                    "total_alerts": len(all_alerts),
                    "critical_alerts": len(critical_alerts),
                    "warning_alerts": len(warning_alerts),
                    "uptime_hours": system_metrics.uptime_seconds / 3600,
                },
            }

        except Exception as e:
            self.logger.error(f"Error running comprehensive health check: {e}")
            return {
                "overall_status": "critical",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            }

    def get_health_summary(self, hours: int = 24) -> dict[str, Any]:
        """Get health summary for the specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            # Filter alerts from the specified period
            recent_alerts = [
                alert
                for alert in self.health_history
                if datetime.fromisoformat(alert.timestamp) >= cutoff_time
            ]

            # Count by component and status
            component_counts = {}
            status_counts = {"healthy": 0, "warning": 0, "critical": 0}

            for alert in recent_alerts:
                component = alert.component
                status = alert.status

                if component not in component_counts:
                    component_counts[component] = {
                        "healthy": 0,
                        "warning": 0,
                        "critical": 0,
                    }

                component_counts[component][status] += 1
                status_counts[status] += 1

            return {
                "period_hours": hours,
                "total_alerts": len(recent_alerts),
                "status_counts": status_counts,
                "component_counts": component_counts,
                "most_problematic_component": (
                    max(
                        component_counts.keys(),
                        key=lambda x: sum(component_counts[x].values()),
                        default=None,
                    )
                    if component_counts
                    else None
                ),
            }

        except Exception as e:
            self.logger.error(f"Error getting health summary: {e}")
            return {"error": str(e)}

    def clear_health_history(self):
        """Clear health check history."""
        self.health_history.clear()
        self.logger.info("Health history cleared")


# Global system monitor instance
system_monitor = SystemMonitor()
