"""
<PERSON>rro<PERSON> Handler Module

Implements structured error handling and graceful exit strategies.
"""

import sys
import traceback
from collections.abc import Callable
from datetime import datetime
from typing import Any, Optional
from functools import wraps

from quant.exceptions import TradingSystemError
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class ErrorHandler:
    """Handles system-wide error handling and graceful exits."""

    def __init__(self):
        self.shutdown_hooks = []
        self.max_retries = 3
        self.retry_delay = 5  # seconds

    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """Global exception handler."""
        if issubclass(exc_type, KeyboardInterrupt):
            # Handle keyboard interrupt gracefully
            logger.info("Received keyboard interrupt, shutting down gracefully...")
            self.graceful_shutdown()
            return

        # Log the exception
        logger.error(
            "Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback)
        )

        # Send error notification
        error_data = {
            "error_type": exc_type.__name__,
            "error_message": str(exc_value),
            "timestamp": datetime.utcnow().isoformat(),
            "traceback": "".join(
                traceback.format_exception(exc_type, exc_value, exc_traceback)
            ),
        }

        try:
            notification_manager.send_error_notification(error_data)
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")

        # For critical errors, initiate graceful shutdown
        if isinstance(exc_value, TradingSystemError):
            logger.error(
                "Critical trading system error, initiating graceful shutdown..."
            )
            self.graceful_shutdown()

    def with_retry(
        self, max_retries: Optional[int] = None, retry_delay: Optional[int] = None
    ):
        """Decorator for retry logic."""

        def decorator(func: Callable):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                nonlocal max_retries, retry_delay
                max_retries = max_retries or self.max_retries
                retry_delay = retry_delay or self.retry_delay

                for attempt in range(max_retries + 1):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        if attempt == max_retries:
                            logger.error(
                                f"Function {func.__name__} failed after {max_retries} retries: {e}"
                            )
                            raise
                        else:
                            logger.warning(
                                f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {retry_delay}s: {e}"
                            )
                            import asyncio

                            await asyncio.sleep(retry_delay)

            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                nonlocal max_retries, retry_delay
                max_retries = max_retries or self.max_retries
                retry_delay = retry_delay or self.retry_delay

                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        if attempt == max_retries:
                            logger.error(
                                f"Function {func.__name__} failed after {max_retries} retries: {e}"
                            )
                            raise
                        else:
                            logger.warning(
                                f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {retry_delay}s: {e}"
                            )
                            import time

                            time.sleep(retry_delay)

            # Return appropriate wrapper based on function type
            import inspect

            if inspect.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper

        return decorator

    def add_shutdown_hook(self, hook: Callable):
        """Add a function to be called during graceful shutdown."""
        self.shutdown_hooks.append(hook)

    def graceful_shutdown(self):
        """Perform graceful shutdown of the application."""
        logger.info("Initiating graceful shutdown...")

        # Call all shutdown hooks
        for hook in self.shutdown_hooks:
            try:
                hook()
            except Exception as e:
                logger.error(f"Error in shutdown hook: {e}")

        logger.info("Graceful shutdown completed")
        sys.exit(0)

    def setup_global_exception_handler(self):
        """Set up global exception handling."""
        sys.excepthook = self.handle_exception


# Global error handler instance
error_handler = ErrorHandler()


def handle_api_errors(func):
    """Decorator for handling API-related errors."""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"API error in {func.__name__}: {e}")
            # Add specific error handling logic here
            raise

    return wrapper


def handle_database_errors(func):
    """Decorator for handling database-related errors."""

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database error in {func.__name__}: {e}")
            # Add specific error handling logic here
            raise

    return wrapper


def handle_analysis_errors(func):
    """Decorator for handling analysis-related errors."""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Analysis error in {func.__name__}: {e}")
            # Add specific error handling logic here
            raise

    return wrapper
