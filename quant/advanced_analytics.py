"""
Advanced Analytics Module

Handles comprehensive performance analytics, risk-adjusted metrics,
and market regime detection for the trading system.
"""

import json
import statistics
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

import numpy as np
from sqlalchemy import text

from quant.database_manager import db
from quant.utils.logger import get_logger
from quant.performance_optimizer import performance_optimizer

logger = get_logger(__name__)


class MarketRegime(Enum):
    """Market regime enumeration."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNDEFINED = "undefined"


@dataclass
class RiskMetrics:
    """Risk-adjusted performance metrics."""
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    expectancy: float
    kelly_criterion: float
    risk_of_ruin: float
    cagr: float


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade: float
    risk_metrics: RiskMetrics
    period_days: int


class AdvancedAnalytics:
    """Advanced analytics engine for trading performance."""

    def __init__(self):
        self.logger = logger
        self.risk_free_rate = 0.02  # 2% annual risk-free rate

    def calculate_risk_adjusted_metrics(self, trades: List[Dict[str, Any]]) -> RiskMetrics:
        """Calculate comprehensive risk-adjusted performance metrics."""
        if not trades:
            return RiskMetrics(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)

        # Extract P&L values
        pnl_values = [trade.get("pnl", 0) for trade in trades if trade.get("pnl") is not None]
        
        if not pnl_values:
            return RiskMetrics(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)

        # Basic metrics
        wins = [pnl for pnl in pnl_values if pnl > 0]
        losses = [pnl for pnl in pnl_values if pnl < 0]
        
        win_rate = len(wins) / len(pnl_values) if pnl_values else 0
        avg_win = statistics.mean(wins) if wins else 0
        avg_loss = statistics.mean(losses) if losses else 0
        
        # Profit factor
        total_wins = sum(wins)
        total_losses = abs(sum(losses)) if losses else 1
        profit_factor = total_wins / total_losses if total_losses > 0 else 0
        
        # Expectancy
        expectancy = (win_rate * avg_win) - ((1 - win_rate) * abs(avg_loss))
        
        # Kelly criterion
        if avg_win > 0 and avg_loss < 0:
            kelly = ((win_rate * avg_win) - ((1 - win_rate) * abs(avg_loss))) / avg_win
        else:
            kelly = 0
        
        # Risk of ruin (simplified calculation)
        if avg_loss < 0 and win_rate > 0.5:
            risk_of_ruin = ((1 - win_rate) / win_rate) ** (1000 / abs(avg_loss))
        else:
            risk_of_ruin = 1.0
        
        # Drawdown analysis
        cumulative_returns = np.cumsum(pnl_values)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (peak - cumulative_returns) / peak
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0
        
        # Calmar ratio (annualized return / max drawdown)
        if max_drawdown > 0:
            total_return = cumulative_returns[-1] if len(cumulative_returns) > 0 else 0
            annualized_return = total_return * (365 / len(pnl_values)) if len(pnl_values) > 0 else 0
            calmar_ratio = annualized_return / max_drawdown
        else:
            calmar_ratio = 0
        
        # CAGR (Compound Annual Growth Rate)
        if len(pnl_values) > 1:
            total_return = sum(pnl_values)
            years = len(pnl_values) / 365  # Assuming daily trades
            cagr = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
        else:
            cagr = 0
        
        # Sharpe ratio
        if len(pnl_values) > 1:
            returns = np.array(pnl_values)
            excess_returns = returns - (self.risk_free_rate / 252)  # Daily risk-free rate
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) if np.std(excess_returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Sortino ratio (only considers downside risk)
        if len(pnl_values) > 1:
            returns = np.array(pnl_values)
            downside_returns = returns[returns < 0]
            if len(downside_returns) > 0:
                downside_deviation = np.std(downside_returns)
                sortino_ratio = np.mean(returns) / downside_deviation if downside_deviation > 0 else 0
            else:
                sortino_ratio = 0
        else:
            sortino_ratio = 0
        
        return RiskMetrics(
            sharpe_ratio=round(sharpe_ratio, 3),
            sortino_ratio=round(sortino_ratio, 3),
            max_drawdown=round(max_drawdown, 4),
            calmar_ratio=round(calmar_ratio, 3),
            win_rate=round(win_rate, 3),
            profit_factor=round(profit_factor, 3),
            expectancy=round(expectancy, 2),
            kelly_criterion=round(kelly, 3),
            risk_of_ruin=round(risk_of_ruin, 4),
            cagr=round(cagr, 4)
        )

    def analyze_performance_by_market_condition(self, days: int = 30) -> Dict[str, Any]:
        """Analyze performance by market conditions and regimes."""
        try:
            # Check cache first
            cached_result = performance_optimizer.get_cached_analytics("market_condition", f"{days}d")
            if cached_result is not None:
                return cached_result
            
            start_date = datetime.now() - timedelta(days=days)
            
            with db.get_session() as session:
                # Get trades with market regime information
                query = text("""
                    SELECT 
                        signal_timestamp,
                        direction,
                        entry_price,
                        exit_price,
                        pnl,
                        status,
                        confidence_score,
                        market_regime_score,
                        trend_strength_score,
                        volatility_score,
                        market_state,
                        trigger_pattern
                    FROM trade_history 
                    WHERE signal_timestamp >= :start_date 
                    AND status IN ('WIN', 'LOSS')
                    AND pnl IS NOT NULL
                    ORDER BY signal_timestamp
                """)
                
                results = session.execute(query, {"start_date": start_date}).fetchall()
                
                if not results:
                    return {"message": "No trade data available for analysis"}
                
                trades = []
                for row in results:
                    trades.append({
                        "timestamp": row.signal_timestamp,
                        "direction": row.direction,
                        "entry_price": row.entry_price,
                        "exit_price": row.exit_price,
                        "pnl": row.pnl,
                        "status": row.status,
                        "confidence_score": row.confidence_score,
                        "market_regime_score": row.market_regime_score,
                        "trend_strength_score": row.trend_strength_score,
                        "volatility_score": row.volatility_score,
                        "market_state": row.market_state,
                        "trigger_pattern": row.trigger_pattern
                    })
                
                # Analyze by market regime
                regime_analysis = self._analyze_by_regime(trades)
                
                # Analyze by confidence level
                confidence_analysis = self._analyze_by_confidence(trades)
                
                # Analyze by market state
                market_state_analysis = self._analyze_by_market_state(trades)
                
                # Analyze by trigger pattern
                trigger_analysis = self._analyze_by_trigger_pattern(trades)
                
                # Overall risk metrics
                risk_metrics = self.calculate_risk_adjusted_metrics(trades)
                
                result = {
                    "period_days": days,
                    "total_trades": len(trades),
                    "regime_analysis": regime_analysis,
                    "confidence_analysis": confidence_analysis,
                    "market_state_analysis": market_state_analysis,
                    "trigger_pattern_analysis": trigger_analysis,
                    "risk_metrics": asdict(risk_metrics),
                    "recommendations": self._generate_recommendations(trades, risk_metrics)
                }
                
                # Cache the result
                performance_optimizer.cache_analytics("market_condition", f"{days}d", result, ttl=1800)
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error analyzing performance by market condition: {e}")
            return {"error": str(e)}

    def _analyze_by_regime(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by market regime."""
        regimes = {}
        
        for trade in trades:
            regime_score = trade.get("market_regime_score", 0.5)
            
            # Determine regime
            if regime_score > 0.6:
                regime = "bullish"
            elif regime_score < 0.4:
                regime = "bearish"
            else:
                regime = "neutral"
            
            if regime not in regimes:
                regimes[regime] = {"trades": [], "wins": 0, "losses": 0, "total_pnl": 0}
            
            regimes[regime]["trades"].append(trade)
            if trade["status"] == "WIN":
                regimes[regime]["wins"] += 1
            else:
                regimes[regime]["losses"] += 1
            regimes[regime]["total_pnl"] += trade.get("pnl", 0)
        
        # Calculate metrics for each regime
        regime_metrics = {}
        for regime, data in regimes.items():
            trades_count = len(data["trades"])
            win_rate = data["wins"] / trades_count if trades_count > 0 else 0
            avg_pnl = data["total_pnl"] / trades_count if trades_count > 0 else 0
            
            risk_metrics = self.calculate_risk_adjusted_metrics(data["trades"])
            
            regime_metrics[regime] = {
                "trades": trades_count,
                "wins": data["wins"],
                "losses": data["losses"],
                "win_rate": round(win_rate, 3),
                "total_pnl": round(data["total_pnl"], 2),
                "avg_pnl": round(avg_pnl, 2),
                "risk_metrics": asdict(risk_metrics)
            }
        
        return regime_metrics

    def _analyze_by_confidence(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by confidence level."""
        confidence_levels = {
            "high": {"min": 0.8, "max": 1.0, "trades": []},
            "medium": {"min": 0.6, "max": 0.8, "trades": []},
            "low": {"min": 0.0, "max": 0.6, "trades": []}
        }
        
        for trade in trades:
            confidence = trade.get("confidence_score", 0.5)
            
            for level, config in confidence_levels.items():
                if config["min"] <= confidence < config["max"]:
                    config["trades"].append(trade)
                    break
        
        # Calculate metrics for each confidence level
        confidence_metrics = {}
        for level, config in confidence_levels.items():
            trades_list = config["trades"]
            if not trades_list:
                continue
            
            wins = len([t for t in trades_list if t["status"] == "WIN"])
            losses = len([t for t in trades_list if t["status"] == "LOSS"])
            win_rate = wins / len(trades_list) if trades_list else 0
            total_pnl = sum(t.get("pnl", 0) for t in trades_list)
            avg_pnl = total_pnl / len(trades_list) if trades_list else 0
            
            risk_metrics = self.calculate_risk_adjusted_metrics(trades_list)
            
            confidence_metrics[level] = {
                "trades": len(trades_list),
                "wins": wins,
                "losses": losses,
                "win_rate": round(win_rate, 3),
                "total_pnl": round(total_pnl, 2),
                "avg_pnl": round(avg_pnl, 2),
                "risk_metrics": asdict(risk_metrics)
            }
        
        return confidence_metrics

    def _analyze_by_market_state(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by market state."""
        states = {}
        
        for trade in trades:
            state = trade.get("market_state", "unknown")
            
            if state not in states:
                states[state] = {"trades": [], "wins": 0, "losses": 0, "total_pnl": 0}
            
            states[state]["trades"].append(trade)
            if trade["status"] == "WIN":
                states[state]["wins"] += 1
            else:
                states[state]["losses"] += 1
            states[state]["total_pnl"] += trade.get("pnl", 0)
        
        # Calculate metrics for each state
        state_metrics = {}
        for state, data in states.items():
            trades_count = len(data["trades"])
            win_rate = data["wins"] / trades_count if trades_count > 0 else 0
            avg_pnl = data["total_pnl"] / trades_count if trades_count > 0 else 0
            
            risk_metrics = self.calculate_risk_adjusted_metrics(data["trades"])
            
            state_metrics[state] = {
                "trades": trades_count,
                "wins": data["wins"],
                "losses": data["losses"],
                "win_rate": round(win_rate, 3),
                "total_pnl": round(data["total_pnl"], 2),
                "avg_pnl": round(avg_pnl, 2),
                "risk_metrics": asdict(risk_metrics)
            }
        
        return state_metrics

    def _analyze_by_trigger_pattern(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by trigger pattern."""
        patterns = {}
        
        for trade in trades:
            pattern = trade.get("trigger_pattern", "unknown")
            
            if pattern not in patterns:
                patterns[pattern] = {"trades": [], "wins": 0, "losses": 0, "total_pnl": 0}
            
            patterns[pattern]["trades"].append(trade)
            if trade["status"] == "WIN":
                patterns[pattern]["wins"] += 1
            else:
                patterns[pattern]["losses"] += 1
            patterns[pattern]["total_pnl"] += trade.get("pnl", 0)
        
        # Calculate metrics for each pattern
        pattern_metrics = {}
        for pattern, data in patterns.items():
            trades_count = len(data["trades"])
            win_rate = data["wins"] / trades_count if trades_count > 0 else 0
            avg_pnl = data["total_pnl"] / trades_count if trades_count > 0 else 0
            
            risk_metrics = self.calculate_risk_adjusted_metrics(data["trades"])
            
            pattern_metrics[pattern] = {
                "trades": trades_count,
                "wins": data["wins"],
                "losses": data["losses"],
                "win_rate": round(win_rate, 3),
                "total_pnl": round(data["total_pnl"], 2),
                "avg_pnl": round(avg_pnl, 2),
                "risk_metrics": asdict(risk_metrics)
            }
        
        return pattern_metrics

    def _generate_recommendations(self, trades: List[Dict[str, Any]], risk_metrics: RiskMetrics) -> List[str]:
        """Generate trading recommendations based on analysis."""
        recommendations = []
        
        # Risk-based recommendations
        if risk_metrics.sharpe_ratio < 1.0:
            recommendations.append("Low Sharpe ratio - consider reducing position sizes or improving strategy")
        
        if risk_metrics.max_drawdown > 0.2:
            recommendations.append("High maximum drawdown - implement stricter risk management")
        
        if risk_metrics.risk_of_ruin > 0.1:
            recommendations.append("High risk of ruin - significantly reduce position sizes")
        
        if risk_metrics.profit_factor < 1.2:
            recommendations.append("Low profit factor - review entry/exit criteria")
        
        # Performance-based recommendations
        if len(trades) > 10:
            recent_trades = trades[-10:]  # Last 10 trades
            recent_wins = len([t for t in recent_trades if t["status"] == "WIN"])
            recent_win_rate = recent_wins / len(recent_trades)
            
            if recent_win_rate < 0.3:
                recommendations.append("Recent performance poor - consider pausing trading")
            elif recent_win_rate > 0.7:
                recommendations.append("Recent performance excellent - consider increasing position sizes")
        
        # Confidence-based recommendations
        high_confidence_trades = [t for t in trades if t.get("confidence_score", 0) >= 0.8]
        if high_confidence_trades:
            high_confidence_wins = len([t for t in high_confidence_trades if t["status"] == "WIN"])
            high_confidence_win_rate = high_confidence_wins / len(high_confidence_trades)
            
            if high_confidence_win_rate > 0.7:
                recommendations.append("High confidence trades perform well - focus on high-confidence setups")
        
        return recommendations

    def detect_market_regime(self, market_data: Dict[str, Any]) -> MarketRegime:
        """Detect current market regime based on market data."""
        try:
            # Extract key indicators
            volatility = market_data.get("volatility", 0.2)
            trend_strength = market_data.get("trend_strength", 0.5)
            price_change = market_data.get("price_change_pct", 0.0)
            
            # Determine regime based on indicators
            if volatility > 0.3:
                return MarketRegime.VOLATILE
            elif abs(price_change) > 5.0:  # 5% price change
                return MarketRegime.BULLISH if price_change > 0 else MarketRegime.BEARISH
            elif trend_strength > 0.7:
                return MarketRegime.BULLISH
            elif trend_strength < 0.3:
                return MarketRegime.BEARISH
            else:
                return MarketRegime.SIDEWAYS
                
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.UNDEFINED

    def get_comprehensive_performance_report(self, days: int = 30) -> Dict[str, Any]:
        """Generate comprehensive performance report with all analytics."""
        try:
            # Get basic performance data
            performance_data = db.get_performance_analytics(days)
            
            # Get market regime analysis
            regime_analysis = self.analyze_performance_by_market_condition(days)
            
            # Get risk metrics
            if isinstance(regime_analysis, dict) and "risk_metrics" in regime_analysis:
                risk_metrics = regime_analysis["risk_metrics"]
            else:
                risk_metrics = {}
            
            # Compile comprehensive report
            report = {
                "report_timestamp": datetime.now().isoformat(),
                "period_days": days,
                "performance_summary": performance_data,
                "market_regime_analysis": regime_analysis,
                "risk_metrics": risk_metrics,
                "trading_recommendations": regime_analysis.get("recommendations", []) if isinstance(regime_analysis, dict) else [],
                "cache_stats": performance_optimizer.get_performance_report()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive performance report: {e}")
            return {"error": str(e)}

    def get_trading_efficiency_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Calculate trading efficiency metrics."""
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            with db.get_session() as session:
                # Get trade efficiency data
                query = text("""
                    SELECT 
                        signal_timestamp,
                        exit_timestamp,
                        entry_price,
                        exit_price,
                        pnl,
                        confidence_score,
                        suggested_bet,
                        status
                    FROM trade_history 
                    WHERE signal_timestamp >= :start_date 
                    AND status IN ('WIN', 'LOSS')
                    AND exit_timestamp IS NOT NULL
                    ORDER BY signal_timestamp
                """)
                
                results = session.execute(query, {"start_date": start_date}).fetchall()
                
                if not results:
                    return {"message": "No trade data available for efficiency analysis"}
                
                # Calculate efficiency metrics
                trades = []
                for row in results:
                    hold_time = (row.exit_timestamp - row.signal_timestamp).total_seconds() / 3600  # hours
                    efficiency_score = 0
                    
                    if row.status == "WIN":
                        # Efficiency based on hold time vs profit
                        if hold_time > 0:
                            efficiency_score = row.pnl / hold_time  # P&L per hour
                    else:
                        # Negative efficiency for losses
                        if hold_time > 0:
                            efficiency_score = row.pnl / hold_time
                    
                    trades.append({
                        "hold_time_hours": hold_time,
                        "pnl": row.pnl,
                        "confidence_score": row.confidence_score,
                        "suggested_bet": row.suggested_bet,
                        "efficiency_score": efficiency_score,
                        "status": row.status
                    })
                
                # Calculate overall efficiency metrics
                avg_hold_time = statistics.mean([t["hold_time_hours"] for t in trades])
                avg_efficiency = statistics.mean([t["efficiency_score"] for t in trades])
                
                # Best and worst performing trades
                best_trade = max(trades, key=lambda x: x["efficiency_score"])
                worst_trade = min(trades, key=lambda x: x["efficiency_score"])
                
                # Efficiency by confidence level
                confidence_efficiency = {}
                for confidence_level in ["high", "medium", "low"]:
                    if confidence_level == "high":
                        level_trades = [t for t in trades if t["confidence_score"] >= 0.8]
                    elif confidence_level == "medium":
                        level_trades = [t for t in trades if 0.6 <= t["confidence_score"] < 0.8]
                    else:
                        level_trades = [t for t in trades if t["confidence_score"] < 0.6]
                    
                    if level_trades:
                        confidence_efficiency[confidence_level] = {
                            "avg_efficiency": statistics.mean([t["efficiency_score"] for t in level_trades]),
                            "avg_hold_time": statistics.mean([t["hold_time_hours"] for t in level_trades]),
                            "trade_count": len(level_trades)
                        }
                
                return {
                    "period_days": days,
                    "total_trades": len(trades),
                    "avg_hold_time_hours": round(avg_hold_time, 2),
                    "avg_efficiency_score": round(avg_efficiency, 4),
                    "best_trade": {
                        "efficiency_score": round(best_trade["efficiency_score"], 4),
                        "hold_time_hours": round(best_trade["hold_time_hours"], 2),
                        "pnl": best_trade["pnl"]
                    },
                    "worst_trade": {
                        "efficiency_score": round(worst_trade["efficiency_score"], 4),
                        "hold_time_hours": round(worst_trade["hold_time_hours"], 2),
                        "pnl": worst_trade["pnl"]
                    },
                    "confidence_efficiency": confidence_efficiency
                }
                
        except Exception as e:
            self.logger.error(f"Error calculating trading efficiency metrics: {e}")
            return {"error": str(e)}


# Global advanced analytics instance
advanced_analytics = AdvancedAnalytics()