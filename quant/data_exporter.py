"""
Data Exporter Module

Handles automatic and manual data export functionality.
"""

import json
import os
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Any

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class DataExporter:
    """Handles data export functionality for trading system."""

    def __init__(self, export_dir: str = "exports"):
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(exist_ok=True)

        # Create subdirectories
        (self.export_dir / "daily").mkdir(exist_ok=True)
        (self.export_dir / "trade_history").mkdir(exist_ok=True)
        (self.export_dir / "performance").mkdir(exist_ok=True)

    def export_trade_history_jsonl(
        self,
        start_date: date | None = None,
        end_date: date | None = None,
        output_file: str | None = None,
    ) -> str:
        """Export trade history to JSONL format."""
        try:
            if not start_date:
                start_date = date.today() - timedelta(days=1)  # Default to yesterday
            if not end_date:
                end_date = date.today()

            # Get trade history data
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            with db.get_session() as session:
                from quant.database_manager import TradeHistory

                trades = (
                    session.query(TradeHistory)
                    .filter(
                        TradeHistory.signal_timestamp >= start_datetime,
                        TradeHistory.signal_timestamp <= end_datetime,
                    )
                    .order_by(TradeHistory.signal_timestamp.desc())
                    .all()
                )

                # Prepare export data
                export_lines = []
                for trade in trades:
                    trade_data = {
                        "id": trade.id,
                        "signal_timestamp": trade.signal_timestamp.isoformat(),
                        "symbol": trade.symbol,
                        "direction": trade.direction,
                        "entry_price": trade.entry_price,
                        "confidence_score": trade.confidence_score,
                        "market_state": trade.market_state,
                        "trigger_pattern": trade.trigger_pattern,
                        "confirmed_indicators": (
                            json.loads(trade.confirmed_indicators)
                            if trade.confirmed_indicators
                            else []
                        ),
                        "suggested_bet": trade.suggested_bet,
                        "status": trade.status,
                        "exit_price": trade.exit_price,
                        "exit_timestamp": (
                            trade.exit_timestamp.isoformat()
                            if trade.exit_timestamp
                            else None
                        ),
                        "pnl": trade.pnl,
                        "decision_details": (
                            json.loads(trade.decision_details)
                            if trade.decision_details
                            else {}
                        ),
                        "confidence_breakdown": (
                            json.loads(trade.confidence_breakdown)
                            if trade.confidence_breakdown
                            else {}
                        ),
                        "market_regime_score": trade.market_regime_score,
                        "trend_strength_score": trade.trend_strength_score,
                        "entry_minutes": trade.entry_minutes,
                        "decision_context": (
                            json.loads(trade.decision_context)
                            if trade.decision_context
                            else {}
                        ),
                        "created_at": (
                            trade.created_at.isoformat() if trade.created_at else None
                        ),
                    }
                    export_lines.append(json.dumps(trade_data, ensure_ascii=False))

                # Generate output filename
                if not output_file:
                    filename = f"trade_history_{start_date}_to_{end_date}.jsonl"
                    output_file = str(self.export_dir / "trade_history" / filename)

                # Write to file
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write("\n".join(export_lines))

                logger.info(f"Exported {len(export_lines)} trades to {output_file}")
                return output_file

        except Exception as e:
            logger.error(f"Error exporting trade history: {e}")
            raise

    def export_daily_performance(
        self,
        start_date: date | None = None,
        end_date: date | None = None,
        output_file: str | None = None,
    ) -> str:
        """Export daily performance data to JSON format."""
        try:
            if not start_date:
                start_date = date.today() - timedelta(
                    days=30
                )  # Default to last 30 days
            if not end_date:
                end_date = date.today()

            # Get performance data
            performance_data = db.get_performance_range(start_date, end_date)

            # Prepare export data
            export_data = {
                "export_metadata": {
                    "export_type": "daily_performance",
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "export_timestamp": datetime.utcnow().isoformat(),
                    "total_days": len(performance_data),
                },
                "daily_performance": performance_data,
                "summary_statistics": self._calculate_performance_summary(
                    performance_data
                ),
            }

            # Generate output filename
            if not output_file:
                filename = f"daily_performance_{start_date}_to_{end_date}.json"
                output_file = str(self.export_dir / "performance" / filename)

            # Write to file
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Exported daily performance data to {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"Error exporting daily performance: {e}")
            raise

    def export_confidence_analysis(
        self, days: int = 30, output_file: str | None = None
    ) -> str:
        """Export confidence scoring analysis data."""
        try:
            # Get confidence performance stats
            confidence_stats = db.get_confidence_performance_stats(days)

            # Prepare export data
            export_data = {
                "export_metadata": {
                    "export_type": "confidence_analysis",
                    "analysis_period_days": days,
                    "export_timestamp": datetime.utcnow().isoformat(),
                },
                "confidence_statistics": confidence_stats,
            }

            # Generate output filename
            if not output_file:
                filename = f"confidence_analysis_{days}_days.json"
                output_file = str(self.export_dir / "performance" / filename)

            # Write to file
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Exported confidence analysis data to {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"Error exporting confidence analysis: {e}")
            raise

    def auto_export_daily_data(self) -> list[str]:
        """Automatically export previous day's data."""
        try:
            exported_files = []
            yesterday = date.today() - timedelta(days=1)

            # Export yesterday's trade history
            try:
                trade_file = self.export_trade_history_jsonl(
                    start_date=yesterday, end_date=yesterday
                )
                exported_files.append(trade_file)
            except Exception as e:
                logger.error(f"Failed to auto-export trade history: {e}")

            # Export performance data for the last 7 days
            try:
                start_date = date.today() - timedelta(days=7)
                perf_file = self.export_daily_performance(
                    start_date=start_date, end_date=yesterday
                )
                exported_files.append(perf_file)
            except Exception as e:
                logger.error(f"Failed to auto-export performance data: {e}")

            logger.info(f"Auto-export completed: {len(exported_files)} files exported")
            return exported_files

        except Exception as e:
            logger.error(f"Error in auto-export: {e}")
            return []

    def export_complete_dataset(
        self, start_date: date | None = None, output_file: str | None = None
    ) -> str:
        """Export complete dataset including trades, performance, and confidence analysis."""
        try:
            if not start_date:
                start_date = date.today() - timedelta(
                    days=30
                )  # Default to last 30 days
            end_date = date.today()

            # Get all data
            trade_history_file = self.export_trade_history_jsonl(start_date, end_date)
            performance_file = self.export_daily_performance(start_date, end_date)
            confidence_file = self.export_confidence_analysis(
                (end_date - start_date).days
            )

            # Create master export file with metadata
            export_data = {
                "export_metadata": {
                    "export_type": "complete_dataset",
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "export_timestamp": datetime.utcnow().isoformat(),
                    "included_files": {
                        "trade_history": os.path.basename(trade_history_file),
                        "daily_performance": os.path.basename(performance_file),
                        "confidence_analysis": os.path.basename(confidence_file),
                    },
                },
                "data_summary": {
                    "trade_history_file": trade_history_file,
                    "performance_file": performance_file,
                    "confidence_file": confidence_file,
                },
            }

            # Generate output filename
            if not output_file:
                filename = f"complete_dataset_{start_date}_to_{end_date}.json"
                output_file = str(self.export_dir / filename)

            # Write to file
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Complete dataset exported to {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"Error exporting complete dataset: {e}")
            raise

    def _calculate_performance_summary(
        self, performance_data: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Calculate summary statistics from performance data."""
        if not performance_data:
            return {}

        total_trades = sum(p["total_trades"] for p in performance_data)
        total_wins = sum(p["winning_trades"] for p in performance_data)
        total_losses = sum(p["losing_trades"] for p in performance_data)
        total_pnl = sum(p["total_pnl"] for p in performance_data)

        overall_win_rate = (total_wins / total_trades * 100) if total_trades > 0 else 0

        profitable_days = len([p for p in performance_data if p["total_pnl"] > 0])
        loss_days = len([p for p in performance_data if p["total_pnl"] < 0])

        avg_daily_pnl = total_pnl / len(performance_data) if performance_data else 0

        return {
            "total_trades": total_trades,
            "total_winning_trades": total_wins,
            "total_losing_trades": total_losses,
            "overall_win_rate": round(overall_win_rate, 2),
            "total_pnl": round(total_pnl, 2),
            "profitable_days": profitable_days,
            "loss_days": loss_days,
            "average_daily_pnl": round(avg_daily_pnl, 2),
            "days_analyzed": len(performance_data),
        }

    def cleanup_old_exports(self, days_to_keep: int = 30):
        """Clean up old export files."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            for subdir in ["daily", "trade_history", "performance"]:
                dir_path = self.export_dir / subdir
                for file_path in dir_path.glob("*"):
                    if (
                        file_path.is_file()
                        and file_path.stat().st_mtime < cutoff_date.timestamp()
                    ):
                        file_path.unlink()
                        logger.info(f"Deleted old export file: {file_path}")

            logger.info(f"Cleanup completed for exports older than {days_to_keep} days")

        except Exception as e:
            logger.error(f"Error cleaning up old exports: {e}")

    def get_export_stats(self) -> dict[str, Any]:
        """Get statistics about exported files."""
        try:
            stats = {
                "export_directory": str(self.export_dir),
                "total_files": 0,
                "files_by_type": {"daily": 0, "trade_history": 0, "performance": 0},
                "total_size_bytes": 0,
                "recent_exports": [],
            }

            for subdir in ["daily", "trade_history", "performance"]:
                dir_path = self.export_dir / subdir
                if dir_path.exists():
                    files = list(dir_path.glob("*"))
                    stats["files_by_type"][subdir] = len(files)
                    stats["total_files"] += len(files)

                    for file_path in files:
                        stats["total_size_bytes"] += file_path.stat().st_size

                        # Get recent exports (last 7 days)
                        if (
                            file_path.stat().st_mtime
                            > (datetime.now() - timedelta(days=7)).timestamp()
                        ):
                            stats["recent_exports"].append(
                                {
                                    "file": str(file_path),
                                    "size_bytes": file_path.stat().st_size,
                                    "modified": datetime.fromtimestamp(
                                        file_path.stat().st_mtime
                                    ).isoformat(),
                                }
                            )

            return stats

        except Exception as e:
            logger.error(f"Error getting export stats: {e}")
            return {"error": str(e)}


# Global data exporter instance
data_exporter = DataExporter()
