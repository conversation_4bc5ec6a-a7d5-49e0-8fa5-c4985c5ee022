"""
Confidence Scoring Visualization Module

Provides visualization capabilities for confidence scoring analysis and performance monitoring.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
import json

from quant.utils.logger import get_logger

logger = get_logger(__name__)

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class ConfidenceScoringVisualizer:
    """Visualizes confidence scoring data and performance metrics."""
    
    def __init__(self, database_manager=None):
        """Initialize visualizer with optional database manager."""
        self.db = database_manager
        self.logger = logger
        
        # Color scheme
        self.colors = {
            'bullish': '#2E8B57',     # SeaGreen
            'bearish': '#DC143C',     # Crimson
            'sideways': '#4682B4',   # SteelBlue
            'volatile': '#FF8C00',    # DarkOrange
            'high_confidence': '#228B22',  # ForestGreen
            'medium_confidence': '#FFD700',  # Gold
            'low_confidence': '#FF6347'     # Tomato
        }
    
    def plot_confidence_distribution(self, confidence_scores: List[float], 
                                   save_path: str = None) -> None:
        """
        Plot distribution of confidence scores.
        
        Args:
            confidence_scores: List of confidence scores
            save_path: Optional path to save the plot
        """
        try:
            plt.figure(figsize=(12, 8))
            
            # Create subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Confidence Score Distribution Analysis', fontsize=16, fontweight='bold')
            
            # 1. Histogram
            ax1.hist(confidence_scores, bins=30, alpha=0.7, color=self.colors['high_confidence'], edgecolor='black')
            ax1.axvline(np.mean(confidence_scores), color='red', linestyle='--', label=f'Mean: {np.mean(confidence_scores):.3f}')
            ax1.axvline(np.median(confidence_scores), color='blue', linestyle='--', label=f'Median: {np.median(confidence_scores):.3f}')
            ax1.set_xlabel('Confidence Score')
            ax1.set_ylabel('Frequency')
            ax1.set_title('Distribution of Confidence Scores')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 2. Box plot
            confidence_categories = ['Low (<0.6)', 'Medium (0.6-0.8)', 'High (>0.8)']
            low_scores = [s for s in confidence_scores if s < 0.6]
            medium_scores = [s for s in confidence_scores if 0.6 <= s < 0.8]
            high_scores = [s for s in confidence_scores if s >= 0.8]
            
            data_to_plot = [low_scores, medium_scores, high_scores]
            colors_box = [self.colors['low_confidence'], self.colors['medium_confidence'], self.colors['high_confidence']]
            
            bp = ax2.boxplot(data_to_plot, patch_artist=True, labels=confidence_categories)
            for patch, color in zip(bp['boxes'], colors_box):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax2.set_ylabel('Confidence Score')
            ax2.set_title('Confidence Score by Category')
            ax2.grid(True, alpha=0.3)
            
            # 3. Time series (if we have timestamps)
            if len(confidence_scores) > 1:
                x轴 = range(len(confidence_scores))
                ax3.plot(x轴, confidence_scores, marker='o', linewidth=1, markersize=3, alpha=0.7)
                ax3.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Minimum Threshold')
                ax3.axhline(y=0.8, color='green', linestyle='--', alpha=0.7, label='Strong Threshold')
                ax3.set_xlabel('Trade Sequence')
                ax3.set_ylabel('Confidence Score')
                ax3.set_title('Confidence Scores Over Time')
                ax3.legend()
                ax3.grid(True, alpha=0.3)
            
            # 4. Confidence level pie chart
            total = len(confidence_scores)
            low_count = len(low_scores)
            medium_count = len(medium_scores)
            high_count = len(high_scores)
            
            sizes = [low_count, medium_count, high_count]
            labels = ['Low Confidence', 'Medium Confidence', 'High Confidence']
            colors_pie = [self.colors['low_confidence'], self.colors['medium_confidence'], self.colors['high_confidence']]
            
            ax4.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
            ax4.set_title('Confidence Level Distribution')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Confidence distribution plot saved to {save_path}")
            
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Error plotting confidence distribution: {e}")
    
    def plot_performance_by_confidence(self, trades_data: List[Dict[str, Any]], 
                                    save_path: str = None) -> None:
        """
        Plot trading performance by confidence level.
        
        Args:
            trades_data: List of trade dictionaries with confidence and outcome data
            save_path: Optional path to save the plot
        """
        try:
            if not trades_data:
                self.logger.warning("No trade data provided for performance analysis")
                return
            
            # Prepare data
            df = pd.DataFrame(trades_data)
            
            # Create confidence categories
            df['confidence_category'] = pd.cut(
                df['confidence_score'], 
                bins=[0, 0.6, 0.8, 1.0], 
                labels=['Low', 'Medium', 'High'],
                include_lowest=True
            )
            
            # Calculate win rates by confidence category
            confidence_performance = df.groupby('confidence_category').agg({
                'confidence_score': ['count', 'mean'],
                'status': lambda x: (x == 'WIN').mean() * 100
            }).round(2)
            
            plt.figure(figsize=(15, 10))
            
            # Create subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Trading Performance by Confidence Level', fontsize=16, fontweight='bold')
            
            # 1. Win rate by confidence category
            categories = confidence_performance.index
            win_rates = confidence_performance['status']['<lambda>']
            colors_bars = [self.colors['low_confidence'], self.colors['medium_confidence'], self.colors['high_confidence']]
            
            bars = ax1.bar(categories, win_rates, color=colors_bars, alpha=0.7)
            ax1.set_ylabel('Win Rate (%)')
            ax1.set_title('Win Rate by Confidence Category')
            ax1.set_ylim(0, 100)
            
            # Add value labels on bars
            for bar, rate in zip(bars, win_rates):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{rate:.1f}%', ha='center', va='bottom')
            
            ax1.grid(True, alpha=0.3)
            
            # 2. Trade count by confidence category
            trade_counts = confidence_performance['confidence_score']['count']
            ax2.pie(trade_counts, labels=categories, colors=colors_bars, autopct='%1.1f%%', startangle=90)
            ax2.set_title('Trade Distribution by Confidence Category')
            
            # 3. Confidence vs Outcome scatter plot
            outcomes_numeric = [1 if trade.get('status') == 'WIN' else 0 for trade in trades_data]
            confidences = [trade.get('confidence_score', 0.5) for trade in trades_data]
            
            colors_scatter = ['green' if outcome == 1 else 'red' for outcome in outcomes_numeric]
            ax3.scatter(confidences, outcomes_numeric, c=colors_scatter, alpha=0.6)
            ax3.set_xlabel('Confidence Score')
            ax3.set_ylabel('Trade Outcome (1=Win, 0=Loss)')
            ax3.set_title('Confidence Score vs Trade Outcome')
            ax3.set_ylim(-0.1, 1.1)
            ax3.grid(True, alpha=0.3)
            
            # 4. PnL distribution by confidence level
            if 'pnl' in df.columns:
                for category, color in zip(categories, colors_bars):
                    category_data = df[df['confidence_category'] == category]['pnl'].dropna()
                    if len(category_data) > 0:
                        ax4.hist(category_data, alpha=0.5, label=category, color=color, bins=20)
                
                ax4.set_xlabel('Profit/Loss')
                ax4.set_ylabel('Frequency')
                ax4.set_title('PnL Distribution by Confidence Category')
                ax4.legend()
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Performance by confidence plot saved to {save_path}")
            
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Error plotting performance by confidence: {e}")
    
    def plot_market_regime_analysis(self, trades_data: List[Dict[str, Any]], 
                                 save_path: str = None) -> None:
        """
        Plot analysis by market regime.
        
        Args:
            trades_data: List of trade dictionaries with market regime data
            save_path: Optional path to save the plot
        """
        try:
            if not trades_data:
                self.logger.warning("No trade data provided for market regime analysis")
                return
            
            # Prepare data
            df = pd.DataFrame(trades_data)
            
            # Extract market regime from confidence breakdown
            df['market_regime'] = df.apply(
                lambda row: row.get('confidence_breakdown', {}).get('market_regime', 'unknown'), 
                axis=1
            )
            
            # Filter out unknown regimes
            df = df[df['market_regime'] != 'unknown']
            
            if df.empty:
                self.logger.warning("No valid market regime data found")
                return
            
            plt.figure(figsize=(15, 10))
            
            # Create subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Market Regime Analysis', fontsize=16, fontweight='bold')
            
            # 1. Win rate by market regime
            regime_performance = df.groupby('market_regime').agg({
                'confidence_score': 'count',
                'status': lambda x: (x == 'WIN').mean() * 100
            }).round(2)
            
            regimes = regime_performance.index
            win_rates = regime_performance['status']
            colors_regime = [self.colors.get(regime, '#808080') for regime in regimes]
            
            bars = ax1.bar(regimes, win_rates, color=colors_regime, alpha=0.7)
            ax1.set_ylabel('Win Rate (%)')
            ax1.set_title('Win Rate by Market Regime')
            ax1.set_ylim(0, 100)
            
            # Add value labels on bars
            for bar, rate in zip(bars, win_rates):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{rate:.1f}%', ha='center', va='bottom')
            
            ax1.grid(True, alpha=0.3)
            
            # 2. Trade count by market regime
            trade_counts = regime_performance['confidence_score']
            ax2.pie(trade_counts, labels=regimes, colors=colors_regime, autopct='%1.1f%%', startangle=90)
            ax2.set_title('Trade Distribution by Market Regime')
            
            # 3. Average confidence by market regime
            avg_confidence = df.groupby('market_regime')['confidence_score'].mean()
            ax3.bar(regimes, avg_confidence, color=colors_regime, alpha=0.7)
            ax3.set_ylabel('Average Confidence Score')
            ax3.set_title('Average Confidence by Market Regime')
            ax3.set_ylim(0, 1)
            ax3.grid(True, alpha=0.3)
            
            # 4. Market regime over time (if we have timestamps)
            if 'signal_timestamp' in df.columns:
                df['signal_timestamp'] = pd.to_datetime(df['signal_timestamp'])
                df_sorted = df.sort_values('signal_timestamp')
                
                # Create a numeric representation for regimes
                regime_mapping = {regime: i for i, regime in enumerate(regimes)}
                df_sorted['regime_numeric'] = df_sorted['market_regime'].map(regime_mapping)
                
                ax4.scatter(df_sorted['signal_timestamp'], df_sorted['regime_numeric'], 
                           c=[self.colors.get(regime, '#808080') for regime in df_sorted['market_regime']], 
                           alpha=0.6)
                ax4.set_xlabel('Time')
                ax4.set_ylabel('Market Regime')
                ax4.set_yticks(range(len(regimes)))
                ax4.set_yticklabels(regimes)
                ax4.set_title('Market Regime Over Time')
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Market regime analysis plot saved to {save_path}")
            
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Error plotting market regime analysis: {e}")
    
    def plot_confidence_breakdown(self, confidence_breakdowns: List[Dict[str, Any]], 
                                save_path: str = None) -> None:
        """
        Plot detailed confidence breakdown analysis.
        
        Args:
            confidence_breakdowns: List of confidence breakdown dictionaries
            save_path: Optional path to save the plot
        """
        try:
            if not confidence_breakdowns:
                self.logger.warning("No confidence breakdown data provided")
                return
            
            # Prepare data
            df = pd.DataFrame(confidence_breakdowns)
            
            # Extract component scores
            components = ['trend_score', 'momentum_score', 'volatility_score', 'volume_score', 'market_regime_score']
            component_data = df[components].dropna()
            
            plt.figure(figsize=(15, 10))
            
            # Create subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Confidence Breakdown Analysis', fontsize=16, fontweight='bold')
            
            # 1. Component score distribution
            component_means = component_data.mean()
            component_stds = component_data.std()
            
            x_pos = np.arange(len(components))
            ax1.bar(x_pos, component_means, yerr=component_stds, capsize=5, alpha=0.7, 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
            ax1.set_xlabel('Component')
            ax1.set_ylabel('Average Score')
            ax1.set_title('Average Component Scores with Standard Deviation')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels([c.replace('_score', '').title() for c in components], rotation=45)
            ax1.set_ylim(0, 1)
            ax1.grid(True, alpha=0.3)
            
            # 2. Component correlation heatmap
            correlation_matrix = component_data.corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                       square=True, ax=ax2, cbar_kws={'label': 'Correlation'})
            ax2.set_title('Component Score Correlations')
            
            # 3. Component trends over time
            if len(component_data) > 1:
                for component in components:
                    ax3.plot(component_data.index, component_data[component], 
                            label=component.replace('_score', '').title(), alpha=0.7)
                ax3.set_xlabel('Sample Index')
                ax3.set_ylabel('Score')
                ax3.set_title('Component Score Trends')
                ax3.legend()
                ax3.grid(True, alpha=0.3)
                ax3.set_ylim(0, 1)
            
            # 4. Radar chart for average scores
            angles = np.linspace(0, 2 * np.pi, len(components), endpoint=False).tolist()
            values = component_means.tolist()
            values += values[:1]  # Complete the circle
            angles += angles[:1]
            
            ax4 = plt.subplot(2, 2, 4, projection='polar')
            ax4.plot(angles, values, 'o-', linewidth=2, alpha=0.7)
            ax4.fill(angles, values, alpha=0.25)
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels([c.replace('_score', '').title() for c in components])
            ax4.set_ylim(0, 1)
            ax4.set_title('Average Component Scores (Radar Chart)')
            ax4.grid(True)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Confidence breakdown plot saved to {save_path}")
            
            plt.show()
            
        except Exception as e:
            self.logger.error(f"Error plotting confidence breakdown: {e}")
    
    def generate_comprehensive_report(self, trades_data: List[Dict[str, Any]], 
                                   save_dir: str = None) -> Dict[str, str]:
        """
        Generate comprehensive confidence scoring report with multiple visualizations.
        
        Args:
            trades_data: List of trade dictionaries
            save_dir: Directory to save report files
            
        Returns:
            Dictionary with paths to generated plots
        """
        try:
            if save_dir:
                import os
                os.makedirs(save_dir, exist_ok=True)
            
            report_plots = {}
            
            # 1. Confidence distribution
            confidence_scores = [trade.get('confidence_score', 0.5) for trade in trades_data]
            save_path = f"{save_dir}/confidence_distribution.png" if save_dir else None
            self.plot_confidence_distribution(confidence_scores, save_path)
            report_plots['confidence_distribution'] = save_path or "displayed"
            
            # 2. Performance by confidence
            save_path = f"{save_dir}/performance_by_confidence.png" if save_dir else None
            self.plot_performance_by_confidence(trades_data, save_path)
            report_plots['performance_by_confidence'] = save_path or "displayed"
            
            # 3. Market regime analysis
            save_path = f"{save_dir}/market_regime_analysis.png" if save_dir else None
            self.plot_market_regime_analysis(trades_data, save_path)
            report_plots['market_regime_analysis'] = save_path or "displayed"
            
            # 4. Confidence breakdown
            confidence_breakdowns = [trade.get('confidence_breakdown', {}) for trade in trades_data]
            confidence_breakdowns = [cb for cb in confidence_breakdowns if cb]  # Remove empty ones
            save_path = f"{save_dir}/confidence_breakdown.png" if save_dir else None
            self.plot_confidence_breakdown(confidence_breakdowns, save_path)
            report_plots['confidence_breakdown'] = save_path or "displayed"
            
            self.logger.info(f"Comprehensive report generated with {len(report_plots)} plots")
            
            return report_plots
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive report: {e}")
            return {}
    
    def create_summary_statistics(self, trades_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create summary statistics for confidence scoring performance.
        
        Args:
            trades_data: List of trade dictionaries
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            if not trades_data:
                return {"error": "No trade data provided"}
            
            df = pd.DataFrame(trades_data)
            
            # Basic statistics
            confidence_scores = df['confidence_score'].dropna()
            basic_stats = {
                "total_trades": len(df),
                "trades_with_confidence": len(confidence_scores),
                "average_confidence": float(confidence_scores.mean()) if len(confidence_scores) > 0 else 0,
                "confidence_std": float(confidence_scores.std()) if len(confidence_scores) > 0 else 0,
                "min_confidence": float(confidence_scores.min()) if len(confidence_scores) > 0 else 0,
                "max_confidence": float(confidence_scores.max()) if len(confidence_scores) > 0 else 0,
            }
            
            # Performance by confidence level
            df['confidence_category'] = pd.cut(
                df['confidence_score'], 
                bins=[0, 0.6, 0.8, 1.0], 
                labels=['Low', 'Medium', 'High'],
                include_lowest=True
            )
            
            performance_stats = {}
            for category in ['Low', 'Medium', 'High']:
                category_data = df[df['confidence_category'] == category]
                if len(category_data) > 0:
                    win_rate = (category_data['status'] == 'WIN').mean() * 100
                    performance_stats[category.lower()] = {
                        "trade_count": len(category_data),
                        "win_rate": round(win_rate, 2),
                        "avg_confidence": round(category_data['confidence_score'].mean(), 3)
                    }
            
            # Market regime analysis
            if 'confidence_breakdown' in df.columns:
                df['market_regime'] = df.apply(
                    lambda row: row.get('confidence_breakdown', {}).get('market_regime', 'unknown'), 
                    axis=1
                )
                
                regime_stats = {}
                for regime in df['market_regime'].unique():
                    if regime != 'unknown':
                        regime_data = df[df['market_regime'] == regime]
                        win_rate = (regime_data['status'] == 'WIN').mean() * 100
                        regime_stats[regime] = {
                            "trade_count": len(regime_data),
                            "win_rate": round(win_rate, 2),
                            "avg_confidence": round(regime_data['confidence_score'].mean(), 3)
                        }
            else:
                regime_stats = {}
            
            return {
                "basic_statistics": basic_stats,
                "performance_by_confidence": performance_stats,
                "market_regime_performance": regime_stats,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error creating summary statistics: {e}")
            return {"error": str(e)}