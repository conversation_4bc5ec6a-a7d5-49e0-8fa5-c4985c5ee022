"""
Performance Tracker Module

Handles daily performance tracking and statistics generation.
"""

import json
from datetime import date, datetime, timedelta
from typing import Any

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceTracker:
    """Tracks and manages daily trading performance."""

    def __init__(self):
        self.reset_hour = 0  # Midnight reset (0 hour)
        self.reset_minute = 0  # Midnight reset (0 minute)

    def get_current_performance(self) -> dict[str, Any]:
        """Get current day's performance statistics."""
        try:
            today = date.today()
            daily_perf = db.get_daily_performance(today)

            if daily_perf:
                return daily_perf
            else:
                # Calculate real-time performance
                return self._calculate_realtime_performance(today)

        except Exception as e:
            logger.error(f"Error getting current performance: {e}")
            return self._get_empty_performance()

    def _calculate_realtime_performance(self, target_date: date) -> dict[str, Any]:
        """Calculate real-time performance for a specific date."""
        try:
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = start_datetime + timedelta(days=1)

            with db.get_session() as session:
                from quant.database_manager import TradeHistory

                trades = (
                    session.query(TradeHistory)
                    .filter(
                        TradeHistory.signal_timestamp >= start_datetime,
                        TradeHistory.signal_timestamp < end_datetime,
                        TradeHistory.status.in_(["WIN", "LOSS"]),
                    )
                    .all()
                )

                total_trades = len(trades)
                winning_trades = len([t for t in trades if t.status == "WIN"])
                losing_trades = len([t for t in trades if t.status == "LOSS"])
                total_pnl = sum(t.pnl for t in trades if t.pnl is not None)
                win_rate = (
                    (winning_trades / total_trades * 100) if total_trades > 0 else 0
                )

                return {
                    "date": target_date.isoformat(),
                    "total_trades": total_trades,
                    "winning_trades": winning_trades,
                    "losing_trades": losing_trades,
                    "win_rate": round(win_rate, 2),
                    "total_pnl": round(total_pnl, 2),
                    "generated_at": datetime.utcnow().isoformat(),
                    "is_realtime": True,
                }

        except Exception as e:
            logger.error(f"Error calculating realtime performance: {e}")
            return self._get_empty_performance()

    def _get_empty_performance(self) -> dict[str, Any]:
        """Get empty performance structure."""
        return {
            "date": date.today().isoformat(),
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            "total_pnl": 0.0,
            "generated_at": datetime.utcnow().isoformat(),
            "is_realtime": False,
        }

    def should_reset_daily_stats(self) -> bool:
        """Check if daily stats should be reset (midnight)."""
        now = datetime.now()
        return now.hour == self.reset_hour and now.minute == self.reset_minute

    def get_performance_summary(self, days: int = 7) -> dict[str, Any]:
        """Get performance summary for the last N days."""
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days - 1)

            performance_data = db.get_performance_range(start_date, end_date)

            if not performance_data:
                return self._get_empty_summary(days)

            # Calculate summary statistics
            total_trades = sum(p["total_trades"] for p in performance_data)
            total_wins = sum(p["winning_trades"] for p in performance_data)
            total_losses = sum(p["losing_trades"] for p in performance_data)
            total_pnl = sum(p["total_pnl"] for p in performance_data)

            overall_win_rate = (
                (total_wins / total_trades * 100) if total_trades > 0 else 0
            )

            # Find best and worst days
            best_day = (
                max(performance_data, key=lambda x: x["total_pnl"])
                if performance_data
                else None
            )
            worst_day = (
                min(performance_data, key=lambda x: x["total_pnl"])
                if performance_data
                else None
            )

            # Calculate average daily performance
            avg_daily_trades = total_trades / days if days > 0 else 0
            avg_daily_pnl = total_pnl / days if days > 0 else 0

            return {
                "period_days": days,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "total_trades": total_trades,
                "total_winning_trades": total_wins,
                "total_losing_trades": total_losses,
                "overall_win_rate": round(overall_win_rate, 2),
                "total_pnl": round(total_pnl, 2),
                "average_daily_trades": round(avg_daily_trades, 1),
                "average_daily_pnl": round(avg_daily_pnl, 2),
                "best_day": (
                    {
                        "date": best_day["date"],
                        "pnl": best_day["total_pnl"],
                        "win_rate": best_day["win_rate"],
                    }
                    if best_day
                    else None
                ),
                "worst_day": (
                    {
                        "date": worst_day["date"],
                        "pnl": worst_day["total_pnl"],
                        "win_rate": worst_day["win_rate"],
                    }
                    if worst_day
                    else None
                ),
                "daily_breakdown": performance_data,
                "generated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return self._get_empty_summary(days)

    def _get_empty_summary(self, days: int) -> dict[str, Any]:
        """Get empty performance summary structure."""
        end_date = date.today()
        start_date = end_date - timedelta(days=days - 1)

        return {
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "total_trades": 0,
            "total_winning_trades": 0,
            "total_losing_trades": 0,
            "overall_win_rate": 0.0,
            "total_pnl": 0.0,
            "average_daily_trades": 0.0,
            "average_daily_pnl": 0.0,
            "best_day": None,
            "worst_day": None,
            "daily_breakdown": [],
            "generated_at": datetime.utcnow().isoformat(),
        }

    def get_streak_analysis(self) -> dict[str, Any]:
        """Analyze winning and losing streaks."""
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=30)  # Last 30 days

            performance_data = db.get_performance_range(start_date, end_date)

            if not performance_data:
                return self._get_empty_streak_analysis()

            # Sort by date (oldest first)
            performance_data.sort(key=lambda x: x["date"])

            current_win_streak = 0
            current_loss_streak = 0
            max_win_streak = 0
            max_loss_streak = 0

            for day in performance_data:
                if day["total_pnl"] > 0:
                    current_win_streak += 1
                    current_loss_streak = 0
                    max_win_streak = max(max_win_streak, current_win_streak)
                elif day["total_pnl"] < 0:
                    current_loss_streak += 1
                    current_win_streak = 0
                    max_loss_streak = max(max_loss_streak, current_loss_streak)
                else:
                    current_win_streak = 0
                    current_loss_streak = 0

            return {
                "analysis_period_days": 30,
                "current_win_streak": current_win_streak,
                "current_loss_streak": current_loss_streak,
                "max_win_streak": max_win_streak,
                "max_loss_streak": max_loss_streak,
                "generated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error getting streak analysis: {e}")
            return self._get_empty_streak_analysis()

    def _get_empty_streak_analysis(self) -> dict[str, Any]:
        """Get empty streak analysis structure."""
        return {
            "analysis_period_days": 30,
            "current_win_streak": 0,
            "current_loss_streak": 0,
            "max_win_streak": 0,
            "max_loss_streak": 0,
            "generated_at": datetime.utcnow().isoformat(),
        }

    def export_performance_data(self, start_date: date, end_date: date) -> str:
        """Export performance data as JSON."""
        try:
            performance_data = db.get_performance_range(start_date, end_date)

            export_data = {
                "export_metadata": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "export_timestamp": datetime.utcnow().isoformat(),
                    "total_days": len(performance_data),
                },
                "performance_data": performance_data,
                "summary": self.get_performance_summary(
                    (end_date - start_date).days + 1
                ),
            }

            return json.dumps(export_data, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error exporting performance data: {e}")
            return json.dumps({"error": str(e)}, indent=2)


# Global performance tracker instance
performance_tracker = PerformanceTracker()
