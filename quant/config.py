"""
Configure Modules.

Author: <PERSON><PERSON><PERSON>tel
Date:   2020/12/29
"""

import json


class Config:
    """配置模块"""

    def __init__(self):
        self.log = {}  # 日志配置
        self.platforms = {}  # 交易所配置
        self.mongodb = {}  # Mongodb配置
        self.dingtalk = None  # 钉钉配置
        self.proxy = None  # HTTP代理配置
        self.heartbeat = {}  # 服务心跳配置

    def loads(self, config_file=None):
        """加载配置
        @param config_file json配置文件
        """
        configures = {}
        if config_file:
            try:
                with open(config_file) as f:
                    data = f.read()
                    configures = json.loads(data)
            except Exception as e:
                print(e)
                exit(0)
            if not configures:
                print("配置文件错误!")
                exit(0)
        self.update(configures)

    def update(self, update_fields):
        """更新配置
        @param update_fields 更新字段
        """
        self.log = update_fields.get("LOG", {})  # 日志配置
        self.mongodb = update_fields.get("MONGODB", None)  # mongodb配置
        self.platforms = update_fields.get("PLATFORMS", {})  # 交易所配置
        self.dingtalk = update_fields.get("DINGTALK", {})  # 钉钉配置
        self.proxy = update_fields.get("PROXY", None)  # HTTP代理配置
        self.heartbeat = update_fields.get("HEARTBEAT", {})  # 心跳配置

        for k, v in update_fields.items():
            setattr(self, k, v)


config = Config()
