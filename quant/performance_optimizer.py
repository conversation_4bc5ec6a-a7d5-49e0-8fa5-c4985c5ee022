"""
Performance Optimization Module

Handles caching mechanisms and performance optimization for the trading system.
"""

import json
import time
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Callable, Dict, Optional, Union
from dataclasses import dataclass, asdict

from quant.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class CacheConfig:
    """Cache configuration settings."""
    default_ttl: int = 300  # 5 minutes default TTL
    max_size: int = 1000  # Maximum cache entries
    cleanup_interval: int = 600  # Clean up expired entries every 10 minutes
    enable_stats: bool = True  # Track cache statistics


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    ttl: int
    access_count: int = 0
    last_accessed: datetime = None
    
    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.created_at
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    @property
    def age_seconds(self) -> int:
        """Get age of cache entry in seconds."""
        return int((datetime.now() - self.created_at).total_seconds())


class PerformanceCache:
    """High-performance caching system for trading data."""
    
    def __init__(self, config: CacheConfig = None):
        self.config = config or CacheConfig()
        self.cache: Dict[str, CacheEntry] = {}
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0,
            "hit_rate": 0.0,
            "current_size": 0
        }
        self.last_cleanup = time.time()
        
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        self.stats["total_requests"] += 1
        
        if key not in self.cache:
            self.stats["misses"] += 1
            self._update_hit_rate()
            return None
        
        entry = self.cache[key]
        
        # Check if expired
        if entry.is_expired:
            self._remove_key(key)
            self.stats["misses"] += 1
            self._update_hit_rate()
            return None
        
        # Update access metadata
        entry.access_count += 1
        entry.last_accessed = datetime.now()
        self.stats["hits"] += 1
        self._update_hit_rate()
        
        logger.debug(f"Cache hit for key: {key}")
        return entry.value
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Set value in cache with TTL."""
        try:
            # Check if we need to clean up
            self._cleanup_if_needed()
            
            # Check if we need to evict entries
            if len(self.cache) >= self.config.max_size:
                self._evict_entries()
            
            # Use provided TTL or default
            cache_ttl = ttl or self.config.default_ttl
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                ttl=cache_ttl
            )
            
            self.cache[key] = entry
            self.stats["current_size"] = len(self.cache)
            
            logger.debug(f"Cache set for key: {key}, TTL: {cache_ttl}s")
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache value for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if key in self.cache:
            del self.cache[key]
            self.stats["current_size"] = len(self.cache)
            logger.debug(f"Cache deleted for key: {key}")
            return True
        return False
    
    def clear(self):
        """Clear all cache entries."""
        self.cache.clear()
        self.stats["current_size"] = 0
        logger.info("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            **self.stats,
            "cache_entries": len(self.cache),
            "max_size": self.config.max_size,
            "default_ttl": self.config.default_ttl,
            "oldest_entry_age": self._get_oldest_entry_age(),
            "memory_usage_mb": self._estimate_memory_usage()
        }
    
    def _update_hit_rate(self):
        """Update hit rate statistic."""
        if self.stats["total_requests"] > 0:
            self.stats["hit_rate"] = self.stats["hits"] / self.stats["total_requests"]
    
    def _cleanup_if_needed(self):
        """Clean up expired entries if interval has passed."""
        current_time = time.time()
        if current_time - self.last_cleanup > self.config.cleanup_interval:
            self._cleanup_expired()
            self.last_cleanup = current_time
    
    def _cleanup_expired(self):
        """Remove all expired entries from cache."""
        expired_keys = [
            key for key, entry in self.cache.items() 
            if entry.is_expired
        ]
        
        for key in expired_keys:
            self._remove_key(key)
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _evict_entries(self):
        """Evict least recently used entries to make space."""
        if not self.cache:
            return
        
        # Sort by last access time and remove oldest entries
        sorted_entries = sorted(
            self.cache.values(), 
            key=lambda x: x.last_accessed
        )
        
        # Remove 10% of oldest entries
        entries_to_remove = max(1, len(sorted_entries) // 10)
        
        for entry in sorted_entries[:entries_to_remove]:
            self._remove_key(entry.key)
        
        self.stats["evictions"] += entries_to_remove
        logger.info(f"Evicted {entries_to_remove} cache entries")
    
    def _remove_key(self, key: str):
        """Remove key from cache and update stats."""
        if key in self.cache:
            del self.cache[key]
            self.stats["current_size"] = len(self.cache)
    
    def _get_oldest_entry_age(self) -> int:
        """Get age of oldest cache entry in seconds."""
        if not self.cache:
            return 0
        
        oldest_entry = min(
            self.cache.values(), 
            key=lambda x: x.created_at
        )
        return oldest_entry.age_seconds
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB."""
        try:
            total_size = sum(
                len(json.dumps(asdict(entry))) 
                for entry in self.cache.values()
            )
            return total_size / (1024 * 1024)  # Convert to MB
        except Exception:
            return 0.0


class PerformanceOptimizer:
    """Performance optimization utilities for the trading system."""
    
    def __init__(self, cache_config: CacheConfig = None):
        self.cache = PerformanceCache(cache_config)
        self.logger = logger
        
        # Cache keys for different data types
        self.cache_keys = {
            "market_data": "market_data_{symbol}_{interval}",
            "trade_history": "trade_history_{limit}_{offset}",
            "performance_stats": "performance_stats_{days}",
            "risk_metrics": "risk_metrics_{date}",
            "system_metrics": "system_metrics_{component}",
            "analytics": "analytics_{type}_{period}",
            "config": "config_{section}"
        }
    
    def cache_result(self, ttl: int = None, key_prefix: str = None):
        """Decorator to cache function results."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = self._generate_cache_key(
                    func.__name__, args, kwargs, key_prefix
                )
                
                # Try to get from cache
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.cache.set(cache_key, result, ttl)
                
                return result
            return wrapper
        return decorator
    
    def cache_market_data(self, symbol: str, interval: str, data: Dict[str, Any], ttl: int = 60):
        """Cache market data with short TTL."""
        cache_key = self.cache_keys["market_data"].format(
            symbol=symbol, interval=interval
        )
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_market_data(self, symbol: str, interval: str) -> Optional[Dict[str, Any]]:
        """Get cached market data."""
        cache_key = self.cache_keys["market_data"].format(
            symbol=symbol, interval=interval
        )
        return self.cache.get(cache_key)
    
    def cache_trade_history(self, limit: int, offset: int, data: list, ttl: int = 300):
        """Cache trade history results."""
        cache_key = self.cache_keys["trade_history"].format(
            limit=limit, offset=offset
        )
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_trade_history(self, limit: int, offset: int) -> Optional[list]:
        """Get cached trade history."""
        cache_key = self.cache_keys["trade_history"].format(
            limit=limit, offset=offset
        )
        return self.cache.get(cache_key)
    
    def cache_performance_stats(self, days: int, data: Dict[str, Any], ttl: int = 600):
        """Cache performance statistics."""
        cache_key = self.cache_keys["performance_stats"].format(days=days)
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_performance_stats(self, days: int) -> Optional[Dict[str, Any]]:
        """Get cached performance statistics."""
        cache_key = self.cache_keys["performance_stats"].format(days=days)
        return self.cache.get(cache_key)
    
    def cache_risk_metrics(self, date_str: str, data: Dict[str, Any], ttl: int = 300):
        """Cache risk metrics."""
        cache_key = self.cache_keys["risk_metrics"].format(date=date_str)
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_risk_metrics(self, date_str: str) -> Optional[Dict[str, Any]]:
        """Get cached risk metrics."""
        cache_key = self.cache_keys["risk_metrics"].format(date=date_str)
        return self.cache.get(cache_key)
    
    def cache_system_metrics(self, component: str, data: Dict[str, Any], ttl: int = 120):
        """Cache system metrics."""
        cache_key = self.cache_keys["system_metrics"].format(component=component)
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_system_metrics(self, component: str) -> Optional[Dict[str, Any]]:
        """Get cached system metrics."""
        cache_key = self.cache_keys["system_metrics"].format(component=component)
        return self.cache.get(cache_key)
    
    def cache_analytics(self, analytics_type: str, period: str, data: Dict[str, Any], ttl: int = 1800):
        """Cache analytics results."""
        cache_key = self.cache_keys["analytics"].format(
            type=analytics_type, period=period
        )
        return self.cache.set(cache_key, data, ttl)
    
    def get_cached_analytics(self, analytics_type: str, period: str) -> Optional[Dict[str, Any]]:
        """Get cached analytics."""
        cache_key = self.cache_keys["analytics"].format(
            type=analytics_type, period=period
        )
        return self.cache.get(cache_key)
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate all cache entries matching a pattern."""
        keys_to_remove = [
            key for key in self.cache.cache.keys() 
            if pattern in key
        ]
        
        for key in keys_to_remove:
            self.cache.delete(key)
        
        if keys_to_remove:
            logger.info(f"Invalidated {len(keys_to_remove)} cache entries matching pattern: {pattern}")
    
    def invalidate_all(self):
        """Invalidate all cache entries."""
        self.cache.clear()
        logger.info("All cache entries invalidated")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive cache performance report."""
        cache_stats = self.cache.get_stats()
        
        return {
            "cache_performance": cache_stats,
            "recommendations": self._generate_recommendations(cache_stats),
            "timestamp": datetime.now().isoformat()
        }
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict, prefix: str = None) -> str:
        """Generate unique cache key for function call."""
        # Create key components
        key_parts = []
        
        if prefix:
            key_parts.append(prefix)
        
        key_parts.append(func_name)
        
        # Add args to key
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
        
        # Add kwargs to key
        for key, value in sorted(kwargs.items()):
            if isinstance(value, (str, int, float, bool)):
                key_parts.append(f"{key}={value}")
        
        return "_".join(key_parts)
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> list[str]:
        """Generate performance recommendations based on cache stats."""
        recommendations = []
        
        # Hit rate recommendations
        if stats["hit_rate"] < 0.5:
            recommendations.append("Low cache hit rate - consider increasing TTL or cache size")
        elif stats["hit_rate"] > 0.9:
            recommendations.append("High cache hit rate - excellent caching efficiency")
        
        # Memory usage recommendations
        if stats["memory_usage_mb"] > 100:
            recommendations.append("High memory usage - consider reducing cache size or TTL")
        
        # Eviction recommendations
        if stats["evictions"] > 100:
            recommendations.append("Frequent evictions - consider increasing cache size")
        
        # Cache size recommendations
        if stats["current_size"] / stats["max_size"] > 0.8:
            recommendations.append("Cache nearly full - consider increasing max_size")
        
        return recommendations


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()