"""
Database Management Module

Handles all SQLite database operations for the trading system.
"""

import asyncio
import json
import time
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

from sqlalchemy import (
    Column,
    Date,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
    create_engine,
    text,
)
from sqlalchemy.exc import OperationalError, SQLAlchemyError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship, sessionmaker

from quant.recovery_manager import recovery_manager, RecoveryPriority
from quant.utils.logger import get_logger
from quant.performance_optimizer import performance_optimizer

Base = declarative_base()


class TradeHistory(Base):
    """Trade history model for storing trading signals and results."""

    __tablename__ = "trade_history"

    id = Column(Integer, primary_key=True)
    signal_timestamp = Column(DateTime, nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    direction = Column(String(10), nullable=False)  # 'LONG' or 'SHORT'
    entry_price = Column(Float, nullable=False)
    confidence_score = Column(Float, nullable=False)
    market_state = Column(String(50), nullable=False)
    trigger_pattern = Column(String(100), nullable=False)
    confirmed_indicators = Column(Text, nullable=False)  # JSON string
    suggested_bet = Column(Float, nullable=False)
    status = Column(String(20), nullable=False, index=True)  # 'PENDING', 'WIN', 'LOSS'
    exit_price = Column(Float, nullable=True)
    exit_timestamp = Column(DateTime, nullable=True)
    pnl = Column(Float, nullable=True)
    decision_details = Column(
        Text, nullable=False
    )  # JSON string with full decision context
    created_at = Column(DateTime, default=datetime.utcnow)

    # Confidence scoring breakdown columns
    confidence_breakdown = Column(
        Text, nullable=True
    )  # JSON string with detailed confidence breakdown
    indicator_scores = Column(
        Text, nullable=True
    )  # JSON string with individual indicator scores
    market_regime_score = Column(Float, nullable=True)  # Market regime confidence score
    trend_strength_score = Column(
        Float, nullable=True
    )  # Trend strength confidence score
    momentum_score = Column(Float, nullable=True)  # Momentum confidence score
    volatility_score = Column(Float, nullable=True)  # Volatility confidence score
    volume_score = Column(Float, nullable=True)  # Volume confidence score
    calculation_time_ms = Column(
        Integer, nullable=True
    )  # Time taken for confidence calculation
    signal_strength = Column(
        Integer, nullable=True
    )  # Signal strength (1=WEAK, 2=MEDIUM, 3=STRONG)

    # Rich text trade history fields
    decision_context = Column(
        Text, nullable=True
    )  # JSON string with comprehensive decision context
    entry_minutes = Column(
        Integer, nullable=True
    )  # Entry minutes for trading decisions
    confirmed_indicators_json = Column(
        Text, nullable=True
    )  # JSON string with confirmed indicators details

    # Composite index for performance
    __table_args__ = (
        Index("idx_status_timestamp", "status", "signal_timestamp"),
        Index("idx_confidence_performance", "confidence_score", "signal_strength"),
        Index(
            "idx_market_regime_performance",
            "market_regime_score",
            "trend_strength_score",
        ),
        Index("idx_symbol_direction_performance", "symbol", "direction", "status"),
        Index("idx_entry_performance", "entry_price", "pnl"),
        Index("idx_market_state_analysis", "market_state", "trigger_pattern"),
        Index("idx_created_at_analysis", "created_at"),
    )


class ConfidenceScoringHistory(Base):
    """Confidence scoring history model for tracking detailed confidence analysis."""

    __tablename__ = "confidence_scoring_history"

    id = Column(Integer, primary_key=True)
    trade_id = Column(Integer, ForeignKey("trade_history.id"), nullable=False)
    timestamp = Column(DateTime, nullable=False, index=True)
    overall_confidence = Column(Float, nullable=False)
    trend_score = Column(Float, nullable=False)
    momentum_score = Column(Float, nullable=False)
    volatility_score = Column(Float, nullable=False)
    volume_score = Column(Float, nullable=False)
    market_regime_score = Column(Float, nullable=False)
    indicator_weights = Column(Text, nullable=True)  # JSON string
    calculation_details = Column(Text, nullable=True)  # JSON string
    calculation_time_ms = Column(Integer, nullable=True)
    market_regime = Column(String(50), nullable=True)
    signal_strength = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship to trade history
    trade = relationship("TradeHistory", backref="confidence_history")

    # Indexes for performance
    __table_args__ = (
        Index("idx_confidence_history_trade_id", "trade_id"),
        Index("idx_confidence_history_timestamp", "timestamp"),
        Index("idx_confidence_history_overall", "overall_confidence"),
        Index("idx_confidence_history_regime", "market_regime"),
    )


class DailyPerformance(Base):
    """Daily performance statistics model."""

    __tablename__ = "daily_performance"

    id = Column(Integer, primary_key=True)
    date = Column(Date, unique=True, nullable=False, index=True)
    total_trades = Column(Integer, nullable=False, default=0)
    winning_trades = Column(Integer, nullable=False, default=0)
    losing_trades = Column(Integer, nullable=False, default=0)
    win_rate = Column(Float, nullable=False, default=0.0)
    total_pnl = Column(Float, nullable=False, default=0.0)
    generated_at = Column(DateTime, default=datetime.utcnow)

    # Indexes for performance
    __table_args__ = (
        Index("idx_daily_performance_date", "date"),
        Index("idx_daily_performance_win_rate", "win_rate"),
    )


class DatabaseManager:
    """Manages database connections and operations with failover capabilities."""

    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = Path(db_path)
        self.engine = create_engine(
            f"sqlite:///{self.db_path}", 
            echo=False,
            pool_size=10,
            max_overflow=20,
            pool_timeout=30,
            pool_recycle=3600
        )
        self.SessionLocal = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )
        self.logger = get_logger(__name__)
        
        # Failover and recovery attributes
        self.connection_retries = 0
        self.max_connection_retries = 3
        self.connection_health_check_interval = 300  # 5 minutes
        self.last_health_check = time.time()
        self._recovery_enabled = True

    def init_database(self):
        """Initialize database with all tables."""
        Base.metadata.create_all(bind=self.engine)

    @contextmanager
    def get_session(self) -> Session:
        """Get database session with automatic cleanup and failover."""
        session = None
        try:
            # Check if we need to perform health check
            if time.time() - self.last_health_check > self.connection_health_check_interval:
                self._check_database_health()
            
            session = self.SessionLocal()
            yield session
            session.commit()
        except OperationalError as e:
            self.logger.error(f"Database operational error: {e}")
            self.connection_retries += 1
            
            # Trigger recovery if enabled
            if self._recovery_enabled and self.connection_retries >= 2:
                asyncio.create_task(self._trigger_database_recovery("operational_error", str(e)))
            
            # Attempt reconnection
            if self.connection_retries <= self.max_connection_retries:
                try:
                    self.logger.info(f"Attempting database reconnection (attempt {self.connection_retries})")
                    self._recreate_engine()
                    session = self.SessionLocal()
                    yield session
                    session.commit()
                    self.connection_retries = 0  # Reset on success
                except Exception as retry_error:
                    self.logger.error(f"Database reconnection failed: {retry_error}")
                    raise retry_error
            else:
                raise e
        except SQLAlchemyError as e:
            self.logger.error(f"Database SQL error: {e}")
            raise
        except Exception as e:
            # 打印完整堆栈，便于定位 maximum recursion depth exceeded 等问题
            self.logger.error(f"Unexpected database error: {e}", exc_info=True)
            raise
        finally:
            if session:
                session.close()

    def save_trade_signal(self, signal_data: dict[str, Any]) -> int:
        """Save a new trade signal to database with duplicate prevention."""
        with self.get_session() as session:
            # Robust timestamp parsing: normalize to UTC naive everywhere
            try:
                from datetime import timezone as _tz
                ts_raw = signal_data["signal_timestamp"]
                if "T" in ts_raw:
                    ts = datetime.fromisoformat(ts_raw.replace('Z', '+00:00'))
                    # Convert any aware time to UTC, then drop tzinfo to store as naive UTC
                    if ts.tzinfo is not None:
                        ts = ts.astimezone(_tz.utc).replace(tzinfo=None)
                else:
                    # Treat as local-naive, convert to UTC-naive using local offset heuristic
                    ts_local = datetime.fromisoformat(ts_raw)
                    try:
                        local_offset = datetime.now() - datetime.utcnow()
                        ts = ts_local - local_offset
                    except Exception:
                        ts = ts_local
            except Exception:
                ts = datetime.utcnow()

            # 防重复检查：检查是否已存在相同特征的交易
            symbol = signal_data.get("symbol", "BTCUSDT")
            direction = signal_data["direction"]
            entry_price = signal_data["entry_price"]

            # 查找5秒内相同特征的交易
            from datetime import timedelta
            time_window_start = ts - timedelta(seconds=5)
            time_window_end = ts + timedelta(seconds=5)

            existing_trade = session.query(TradeHistory).filter(
                TradeHistory.symbol == symbol,
                TradeHistory.direction == direction,
                TradeHistory.entry_price == entry_price,
                TradeHistory.signal_timestamp >= time_window_start,
                TradeHistory.signal_timestamp <= time_window_end
            ).first()

            if existing_trade:
                self.logger.warning(
                    f"Duplicate trade detected! Existing trade ID: {existing_trade.id}, "
                    f"timestamp: {existing_trade.signal_timestamp}, returning existing ID"
                )
                return existing_trade.id

            trade = TradeHistory(
                signal_timestamp=ts,
                symbol=signal_data["symbol"],
                direction=signal_data["direction"],
                entry_price=signal_data["entry_price"],
                confidence_score=signal_data["confidence_score"],
                market_state=signal_data["market_state"],
                trigger_pattern=signal_data["trigger_pattern"],
                confirmed_indicators=json.dumps(signal_data["confirmed_indicators"]),
                suggested_bet=signal_data["suggested_bet"],
                status="PENDING",
                decision_details=json.dumps(signal_data["decision_details"]),
                # Confidence scoring breakdown data
                confidence_breakdown=json.dumps(
                    signal_data.get("confidence_breakdown", {})
                ),
                indicator_scores=json.dumps(signal_data.get("indicator_scores", {})),
                market_regime_score=signal_data.get("confidence_breakdown", {}).get(
                    "market_regime_score"
                ),
                trend_strength_score=signal_data.get("confidence_breakdown", {}).get(
                    "trend_score"
                ),
                momentum_score=signal_data.get("confidence_breakdown", {}).get(
                    "momentum_score"
                ),
                volatility_score=signal_data.get("confidence_breakdown", {}).get(
                    "volatility_score"
                ),
                volume_score=signal_data.get("confidence_breakdown", {}).get(
                    "volume_score"
                ),
                calculation_time_ms=signal_data.get("calculation_time_ms"),
                signal_strength=signal_data.get("signal_strength"),
                # Rich text trade history fields
                decision_context=json.dumps(signal_data.get("decision_context", {})),
                entry_minutes=signal_data.get("entry_minutes"),
                confirmed_indicators_json=json.dumps(
                    {
                        "indicators": signal_data.get("confirmed_indicators", []),
                        "details": signal_data.get("indicator_details", {}),
                    }
                ),
            )
            session.add(trade)
            session.flush()

            # Save confidence scoring history if available
            if "confidence_breakdown" in signal_data:
                self._save_confidence_scoring_history(session, trade.id, signal_data)

            return trade.id

    def update_trade_result(self, trade_id: int, result_data: dict[str, Any]) -> bool:
        """Update trade result after settlement with enhanced data consistency."""
        with self.get_session() as session:
            trade = (
                session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
            )
            if trade:
                # Data consistency validation - ensure settlement data matches original trade
                if not self._validate_settlement_consistency(trade, result_data):
                    self.logger.warning(f"Settlement data consistency check failed for trade {trade_id}")
                    # Log the inconsistency but continue with settlement
                
                # Update core settlement data
                trade.exit_price = result_data["exit_price"]
                trade.exit_timestamp = datetime.fromisoformat(
                    result_data["exit_timestamp"]
                )
                trade.pnl = result_data["pnl"]
                trade.status = result_data["status"]
                
                # Log settlement details for audit trail
                self.logger.info(f"Trade {trade_id} settled with status {result_data['status']}")
                self.logger.info(f"Settlement data consistency: {result_data.get('data_consistency_verified', False)}")
                
                return True
            return False
    
    def _validate_settlement_consistency(self, trade: TradeHistory, result_data: dict[str, Any]) -> bool:
        """Validate settlement data consistency with original trade data."""
        try:
            # Check if settlement direction matches original trade direction
            if trade.direction != result_data.get("direction"):
                self.logger.error(f"Direction mismatch for trade {trade.id}: Original {trade.direction}, Settlement {result_data.get('direction')}")
                return False
            
            # Check if settlement entry price matches original trade entry price
            if abs(trade.entry_price - result_data.get("entry_price", 0)) > 0.01:
                self.logger.error(f"Entry price mismatch for trade {trade.id}: Original {trade.entry_price}, Settlement {result_data.get('entry_price')}")
                return False
            
            # Check if settlement trade ID matches
            if trade.id != result_data.get("trade_id"):
                self.logger.error(f"Trade ID mismatch for trade {trade.id}: Original {trade.id}, Settlement {result_data.get('trade_id')}")
                return False
            
            # Validate settlement time calculation
            if "calculated_settlement_time" in result_data:
                expected_settlement_time = trade.signal_timestamp + timedelta(minutes=10)
                actual_settlement_time = datetime.fromisoformat(result_data["calculated_settlement_time"])
                
                if abs((expected_settlement_time - actual_settlement_time).total_seconds()) > 60:  # 1 minute tolerance
                    self.logger.warning(f"Settlement time calculation deviation for trade {trade.id}")
            
            self.logger.debug(f"Settlement data consistency validation passed for trade {trade.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating settlement consistency for trade {trade.id}: {e}")
            return False

    def get_pending_trades(self) -> list[dict[str, Any]]:
        """Get all pending trades for settlement with proper ordering."""
        with self.get_session() as session:
            trades = (
                session.query(TradeHistory)
                .filter(TradeHistory.status == "PENDING")
                .order_by(TradeHistory.signal_timestamp.asc())  # 按信号时间升序排列
                .all()
            )
            # Convert to dictionaries to avoid session binding issues
            # Normalize timestamps to UTC naive for consistency; also correct legacy local-naive rows
            def _to_utc_naive(dt: datetime) -> datetime:
                try:
                    if dt is None:
                        return dt
                    # If dt is in the future by a large margin relative to UTC now, assume it was saved as local-naive
                    now_utc = datetime.utcnow()
                    # Local offset approximation
                    local_offset = (datetime.now() - now_utc)
                    # Heuristic: if dt - now_utc > half of local offset, treat as local-naive and convert to UTC
                    if (dt - now_utc) > (local_offset / 2):
                        return dt - local_offset
                    return dt
                except Exception:
                    return dt

            result = []
            for trade in trades:
                signal_ts = _to_utc_naive(trade.signal_timestamp)
                exit_ts = _to_utc_naive(trade.exit_timestamp) if trade.exit_timestamp else None
                result.append(
                    {
                        "id": trade.id,
                        "signal_timestamp": signal_ts,
                        "symbol": trade.symbol,
                        "direction": trade.direction,
                        "entry_price": trade.entry_price,
                        "confidence_score": trade.confidence_score,
                        "market_state": trade.market_state,
                        "trigger_pattern": trade.trigger_pattern,
                        "confirmed_indicators": trade.confirmed_indicators,
                        "suggested_bet": trade.suggested_bet,
                        "status": trade.status,
                        "exit_price": trade.exit_price,
                        "exit_timestamp": exit_ts,
                        "pnl": trade.pnl,
                        "decision_details": trade.decision_details,
                    }
                )
            return result

    def get_trade_history(
        self, limit: int = 100, offset: int = 0
    ) -> list[TradeHistory]:
        """Get trade history with pagination."""
        with self.get_session() as session:
            return (
                session.query(TradeHistory)
                .order_by(TradeHistory.signal_timestamp.desc())
                .offset(offset)
                .limit(limit)
                .all()
            )

    def get_daily_stats(self, date: datetime) -> dict[str, Any]:
        """Get daily trading statistics."""
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        with self.get_session() as session:
            trades = (
                session.query(TradeHistory)
                .filter(
                    TradeHistory.signal_timestamp >= start_date,
                    TradeHistory.signal_timestamp <= end_date,
                )
                .all()
            )

            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.status == "WIN"])
            losing_trades = len([t for t in trades if t.status == "LOSS"])

            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            return {
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": round(win_rate, 2),
                "date": date.date().isoformat(),
            }

    def _save_confidence_scoring_history(
        self, session: Session, trade_id: int, signal_data: dict[str, Any]
    ):
        """Save confidence scoring history data."""
        try:
            confidence_breakdown = signal_data.get("confidence_breakdown", {})

            history = ConfidenceScoringHistory(
                trade_id=trade_id,
                timestamp=datetime.fromisoformat(signal_data["signal_timestamp"]),
                overall_confidence=signal_data["confidence_score"],
                trend_score=confidence_breakdown.get("trend_score", 0.0),
                momentum_score=confidence_breakdown.get("momentum_score", 0.0),
                volatility_score=confidence_breakdown.get("volatility_score", 0.0),
                volume_score=confidence_breakdown.get("volume_score", 0.0),
                market_regime_score=confidence_breakdown.get(
                    "market_regime_score", 0.0
                ),
                indicator_weights=json.dumps(
                    signal_data.get("decision_details", {})
                    .get("calculation_details", {})
                    .get("weights_used", {})
                ),
                calculation_details=json.dumps(
                    signal_data.get("decision_details", {}).get(
                        "calculation_details", {}
                    )
                ),
                calculation_time_ms=signal_data.get("calculation_time_ms"),
                market_regime=confidence_breakdown.get("market_regime"),
                signal_strength=signal_data.get("signal_strength"),
            )

            session.add(history)

        except Exception as e:
            self.logger.error(f"Error saving confidence scoring history: {e}")

    def get_confidence_performance_stats(self, days: int = 30) -> dict[str, Any]:
        """Get confidence scoring performance statistics."""
        try:
            start_date = datetime.now().replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            start_date = start_date - timedelta(days=days)

            with self.get_session() as session:
                # Get trades with confidence breakdown data
                trades = (
                    session.query(TradeHistory)
                    .filter(
                        TradeHistory.signal_timestamp >= start_date,
                        TradeHistory.confidence_score.isnot(None),
                        TradeHistory.trend_strength_score.isnot(None),
                    )
                    .all()
                )

                if not trades:
                    return {"message": "No confidence scoring data available"}

                # Calculate statistics
                total_trades = len(trades)
                winning_trades = [t for t in trades if t.status == "WIN"]
                losing_trades = [t for t in trades if t.status == "LOSS"]

                # Confidence level analysis
                high_confidence_trades = [
                    t for t in trades if t.confidence_score >= 0.8
                ]
                medium_confidence_trades = [
                    t for t in trades if 0.6 <= t.confidence_score < 0.8
                ]
                low_confidence_trades = [t for t in trades if t.confidence_score < 0.6]

                # Calculate win rates by confidence level
                high_confidence_wins = len(
                    [t for t in high_confidence_trades if t.status == "WIN"]
                )
                medium_confidence_wins = len(
                    [t for t in medium_confidence_trades if t.status == "WIN"]
                )
                low_confidence_wins = len(
                    [t for t in low_confidence_trades if t.status == "WIN"]
                )

                high_win_rate = (
                    (high_confidence_wins / len(high_confidence_trades) * 100)
                    if high_confidence_trades
                    else 0
                )
                medium_win_rate = (
                    (medium_confidence_wins / len(medium_confidence_trades) * 100)
                    if medium_confidence_trades
                    else 0
                )
                low_win_rate = (
                    (low_confidence_wins / len(low_confidence_trades) * 100)
                    if low_confidence_trades
                    else 0
                )

                # Market regime analysis
                bullish_trades = [
                    t
                    for t in trades
                    if t.market_regime_score and t.market_regime_score > 0.6
                ]
                bearish_trades = [
                    t
                    for t in trades
                    if t.market_regime_score and t.market_regime_score < 0.4
                ]

                bullish_wins = len([t for t in bullish_trades if t.status == "WIN"])
                bearish_wins = len([t for t in bearish_trades if t.status == "WIN"])

                bullish_win_rate = (
                    (bullish_wins / len(bullish_trades) * 100) if bullish_trades else 0
                )
                bearish_win_rate = (
                    (bearish_wins / len(bearish_trades) * 100) if bearish_trades else 0
                )

                # Average scores by outcome
                winning_avg_confidence = (
                    sum(t.confidence_score for t in winning_trades)
                    / len(winning_trades)
                    if winning_trades
                    else 0
                )
                losing_avg_confidence = (
                    sum(t.confidence_score for t in losing_trades) / len(losing_trades)
                    if losing_trades
                    else 0
                )

                return {
                    "period_days": days,
                    "total_trades": total_trades,
                    "winning_trades": len(winning_trades),
                    "losing_trades": len(losing_trades),
                    "overall_win_rate": round(
                        (
                            (len(winning_trades) / total_trades * 100)
                            if total_trades > 0
                            else 0
                        ),
                        2,
                    ),
                    "confidence_analysis": {
                        "high_confidence": {
                            "trades": len(high_confidence_trades),
                            "wins": high_confidence_wins,
                            "win_rate": round(high_win_rate, 2),
                        },
                        "medium_confidence": {
                            "trades": len(medium_confidence_trades),
                            "wins": medium_confidence_wins,
                            "win_rate": round(medium_win_rate, 2),
                        },
                        "low_confidence": {
                            "trades": len(low_confidence_trades),
                            "wins": low_confidence_wins,
                            "win_rate": round(low_win_rate, 2),
                        },
                    },
                    "market_regime_analysis": {
                        "bullish": {
                            "trades": len(bullish_trades),
                            "wins": bullish_wins,
                            "win_rate": round(bullish_win_rate, 2),
                        },
                        "bearish": {
                            "trades": len(bearish_trades),
                            "wins": bearish_wins,
                            "win_rate": round(bearish_win_rate, 2),
                        },
                    },
                    "performance_metrics": {
                        "winning_avg_confidence": round(winning_avg_confidence, 3),
                        "losing_avg_confidence": round(losing_avg_confidence, 3),
                        "confidence_edge": round(
                            winning_avg_confidence - losing_avg_confidence, 3
                        ),
                    },
                }

        except Exception as e:
            self.logger.error(f"Error getting confidence performance stats: {e}")
            return {"error": str(e)}

    def get_confidence_scoring_history(self, trade_id: int) -> list[dict[str, Any]]:
        """Get confidence scoring history for a specific trade."""
        try:
            with self.get_session() as session:
                history_records = (
                    session.query(ConfidenceScoringHistory)
                    .filter(ConfidenceScoringHistory.trade_id == trade_id)
                    .order_by(ConfidenceScoringHistory.timestamp.desc())
                    .all()
                )

                return [
                    {
                        "id": record.id,
                        "timestamp": record.timestamp.isoformat(),
                        "overall_confidence": record.overall_confidence,
                        "trend_score": record.trend_score,
                        "momentum_score": record.momentum_score,
                        "volatility_score": record.volatility_score,
                        "volume_score": record.volume_score,
                        "market_regime_score": record.market_regime_score,
                        "indicator_weights": (
                            json.loads(record.indicator_weights)
                            if record.indicator_weights
                            else {}
                        ),
                        "calculation_details": (
                            json.loads(record.calculation_details)
                            if record.calculation_details
                            else {}
                        ),
                        "calculation_time_ms": record.calculation_time_ms,
                        "market_regime": record.market_regime,
                        "signal_strength": record.signal_strength,
                    }
                    for record in history_records
                ]

        except Exception as e:
            self.logger.error(f"Error getting confidence scoring history: {e}")
            return []

    def save_daily_performance(
        self, date: datetime.date, performance_data: dict[str, Any]
    ) -> bool:
        """Save daily performance statistics."""
        try:
            with self.get_session() as session:
                # Check if record already exists for this date
                existing_record = (
                    session.query(DailyPerformance)
                    .filter(DailyPerformance.date == date)
                    .first()
                )

                if existing_record:
                    # Update existing record
                    existing_record.total_trades = performance_data.get(
                        "total_trades", 0
                    )
                    existing_record.winning_trades = performance_data.get(
                        "winning_trades", 0
                    )
                    existing_record.losing_trades = performance_data.get(
                        "losing_trades", 0
                    )
                    existing_record.win_rate = performance_data.get("win_rate", 0.0)
                    existing_record.total_pnl = performance_data.get("total_pnl", 0.0)
                    existing_record.generated_at = datetime.utcnow()
                else:
                    # Create new record
                    daily_perf = DailyPerformance(
                        date=date,
                        total_trades=performance_data.get("total_trades", 0),
                        winning_trades=performance_data.get("winning_trades", 0),
                        losing_trades=performance_data.get("losing_trades", 0),
                        win_rate=performance_data.get("win_rate", 0.0),
                        total_pnl=performance_data.get("total_pnl", 0.0),
                        generated_at=datetime.utcnow(),
                    )
                    session.add(daily_perf)

                return True

        except Exception as e:
            self.logger.error(f"Error saving daily performance: {e}")
            return False

    def get_daily_performance(self, date: datetime.date) -> dict[str, Any] | None:
        """Get daily performance statistics for a specific date."""
        try:
            with self.get_session() as session:
                record = (
                    session.query(DailyPerformance)
                    .filter(DailyPerformance.date == date)
                    .first()
                )

                if record:
                    return {
                        "date": record.date.isoformat(),
                        "total_trades": record.total_trades,
                        "winning_trades": record.winning_trades,
                        "losing_trades": record.losing_trades,
                        "win_rate": record.win_rate,
                        "total_pnl": record.total_pnl,
                        "generated_at": record.generated_at.isoformat(),
                    }
                return None

        except Exception as e:
            self.logger.error(f"Error getting daily performance: {e}")
            return None

    def get_performance_range(
        self, start_date: datetime.date, end_date: datetime.date
    ) -> list[dict[str, Any]]:
        """Get performance statistics for a date range."""
        try:
            with self.get_session() as session:
                records = (
                    session.query(DailyPerformance)
                    .filter(
                        DailyPerformance.date >= start_date,
                        DailyPerformance.date <= end_date,
                    )
                    .order_by(DailyPerformance.date.desc())
                    .all()
                )

                return [
                    {
                        "date": record.date.isoformat(),
                        "total_trades": record.total_trades,
                        "winning_trades": record.winning_trades,
                        "losing_trades": record.losing_trades,
                        "win_rate": record.win_rate,
                        "total_pnl": record.total_pnl,
                        "generated_at": record.generated_at.isoformat(),
                    }
                    for record in records
                ]

        except Exception as e:
            self.logger.error(f"Error getting performance range: {e}")
            return []

    def calculate_and_save_daily_performance(self, date: datetime.date) -> bool:
        """Calculate and save daily performance statistics for a specific date."""
        try:
            # Get daily stats from existing method
            start_datetime = datetime.combine(date, datetime.min.time())
            daily_stats = self.get_daily_stats(start_datetime)

            # Calculate total PnL for the day
            with self.get_session() as session:
                trades = (
                    session.query(TradeHistory)
                    .filter(
                        TradeHistory.signal_timestamp >= start_datetime,
                        TradeHistory.signal_timestamp
                        < start_datetime + timedelta(days=1),
                        TradeHistory.status.in_(["WIN", "LOSS"]),
                    )
                    .all()
                )

                total_pnl = sum(trade.pnl for trade in trades if trade.pnl is not None)

            # Prepare performance data
            performance_data = {
                "total_trades": daily_stats["total_trades"],
                "winning_trades": daily_stats["winning_trades"],
                "losing_trades": daily_stats["losing_trades"],
                "win_rate": daily_stats["win_rate"],
                "total_pnl": total_pnl,
            }

            # Save to database
            return self.save_daily_performance(date, performance_data)

        except Exception as e:
            self.logger.error(f"Error calculating daily performance: {e}")
            return False

    def _check_database_health(self):
        """Check database health and connectivity without recursive session usage."""
        # 防止递归调用导致的maximum recursion depth exceeded
        if getattr(self, "_health_check_in_progress", False):
            return
        self._health_check_in_progress = True
        try:
            # 直接使用engine连接，避免再次进入get_session()
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1")).scalar()
                if result == 1:
                    self.last_health_check = time.time()
                    self.connection_retries = 0  # Reset counter on successful check
                    self.logger.debug("Database health check passed")
                else:
                    raise Exception("Database health check returned unexpected result")
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}", exc_info=True)
            self.connection_retries += 1

            # Trigger recovery if enabled
            if self._recovery_enabled and self.connection_retries >= 2:
                asyncio.create_task(self._trigger_database_recovery("health_check_failed", str(e)))
        finally:
            self._health_check_in_progress = False

    def _recreate_engine(self):
        """Recreate database engine for reconnection."""
        try:
            # Dispose of existing engine
            self.engine.dispose()
            
            # Create new engine with connection pooling
            self.engine = create_engine(
                f"sqlite:///{self.db_path}", 
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600
            )
            
            # Update session maker
            self.SessionLocal = sessionmaker(
                autocommit=False, autoflush=False, bind=self.engine
            )
            
            self.logger.info("Database engine recreated successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to recreate database engine: {e}")
            raise

    async def _trigger_database_recovery(self, error_type: str, error_message: str):
        """Trigger database recovery operation."""
        try:
            await recovery_manager.trigger_recovery(
                component="database",
                operation_type=error_type,
                priority=RecoveryPriority.HIGH,
                data={
                    "error_type": error_type,
                    "error_message": error_message,
                    "retry_count": self.connection_retries,
                    "db_path": str(self.db_path)
                }
            )
        except Exception as e:
            self.logger.error(f"Failed to trigger database recovery: {e}")

    def get_database_status(self) -> dict[str, Any]:
        """Get current database status and health."""
        try:
            # Test connection
            start_time = time.time()
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
            response_time = (time.time() - start_time) * 1000
            
            pool = getattr(self.engine, "pool", None)
            try:
                pool_size = pool.size() if hasattr(pool, "size") else None
            except Exception:
                pool_size = None
            try:
                pool_overflow = pool.overflow() if hasattr(pool, "overflow") else None
            except Exception:
                pool_overflow = None
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "connection_retries": self.connection_retries,
                "pool_size": pool_size,
                "pool_overflow": pool_overflow,
                "recovery_enabled": self._recovery_enabled,
                "last_health_check": datetime.fromtimestamp(self.last_health_check).isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connection_retries": self.connection_retries,
                "recovery_enabled": self._recovery_enabled,
                "last_health_check": datetime.fromtimestamp(self.last_health_check).isoformat()
            }

    def get_optimized_trade_history(
        self, 
        limit: int = 100, 
        offset: int = 0,
        filters: dict[str, Any] = None,
        sort_by: str = "signal_timestamp",
        sort_desc: bool = True
    ) -> list[dict[str, Any]]:
        """Get optimized trade history with filtering and sorting."""
        try:
            with self.get_session() as session:
                # Build base query
                query = session.query(TradeHistory)
                
                # Apply filters if provided
                if filters:
                    if "status" in filters:
                        query = query.filter(TradeHistory.status == filters["status"])
                    if "symbol" in filters:
                        query = query.filter(TradeHistory.symbol == filters["symbol"])
                    if "direction" in filters:
                        query = query.filter(TradeHistory.direction == filters["direction"])
                    if "start_date" in filters:
                        query = query.filter(TradeHistory.signal_timestamp >= filters["start_date"])
                    if "end_date" in filters:
                        query = query.filter(TradeHistory.signal_timestamp <= filters["end_date"])
                    if "min_confidence" in filters:
                        query = query.filter(TradeHistory.confidence_score >= filters["min_confidence"])
                    if "max_confidence" in filters:
                        query = query.filter(TradeHistory.confidence_score <= filters["max_confidence"])
                
                # Apply sorting
                if sort_by == "signal_timestamp":
                    order_by = TradeHistory.signal_timestamp.desc() if sort_desc else TradeHistory.signal_timestamp.asc()
                elif sort_by == "confidence_score":
                    order_by = TradeHistory.confidence_score.desc() if sort_desc else TradeHistory.confidence_score.asc()
                elif sort_by == "pnl":
                    order_by = TradeHistory.pnl.desc() if sort_desc else TradeHistory.pnl.asc()
                elif sort_by == "entry_price":
                    order_by = TradeHistory.entry_price.desc() if sort_desc else TradeHistory.entry_price.asc()
                else:
                    order_by = TradeHistory.signal_timestamp.desc()
                
                query = query.order_by(order_by)
                
                # Apply pagination
                trades = query.offset(offset).limit(limit).all()
                
                # Convert to dictionaries with only necessary fields
                return [
                    {
                        "id": trade.id,
                        "signal_timestamp": trade.signal_timestamp.isoformat(),
                        "symbol": trade.symbol,
                        "direction": trade.direction,
                        "entry_price": trade.entry_price,
                        "confidence_score": trade.confidence_score,
                        "market_state": trade.market_state,
                        "suggested_bet": trade.suggested_bet,
                        "status": trade.status,
                        "exit_price": trade.exit_price,
                        "exit_timestamp": trade.exit_timestamp.isoformat() if trade.exit_timestamp else None,
                        "pnl": trade.pnl,
                        "signal_strength": trade.signal_strength,
                    }
                    for trade in trades
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting optimized trade history: {e}")
            return []

    def get_performance_analytics(self, days: int = 30) -> dict[str, Any]:
        """Get comprehensive performance analytics with optimized queries and caching."""
        try:
            # Check cache first
            cached_result = performance_optimizer.get_cached_performance_stats(days)
            if cached_result is not None:
                self.logger.debug(f"Using cached performance analytics for {days} days")
                return cached_result
            
            start_date = datetime.now() - timedelta(days=days)
            
            with self.get_session() as session:
                # Use optimized SQL for performance analytics
                analytics_query = text("""
                    SELECT 
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN status = 'WIN' THEN 1 ELSE 0 END) as winning_trades,
                        SUM(CASE WHEN status = 'LOSS' THEN 1 ELSE 0 END) as losing_trades,
                        SUM(CASE WHEN pnl IS NOT NULL THEN pnl ELSE 0 END) as total_pnl,
                        AVG(CASE WHEN status = 'WIN' THEN confidence_score ELSE NULL END) as avg_win_confidence,
                        AVG(CASE WHEN status = 'LOSS' THEN confidence_score ELSE NULL END) as avg_loss_confidence,
                        AVG(pnl) as avg_pnl,
                        MAX(pnl) as max_win,
                        MIN(pnl) as max_loss,
                        SUM(CASE WHEN pnl > 0 THEN pnl ELSE 0 END) as total_wins,
                        SUM(CASE WHEN pnl < 0 THEN ABS(pnl) ELSE 0 END) as total_losses
                    FROM trade_history 
                    WHERE signal_timestamp >= :start_date 
                    AND status IN ('WIN', 'LOSS')
                """)
                
                result = session.execute(analytics_query, {"start_date": start_date}).fetchone()
                
                if not result or result.total_trades == 0:
                    result_data = {"message": "No trade data available for analytics"}
                else:
                    # Calculate derived metrics
                    win_rate = (result.winning_trades / result.total_trades * 100) if result.total_trades > 0 else 0
                    profit_factor = (result.total_wins / result.total_losses) if result.total_losses > 0 else 0
                    avg_win = result.total_wins / result.winning_trades if result.winning_trades > 0 else 0
                    avg_loss = result.total_losses / result.losing_trades if result.losing_trades > 0 else 0
                    
                    result_data = {
                        "period_days": days,
                        "total_trades": result.total_trades,
                        "winning_trades": result.winning_trades,
                        "losing_trades": result.losing_trades,
                        "win_rate": round(win_rate, 2),
                        "total_pnl": round(result.total_pnl, 2),
                        "avg_pnl": round(result.avg_pnl, 2),
                        "max_win": round(result.max_win, 2),
                        "max_loss": round(result.max_loss, 2),
                        "avg_win": round(avg_win, 2),
                        "avg_loss": round(avg_loss, 2),
                        "profit_factor": round(profit_factor, 2),
                        "confidence_metrics": {
                            "avg_win_confidence": round(result.avg_win_confidence, 3) if result.avg_win_confidence else 0,
                            "avg_loss_confidence": round(result.avg_loss_confidence, 3) if result.avg_loss_confidence else 0,
                            "confidence_edge": round((result.avg_win_confidence or 0) - (result.avg_loss_confidence or 0), 3)
                        }
                    }
                
                # Cache the result
                performance_optimizer.cache_performance_stats(days, result_data, ttl=600)  # 10 minutes
                
                return result_data
                
        except Exception as e:
            self.logger.error(f"Error getting performance analytics: {e}")
            return {"error": str(e)}

    def get_market_regime_analysis(self, days: int = 30) -> dict[str, Any]:
        """Get market regime analysis with optimized queries and caching."""
        try:
            # Check cache first
            cached_result = performance_optimizer.get_cached_analytics("market_regime", f"{days}d")
            if cached_result is not None:
                self.logger.debug(f"Using cached market regime analysis for {days} days")
                return cached_result
            
            start_date = datetime.now() - timedelta(days=days)
            
            with self.get_session() as session:
                # Use optimized SQL for market regime analysis
                regime_query = text("""
                    SELECT 
                        CASE 
                            WHEN market_regime_score > 0.6 THEN 'bullish'
                            WHEN market_regime_score < 0.4 THEN 'bearish'
                            ELSE 'neutral'
                        END as regime,
                        COUNT(*) as trades,
                        SUM(CASE WHEN status = 'WIN' THEN 1 ELSE 0 END) as wins,
                        SUM(CASE WHEN status = 'LOSS' THEN 1 ELSE 0 END) as losses,
                        AVG(confidence_score) as avg_confidence,
                        SUM(CASE WHEN pnl IS NOT NULL THEN pnl ELSE 0 END) as total_pnl
                    FROM trade_history 
                    WHERE signal_timestamp >= :start_date 
                    AND market_regime_score IS NOT NULL
                    AND status IN ('WIN', 'LOSS')
                    GROUP BY regime
                    ORDER BY trades DESC
                """)
                
                results = session.execute(regime_query, {"start_date": start_date}).fetchall()
                
                regime_analysis = {}
                for row in results:
                    win_rate = (row.wins / row.trades * 100) if row.trades > 0 else 0
                    regime_analysis[row.regime] = {
                        "trades": row.trades,
                        "wins": row.wins,
                        "losses": row.losses,
                        "win_rate": round(win_rate, 2),
                        "avg_confidence": round(row.avg_confidence, 3) if row.avg_confidence else 0,
                        "total_pnl": round(row.total_pnl, 2)
                    }
                
                result_data = {
                    "period_days": days,
                    "regime_analysis": regime_analysis,
                    "best_regime": max(regime_analysis.keys(), key=lambda x: regime_analysis[x].get("win_rate", 0)) if regime_analysis else None
                }
                
                # Cache the result
                performance_optimizer.cache_analytics("market_regime", f"{days}d", result_data, ttl=1800)  # 30 minutes
                
                return result_data
                
        except Exception as e:
            self.logger.error(f"Error getting market regime analysis: {e}")
            return {"error": str(e)}

    # ===== Auto trading support helpers =====
    def get_trade_by_signal_timestamp(self, symbol: str, signal_timestamp: str) -> dict[str, Any] | None:
        """Fetch a trade by exact signal timestamp and symbol.

        Returns minimal dict with key fields or None if not found.
        """
        try:
            # Normalize timestamp to UTC naive for equality match
            from datetime import timezone as _tz
            ts = datetime.fromisoformat(signal_timestamp.replace('Z', '+00:00')) if 'T' in signal_timestamp else datetime.fromisoformat(signal_timestamp)
            if ts.tzinfo is not None:
                ts = ts.astimezone(_tz.utc).replace(tzinfo=None)

            with self.get_session() as session:
                trade = (
                    session.query(TradeHistory)
                    .filter(
                        TradeHistory.symbol == symbol,
                        TradeHistory.signal_timestamp == ts,
                    )
                    .first()
                )

                if not trade:
                    return None

                return {
                    "id": trade.id,
                    "signal_timestamp": trade.signal_timestamp.isoformat(),
                    "symbol": trade.symbol,
                    "direction": trade.direction,
                    "entry_price": trade.entry_price,
                    "suggested_bet": trade.suggested_bet,
                    "status": trade.status,
                    "decision_details": trade.decision_details,
                }
        except Exception as e:
            self.logger.error(f"Error fetching trade by signal timestamp: {e}")
            return None

    def update_trade_decision_details(self, trade_id: int, updates: dict[str, Any]) -> bool:
        """Merge provided updates into the existing decision_details JSON for a trade."""
        try:
            with self.get_session() as session:
                trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
                if not trade:
                    return False

                try:
                    current = json.loads(trade.decision_details) if trade.decision_details else {}
                except Exception:
                    current = {}

                # Shallow merge; nested callers should prepare nested dicts
                current.update(updates or {})
                trade.decision_details = json.dumps(current)
                return True
        except Exception as e:
            self.logger.error(f"Error updating trade decision details: {e}")
            return False

    def cleanup_old_data(self, days_to_keep: int = 90) -> dict[str, Any]:
        """Clean up old trade data to maintain performance."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.get_session() as session:
                # Count records to be deleted
                count_query = text("""
                    SELECT COUNT(*) as count FROM trade_history 
                    WHERE signal_timestamp < :cutoff_date
                """)
                count_result = session.execute(count_query, {"cutoff_date": cutoff_date}).fetchone()
                
                if count_result.count == 0:
                    return {"message": "No old data to clean up", "records_deleted": 0}
                
                # Delete old records
                delete_query = text("""
                    DELETE FROM trade_history 
                    WHERE signal_timestamp < :cutoff_date
                """)
                session.execute(delete_query, {"cutoff_date": cutoff_date})
                
                # Also clean up old confidence history
                delete_confidence_query = text("""
                    DELETE FROM confidence_scoring_history 
                    WHERE timestamp < :cutoff_date
                """)
                session.execute(delete_confidence_query, {"cutoff_date": cutoff_date})
                
                session.commit()
                
                self.logger.info(f"Cleaned up {count_result.count} old trade records")
                
                return {
                    "records_deleted": count_result.count,
                    "cutoff_date": cutoff_date.isoformat(),
                    "days_kept": days_to_keep
                }
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
            return {"error": str(e)}

    def optimize_database(self) -> dict[str, Any]:
        """Run database optimization operations."""
        try:
            with self.get_session() as session:
                # Run SQLite optimization commands
                optimize_commands = [
                    "PRAGMA optimize",
                    "PRAGMA vacuum",
                    "PRAGMA analysis_limit=400",
                    "PRAGMA optimize"
                ]
                
                results = {}
                for command in optimize_commands:
                    start_time = time.time()
                    session.execute(text(command))
                    execution_time = time.time() - start_time
                    results[command] = round(execution_time, 3)
                
                session.commit()
                
                self.logger.info(f"Database optimization completed: {results}")
                
                return {
                    "status": "completed",
                    "optimization_results": results,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error optimizing database: {e}")
            return {"error": str(e)}

    def get_query_performance_stats(self) -> dict[str, Any]:
        """Get database query performance statistics."""
        try:
            with self.get_session() as session:
                # Get SQLite performance stats
                stats_query = text("""
                    SELECT 
                        name,
                        value
                    FROM pragma_database_list
                    UNION ALL
                    SELECT 
                        'cache_size' as name,
                        CAST(value AS INTEGER) as value
                    FROM pragma_cache_size
                    UNION ALL
                    SELECT 
                        'page_size' as name,
                        CAST(value AS INTEGER) as value
                    FROM pragma_page_size
                    UNION ALL
                    SELECT 
                        'mmap_size' as name,
                        CAST(value AS INTEGER) as value
                    FROM pragma_mmap_size
                """)
                
                results = session.execute(stats_query).fetchall()
                
                stats = {row.name: row.value for row in results}
                
                # Test query performance
                test_queries = [
                    "SELECT COUNT(*) FROM trade_history",
                    "SELECT COUNT(*) FROM trade_history WHERE status = 'WIN'",
                    "SELECT AVG(confidence_score) FROM trade_history WHERE status = 'WIN'",
                    "SELECT COUNT(*) FROM confidence_scoring_history"
                ]
                
                query_performance = {}
                for query in test_queries:
                    start_time = time.time()
                    session.execute(text(query)).fetchone()
                    execution_time = (time.time() - start_time) * 1000
                    query_performance[query] = round(execution_time, 2)
                
                return {
                    "database_stats": stats,
                    "query_performance_ms": query_performance,
                    "connection_pool": {
                        "size": self.engine.pool.size(),
                        "overflow": self.engine._pool._overflow,
                        "timeout": self.engine.pool.timeout()
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Error getting query performance stats: {e}")
            return {"error": str(e)}


# Global database instance
db = DatabaseManager()
