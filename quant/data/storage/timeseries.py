"""
Time Series Storage
"""

import os
import sqlite3
from abc import ABC, abstractmethod
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import pandas as pd


class BaseStorage(ABC):
    """存储基类"""

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.storage_path = config.get("storage_path", "./data") if config else "./data"
        self.is_connected = False

    @abstractmethod
    def connect(self):
        """连接存储"""
        pass

    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass

    @abstractmethod
    def save(self, data: pd.DataFrame, table_name: str, **kwargs):
        """保存数据"""
        pass

    @abstractmethod
    def load(self, table_name: str, **kwargs) -> pd.DataFrame | None:
        """加载数据"""
        pass


class TimeSeriesStorage(BaseStorage):
    """时间序列存储"""

    def __init__(self, name: str = "TimeSeriesStorage", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.db_path = os.path.join(self.storage_path, "timeseries.db")
        self.connection = None
        self.compression = config.get("compression", True) if config else True
        self.chunk_size = config.get("chunk_size", 10000) if config else 10000

    def connect(self):
        """连接数据库"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            self.connection = sqlite3.connect(self.db_path)
            self.connection.execute("PRAGMA journal_mode=WAL")
            self.connection.execute("PRAGMA synchronous=NORMAL")
            self.connection.execute("PRAGMA cache_size=10000")

            self.is_connected = True

        except Exception as e:
            print(f"Error connecting to database: {e}")
            self.is_connected = False

    def disconnect(self):
        """断开连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
        self.is_connected = False

    def save(self, data: pd.DataFrame, table_name: str, **kwargs):
        """
        保存时间序列数据

        Args:
            data: 时间序列数据
            table_name: 表名
            **kwargs: 其他参数
        """
        if not self.is_connected:
            self.connect()

        if data.empty:
            return

        try:
            # 确保索引是时间戳
            if not isinstance(data.index, pd.DatetimeIndex):
                raise ValueError("Data must have a datetime index")

            # 添加时间戳列
            data_copy = data.copy()
            data_copy["timestamp"] = data_copy.index

            # 分块保存大数据
            if len(data_copy) > self.chunk_size:
                self._save_chunked(data_copy, table_name, **kwargs)
            else:
                self._save_single(data_copy, table_name, **kwargs)

        except Exception as e:
            print(f"Error saving data to {table_name}: {e}")

    def _save_single(self, data: pd.DataFrame, table_name: str, **kwargs):
        """保存单块数据"""
        # 创建表（如果不存在）
        self._create_table_if_not_exists(data, table_name)

        # 保存数据
        data.to_sql(
            table_name,
            self.connection,
            if_exists="append",
            index=False,
            chunksize=self.chunk_size,
        )

        # 提交事务
        self.connection.commit()

    def _save_chunked(self, data: pd.DataFrame, table_name: str, **kwargs):
        """分块保存大数据"""
        # 创建表（如果不存在）
        self._create_table_if_not_exists(data, table_name)

        # 分块保存
        for i in range(0, len(data), self.chunk_size):
            chunk = data.iloc[i : i + self.chunk_size]

            chunk.to_sql(
                table_name,
                self.connection,
                if_exists="append",
                index=False,
                chunksize=self.chunk_size,
            )

            # 定期提交
            if i % (self.chunk_size * 10) == 0:
                self.connection.commit()

        # 最终提交
        self.connection.commit()

    def _create_table_if_not_exists(self, data: pd.DataFrame, table_name: str):
        """创建表（如果不存在）"""
        cursor = self.connection.cursor()

        # 检查表是否存在
        cursor.execute(
            f"""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='{table_name}'
        """
        )

        if cursor.fetchone() is None:
            # 创建表
            columns = []
            for col in data.columns:
                if col == "timestamp":
                    continue  # Skip timestamp column as it will be added separately
                
                dtype = data[col].dtype

                if dtype == "object":
                    sql_type = "TEXT"
                elif dtype in ["int64", "int32"]:
                    sql_type = "INTEGER"
                elif dtype in ["float64", "float32"]:
                    sql_type = "REAL"
                else:
                    sql_type = "TEXT"

                columns.append(f'"{col}" {sql_type}')

            # 添加时间戳列（确保只添加一次）
            columns.append("timestamp DATETIME")

            create_sql = f"""
                CREATE TABLE {table_name} (
                    {', '.join(columns)}
                )
            """

            cursor.execute(create_sql)

            # 创建索引
            cursor.execute(
                f"""
                CREATE INDEX idx_{table_name}_timestamp 
                ON {table_name}(timestamp)
            """
            )

            self.connection.commit()

    def load(self, table_name: str, **kwargs) -> pd.DataFrame | None:
        """
        加载时间序列数据

        Args:
            table_name: 表名
            **kwargs: 查询参数
                - start_time: 开始时间
                - end_time: 结束时间
                - columns: 指定列
                - limit: 限制条数

        Returns:
            时间序列数据
        """
        if not self.is_connected:
            self.connect()

        try:
            # 构建查询
            query = f"SELECT * FROM {table_name}"

            # 添加条件
            conditions = []
            params = []

            start_time = kwargs.get("start_time")
            end_time = kwargs.get("end_time")
            columns = kwargs.get("columns")
            limit = kwargs.get("limit")

            if start_time:
                conditions.append("timestamp >= ?")
                if isinstance(start_time, str):
                    params.append(pd.to_datetime(start_time))
                else:
                    params.append(start_time)

            if end_time:
                conditions.append("timestamp <= ?")
                if isinstance(end_time, str):
                    params.append(pd.to_datetime(end_time))
                else:
                    params.append(end_time)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            # 添加排序
            query += " ORDER BY timestamp"

            # 添加限制
            if limit:
                query += f" LIMIT {limit}"

            # 执行查询
            df = pd.read_sql_query(query, self.connection, params=params)

            if not df.empty:
                # 设置时间索引
                df["timestamp"] = pd.to_datetime(df["timestamp"])
                df.set_index("timestamp", inplace=True)

                # 选择指定列
                if columns:
                    available_columns = [col for col in columns if col in df.columns]
                    df = df[available_columns]

            return df

        except Exception as e:
            print(f"Error loading data from {table_name}: {e}")
            return None

    def get_table_info(self, table_name: str) -> dict[str, Any]:
        """获取表信息"""
        if not self.is_connected:
            self.connect()

        try:
            cursor = self.connection.cursor()

            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()

            # 获取数据量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]

            # 获取时间范围
            cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
            time_range = cursor.fetchone()

            return {
                "table_name": table_name,
                "columns": columns_info,
                "row_count": row_count,
                "time_range": {"start": time_range[0], "end": time_range[1]},
                "size_mb": self._get_table_size(table_name),
            }

        except Exception as e:
            print(f"Error getting table info for {table_name}: {e}")
            return {}

    def _get_table_size(self, table_name: str) -> float:
        """获取表大小（MB）"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                f"SELECT COUNT(*) * AVG(length(*)) / 1024.0 / 1024.0 FROM {table_name}"
            )
            return cursor.fetchone()[0]
        except:
            return 0.0

    def list_tables(self) -> list[str]:
        """列出所有表"""
        if not self.is_connected:
            self.connect()

        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            return tables
        except Exception as e:
            print(f"Error listing tables: {e}")
            return []

    def delete_table(self, table_name: str):
        """删除表"""
        if not self.is_connected:
            self.connect()

        try:
            cursor = self.connection.cursor()
            cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            self.connection.commit()
        except Exception as e:
            print(f"Error deleting table {table_name}: {e}")

    def cleanup_old_data(self, table_name: str, days_to_keep: int = 30):
        """清理旧数据"""
        if not self.is_connected:
            self.connect()

        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            cursor = self.connection.cursor()
            cursor.execute(
                f"DELETE FROM {table_name} WHERE timestamp < ?", (cutoff_date,)
            )
            self.connection.commit()

            print(f"Cleaned up data older than {days_to_keep} days from {table_name}")

        except Exception as e:
            print(f"Error cleaning up old data from {table_name}: {e}")

    def backup_database(self, backup_path: str):
        """备份数据库"""
        if not self.is_connected:
            self.connect()

        try:
            # 确保备份目录存在
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)

            # 创建备份
            backup_connection = sqlite3.connect(backup_path)
            self.connection.backup(backup_connection)
            backup_connection.close()

            print(f"Database backed up to {backup_path}")

        except Exception as e:
            print(f"Error backing up database: {e}")

    def get_storage_stats(self) -> dict[str, Any]:
        """获取存储统计"""
        if not self.is_connected:
            self.connect()

        try:
            cursor = self.connection.cursor()

            # 获取数据库文件大小
            db_size = os.path.getsize(self.db_path) / 1024.0 / 1024.0  # MB

            # 获取表数量
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]

            # 获取总行数
            total_rows = 0
            tables = self.list_tables()
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                total_rows += cursor.fetchone()[0]

            return {
                "database_path": self.db_path,
                "database_size_mb": db_size,
                "table_count": table_count,
                "total_rows": total_rows,
                "tables": tables,
            }

        except Exception as e:
            print(f"Error getting storage stats: {e}")
            return {}
