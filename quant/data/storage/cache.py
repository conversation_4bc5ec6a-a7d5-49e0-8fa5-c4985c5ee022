"""
Cache Manager
"""

import hashlib
import os
import pickle
import threading
import time
from abc import ABC, abstractmethod
from typing import Any

import pandas as pd


class BaseCache(ABC):
    """缓存基类"""

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.cache_size = config.get("cache_size", 1000) if config else 1000
        self.ttl = config.get("ttl", 3600) if config else 3600  # 默认1小时
        self.stats = {"hits": 0, "misses": 0, "evictions": 0, "size": 0}

    @abstractmethod
    def get(self, key: str) -> Any | None:
        """获取缓存值"""
        pass

    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        pass

    @abstractmethod
    def delete(self, key: str):
        """删除缓存值"""
        pass

    @abstractmethod
    def clear(self):
        """清空缓存"""
        pass

    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        return self.stats["hits"] / total_requests if total_requests > 0 else 0.0

    def get_stats(self) -> dict[str, Any]:
        """获取缓存统计"""
        return {
            **self.stats,
            "hit_rate": self.get_hit_rate(),
            "cache_size": self.cache_size,
            "ttl": self.ttl,
        }


class MemoryCache(BaseCache):
    """内存缓存"""

    def __init__(self, name: str = "MemoryCache", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.cache = {}
        self.access_times = {}
        self.lock = threading.Lock()

    def get(self, key: str) -> Any | None:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]

                # 检查是否过期
                if self._is_expired(entry):
                    self._remove_key(key)
                    self.stats["misses"] += 1
                    return None

                # 更新访问时间
                self.access_times[key] = time.time()
                self.stats["hits"] += 1
                return entry["value"]

            self.stats["misses"] += 1
            return None

    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        with self.lock:
            # 如果缓存已满，执行清理
            if len(self.cache) >= self.cache_size:
                self._evict()

            # 设置缓存值
            entry_ttl = ttl if ttl is not None else self.ttl
            self.cache[key] = {
                "value": value,
                "created_at": time.time(),
                "ttl": entry_ttl,
            }
            self.access_times[key] = time.time()
            self.stats["size"] = len(self.cache)

    def delete(self, key: str):
        """删除缓存值"""
        with self.lock:
            self._remove_key(key)

    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.stats["size"] = 0

    def _is_expired(self, entry: dict[str, Any]) -> bool:
        """检查是否过期"""
        return time.time() - entry["created_at"] > entry["ttl"]

    def _remove_key(self, key: str):
        """移除键"""
        if key in self.cache:
            del self.cache[key]
        if key in self.access_times:
            del self.access_times[key]
        self.stats["size"] = len(self.cache)

    def _evict(self):
        """淘汰缓存"""
        if not self.cache:
            return

        # LRU淘汰策略
        oldest_key = min(self.access_times, key=self.access_times.get)
        self._remove_key(oldest_key)
        self.stats["evictions"] += 1


class FileCache(BaseCache):
    """文件缓存"""

    def __init__(self, name: str = "FileCache", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.cache_dir = config.get("cache_dir", "./cache") if config else "./cache"
        self._ensure_cache_dir()

    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        os.makedirs(self.cache_dir, exist_ok=True)

    def _get_cache_file_path(self, key: str) -> str:
        """获取缓存文件路径"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{key_hash}.cache")

    def get(self, key: str) -> Any | None:
        """获取缓存值"""
        file_path = self._get_cache_file_path(key)

        if not os.path.exists(file_path):
            self.stats["misses"] += 1
            return None

        try:
            with open(file_path, "rb") as f:
                entry = pickle.load(f)

            # 检查是否过期
            if self._is_expired(entry):
                os.remove(file_path)
                self.stats["misses"] += 1
                return None

            self.stats["hits"] += 1
            return entry["value"]

        except Exception as e:
            print(f"Error reading cache file {file_path}: {e}")
            self.stats["misses"] += 1
            return None

    def set(self, key: str, value: Any, ttl: int = None):
        """设置缓存值"""
        file_path = self._get_cache_file_path(key)

        # 如果缓存已满，执行清理
        if self._get_cache_count() >= self.cache_size:
            self._evict()

        try:
            entry = {
                "value": value,
                "created_at": time.time(),
                "ttl": ttl if ttl is not None else self.ttl,
            }

            with open(file_path, "wb") as f:
                pickle.dump(entry, f)

            self.stats["size"] = self._get_cache_count()

        except Exception as e:
            print(f"Error writing cache file {file_path}: {e}")

    def delete(self, key: str):
        """删除缓存值"""
        file_path = self._get_cache_file_path(key)

        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                self.stats["size"] = self._get_cache_count()
            except Exception as e:
                print(f"Error deleting cache file {file_path}: {e}")

    def clear(self):
        """清空缓存"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith(".cache"):
                    file_path = os.path.join(self.cache_dir, filename)
                    os.remove(file_path)

            self.stats["size"] = 0

        except Exception as e:
            print(f"Error clearing cache: {e}")

    def _is_expired(self, entry: dict[str, Any]) -> bool:
        """检查是否过期"""
        return time.time() - entry["created_at"] > entry["ttl"]

    def _get_cache_count(self) -> int:
        """获取缓存文件数量"""
        try:
            return len([f for f in os.listdir(self.cache_dir) if f.endswith(".cache")])
        except:
            return 0

    def _evict(self):
        """淘汰缓存"""
        cache_files = []

        for filename in os.listdir(self.cache_dir):
            if filename.endswith(".cache"):
                file_path = os.path.join(self.cache_dir, filename)
                cache_files.append(
                    {"path": file_path, "mtime": os.path.getmtime(file_path)}
                )

        if cache_files:
            # 按修改时间排序，删除最旧的
            cache_files.sort(key=lambda x: x["mtime"])
            oldest_file = cache_files[0]

            try:
                os.remove(oldest_file["path"])
                self.stats["evictions"] += 1
            except Exception as e:
                print(f"Error evicting cache file {oldest_file['path']}: {e}")


class CacheManager:
    """缓存管理器"""

    def __init__(self, config: dict[str, Any] = None):
        self.config = config or {}
        self.caches = {}
        self.default_cache_type = (
            config.get("default_cache_type", "memory") if config else "memory"
        )

        # 初始化默认缓存
        self._init_default_caches()

    def _init_default_caches(self):
        """初始化默认缓存"""
        # 内存缓存
        memory_config = self.config.get("memory_cache", {})
        self.caches["memory"] = MemoryCache("MemoryCache", memory_config)

        # 文件缓存
        file_config = self.config.get("file_cache", {})
        self.caches["file"] = FileCache("FileCache", file_config)

    def get_cache(self, cache_type: str = None) -> BaseCache:
        """获取缓存实例"""
        if cache_type is None:
            cache_type = self.default_cache_type

        if cache_type not in self.caches:
            raise ValueError(f"Cache type '{cache_type}' not found")

        return self.caches[cache_type]

    def get(self, key: str, cache_type: str = None) -> Any | None:
        """获取缓存值"""
        cache = self.get_cache(cache_type)
        return cache.get(key)

    def set(self, key: str, value: Any, cache_type: str = None, ttl: int = None):
        """设置缓存值"""
        cache = self.get_cache(cache_type)
        cache.set(key, value, ttl)

    def delete(self, key: str, cache_type: str = None):
        """删除缓存值"""
        cache = self.get_cache(cache_type)
        cache.delete(key)

    def clear(self, cache_type: str = None):
        """清空缓存"""
        if cache_type is None:
            # 清空所有缓存
            for cache in self.caches.values():
                cache.clear()
        else:
            cache = self.get_cache(cache_type)
            cache.clear()

    def get_stats(self, cache_type: str = None) -> dict[str, Any]:
        """获取缓存统计"""
        if cache_type is None:
            # 返回所有缓存统计
            stats = {}
            for name, cache in self.caches.items():
                stats[name] = cache.get_stats()
            return stats
        else:
            cache = self.get_cache(cache_type)
            return cache.get_stats()

    def cache_dataframe(
        self, df: pd.DataFrame, key: str, cache_type: str = None, ttl: int = None
    ):
        """缓存DataFrame"""
        cache = self.get_cache(cache_type)

        # 转换为字典格式存储
        cache_data = {
            "data": df.to_dict("records"),
            "index": df.index.tolist(),
            "columns": df.columns.tolist(),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
        }

        cache.set(key, cache_data, ttl)

    def get_cached_dataframe(
        self, key: str, cache_type: str = None
    ) -> pd.DataFrame | None:
        """获取缓存的DataFrame"""
        cache = self.get_cache(cache_type)
        cache_data = cache.get(key)

        if cache_data is None:
            return None

        try:
            # 重建DataFrame
            df = pd.DataFrame(cache_data["data"])
            df.index = cache_data["index"]
            df.columns = cache_data["columns"]

            # 转换数据类型
            for col, dtype_str in cache_data["dtypes"].items():
                if col in df.columns:
                    if "int" in dtype_str:
                        df[col] = pd.to_numeric(df[col], downcast="integer")
                    elif "float" in dtype_str:
                        df[col] = pd.to_numeric(df[col], downcast="float")

            return df

        except Exception as e:
            print(f"Error reconstructing cached DataFrame: {e}")
            return None

    def cleanup_expired(self, cache_type: str = None):
        """清理过期缓存"""
        if cache_type is None:
            # 清理所有缓存
            for cache in self.caches.values():
                if hasattr(cache, "cleanup_expired"):
                    cache.cleanup_expired()
        else:
            cache = self.get_cache(cache_type)
            if hasattr(cache, "cleanup_expired"):
                cache.cleanup_expired()

    def get_memory_usage(self) -> dict[str, Any]:
        """获取内存使用情况"""
        usage = {}

        for name, cache in self.caches.items():
            if isinstance(cache, MemoryCache):
                usage[name] = {
                    "size": len(cache.cache),
                    "max_size": cache.cache_size,
                    "hit_rate": cache.get_hit_rate(),
                }
            elif isinstance(cache, FileCache):
                usage[name] = {
                    "size": cache._get_cache_count(),
                    "max_size": cache.cache_size,
                    "hit_rate": cache.get_hit_rate(),
                }

        return usage
