"""
Ticker数据收集器
"""

import time
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd
import requests

from .kline_collector import BaseCollector


class TickerCollector(BaseCollector):
    """Ticker数据收集器"""

    def __init__(self, name: str = "TickerCollector", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.api_endpoint = config.get("api_endpoint", "") if config else ""
        self.api_key = config.get("api_key", "") if config else ""
        self.timeout = config.get("timeout", 30) if config else 30

    def collect(self, symbol: str, **kwargs) -> pd.DataFrame | None:
        """
        收集Ticker数据

        Args:
            symbol: 交易标的
            **kwargs: 其他参数

        Returns:
            Ticker数据DataFrame
        """
        try:
            # 构建API请求参数
            params = {"symbol": symbol}
            params.update(kwargs)

            # 发送请求
            if self.api_endpoint:
                response = requests.get(
                    self.api_endpoint,
                    params=params,
                    timeout=self.timeout,
                    headers={"X-MBX-APIKEY": self.api_key} if self.api_key else {},
                )
                response.raise_for_status()
                data = response.json()
            else:
                # 如果没有API端点，生成模拟数据
                data = self._generate_mock_ticker_data(symbol)

            # 转换为DataFrame
            df = self._parse_ticker_data(data)

            if df is not None:
                self.data_history.append(
                    {"symbol": symbol, "timestamp": datetime.now(), "data_points": 1}
                )

            return df

        except Exception as e:
            print(f"Error collecting ticker data for {symbol}: {e}")
            return None

    def _parse_ticker_data(self, data: dict) -> pd.DataFrame:
        """解析Ticker数据"""
        if not data:
            return None

        # 创建单行DataFrame
        df = pd.DataFrame([data])

        # 添加时间戳
        df["timestamp"] = datetime.now()
        df.set_index("timestamp", inplace=True)

        # 转换数值列
        numeric_columns = ["price", "volume", "bid", "ask", "high", "low", "open"]
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors="coerce")

        return df

    def _generate_mock_ticker_data(self, symbol: str) -> dict[str, Any]:
        """生成模拟Ticker数据"""
        base_price = 50000

        return {
            "symbol": symbol,
            "price": base_price + np.random.normal(0, base_price * 0.01),
            "volume": np.random.uniform(1000, 10000),
            "bid": base_price - 10,
            "ask": base_price + 10,
            "high": base_price + base_price * 0.02,
            "low": base_price - base_price * 0.02,
            "open": base_price,
            "close_time": int(datetime.now().timestamp() * 1000),
            "count": np.random.randint(100, 1000),
        }

    def collect_multiple_symbols(self, symbols: list[str]) -> pd.DataFrame:
        """
        收集多个标的的Ticker数据

        Args:
            symbols: 交易标的列表

        Returns:
            多个标的的Ticker数据
        """
        all_data = []

        for symbol in symbols:
            df = self.collect(symbol=symbol)
            if df is not None:
                all_data.append(df)
            time.sleep(0.1)  # 避免API限制

        if all_data:
            return pd.concat(all_data)
        return pd.DataFrame()

    def get_24h_ticker(self, symbol: str) -> pd.DataFrame | None:
        """获取24小时Ticker统计"""
        try:
            if self.api_endpoint:
                # 假设API端点支持24小时统计
                endpoint = f"{self.api_endpoint}/24hr"
                params = {"symbol": symbol}

                response = requests.get(
                    endpoint,
                    params=params,
                    timeout=self.timeout,
                    headers={"X-MBX-APIKEY": self.api_key} if self.api_key else {},
                )
                response.raise_for_status()
                data = response.json()

                # 转换为DataFrame
                df = pd.DataFrame([data])
                df["timestamp"] = datetime.now()
                df.set_index("timestamp", inplace=True)

                # 转换数值列
                numeric_columns = [
                    "priceChange",
                    "priceChangePercent",
                    "lastPrice",
                    "openPrice",
                    "highPrice",
                    "lowPrice",
                    "volume",
                    "count",
                ]
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors="coerce")

                return df

        except Exception as e:
            print(f"Error getting 24h ticker for {symbol}: {e}")

        return None

    def get_ticker_history(self, symbol: str, hours: int = 24) -> pd.DataFrame:
        """
        获取Ticker历史数据

        Args:
            symbol: 交易标的
            hours: 历史小时数

        Returns:
            Ticker历史数据
        """
        historical_data = []

        for i in range(hours):
            # 生成历史时间点
            timestamp = datetime.now() - timedelta(hours=i)

            # 收集该时间点的数据
            df = self.collect(symbol=symbol)
            if df is not None:
                df.index = [timestamp]
                historical_data.append(df)

            time.sleep(0.01)

        if historical_data:
            return pd.concat(historical_data).sort_index()
        return pd.DataFrame()

    def calculate_price_change(
        self, symbol: str, period: str = "1h"
    ) -> float | None:
        """
        计算价格变化

        Args:
            symbol: 交易标的
            period: 时间周期

        Returns:
            价格变化百分比
        """
        try:
            # 获取历史数据
            if period == "1h":
                hours = 1
            elif period == "24h":
                hours = 24
            else:
                hours = 1

            history = self.get_ticker_history(symbol, hours)

            if len(history) >= 2:
                current_price = history["price"].iloc[-1]
                previous_price = history["price"].iloc[0]
                change_percent = (current_price - previous_price) / previous_price * 100
                return change_percent

        except Exception as e:
            print(f"Error calculating price change for {symbol}: {e}")

        return None

    def get_market_summary(self, symbols: list[str]) -> dict[str, Any]:
        """
        获取市场摘要

        Args:
            symbols: 交易标的列表

        Returns:
            市场摘要
        """
        summary = {
            "total_symbols": len(symbols),
            "timestamp": datetime.now(),
            "symbols_data": {},
        }

        # 收集所有标的的数据
        df = self.collect_multiple_symbols(symbols)

        if not df.empty:
            # 计算市场统计
            summary["market_stats"] = {
                "avg_price": df["price"].mean(),
                "total_volume": df["volume"].sum(),
                "price_std": df["price"].std(),
                "price_range": {"min": df["price"].min(), "max": df["price"].max()},
            }

            # 为每个标的生成摘要
            for symbol in symbols:
                symbol_data = df[df["symbol"] == symbol]
                if not symbol_data.empty:
                    summary["symbols_data"][symbol] = {
                        "price": symbol_data["price"].iloc[-1],
                        "volume": symbol_data["volume"].iloc[-1],
                        "bid": symbol_data["bid"].iloc[-1],
                        "ask": symbol_data["ask"].iloc[-1],
                    }

        return summary
