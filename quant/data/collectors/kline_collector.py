"""
K线数据收集器
"""

import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any

import numpy as np
import pandas as pd
import requests


class BaseCollector(ABC):
    """数据收集器基类"""

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.is_running = False
        self.data_history = []

    @abstractmethod
    def collect(self, symbol: str, **kwargs) -> pd.DataFrame | None:
        """收集数据"""
        pass

    def start(self):
        """启动收集器"""
        self.is_running = True

    def stop(self):
        """停止收集器"""
        self.is_running = False

    def get_status(self) -> dict[str, Any]:
        """获取状态"""
        return {
            "name": self.name,
            "is_running": self.is_running,
            "data_count": len(self.data_history),
        }


class KlineCollector(BaseCollector):
    """K线数据收集器"""

    def __init__(self, name: str = "KlineCollector", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.api_endpoint = config.get("api_endpoint", "") if config else ""
        self.api_key = config.get("api_key", "") if config else ""
        self.timeout = config.get("timeout", 30) if config else 30

    def collect(
        self, symbol: str, interval: str = "1m", limit: int = 100, **kwargs
    ) -> pd.DataFrame | None:
        """
        收集K线数据

        Args:
            symbol: 交易标的
            interval: 时间间隔 (1m, 5m, 15m, 1h, 4h, 1d)
            limit: 数据条数
            **kwargs: 其他参数

        Returns:
            K线数据DataFrame
        """
        try:
            # 构建API请求参数
            params = {"symbol": symbol, "interval": interval, "limit": limit}
            params.update(kwargs)

            # 发送请求
            if self.api_endpoint:
                response = requests.get(
                    self.api_endpoint,
                    params=params,
                    timeout=self.timeout,
                    headers={"X-MBX-APIKEY": self.api_key} if self.api_key else {},
                )
                response.raise_for_status()
                data = response.json()
            else:
                # 如果没有API端点，生成模拟数据
                data = self._generate_mock_kline_data(symbol, interval, limit)

            # 转换为DataFrame
            df = self._parse_kline_data(data)

            if df is not None:
                self.data_history.append(
                    {
                        "symbol": symbol,
                        "interval": interval,
                        "timestamp": datetime.now(),
                        "data_points": len(df),
                    }
                )

            return df

        except Exception as e:
            print(f"Error collecting kline data for {symbol}: {e}")
            return None

    def _parse_kline_data(self, data: list[list]) -> pd.DataFrame:
        """解析K线数据"""
        if not data:
            return None

        # 标准K线数据格式: [timestamp, open, high, low, close, volume, ...]
        df = pd.DataFrame(
            data,
            columns=[
                "timestamp",
                "open",
                "high",
                "low",
                "close",
                "volume",
                "close_time",
                "quote_volume",
                "count",
                "taker_buy_volume",
                "taker_buy_quote_volume",
                "ignore",
            ],
        )

        # 转换数据类型
        numeric_columns = ["open", "high", "low", "close", "volume"]
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")

        # 转换时间戳
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
        df.set_index("timestamp", inplace=True)

        return df

    def _generate_mock_kline_data(
        self, symbol: str, interval: str, limit: int
    ) -> list[list]:
        """生成模拟K线数据"""
        data = []
        base_price = 50000  # 基础价格

        for i in range(limit):
            timestamp = int((datetime.now() - timedelta(minutes=i)).timestamp() * 1000)

            # 生成随机价格
            price_change = np.random.normal(0, base_price * 0.01)
            open_price = base_price + price_change
            close_price = open_price + np.random.normal(0, base_price * 0.005)
            high_price = max(open_price, close_price) + abs(
                np.random.normal(0, base_price * 0.003)
            )
            low_price = min(open_price, close_price) - abs(
                np.random.normal(0, base_price * 0.003)
            )
            volume = np.random.uniform(100, 1000)

            kline = [
                timestamp,
                open_price,
                high_price,
                low_price,
                close_price,
                volume,
                timestamp + 60000,  # close_time
                volume * close_price,  # quote_volume
                100,  # count
                volume * 0.6,  # taker_buy_volume
                volume * 0.6 * close_price,  # taker_buy_quote_volume
                0,  # ignore
            ]

            data.append(kline)
            base_price = close_price

        return data[::-1]  # 反转使数据按时间顺序排列

    def collect_historical_data(
        self,
        symbol: str,
        start_time: datetime,
        end_time: datetime,
        interval: str = "1m",
    ) -> pd.DataFrame:
        """
        收集历史K线数据

        Args:
            symbol: 交易标的
            start_time: 开始时间
            end_time: 结束时间
            interval: 时间间隔

        Returns:
            历史K线数据
        """
        all_data = []
        current_time = start_time

        while current_time < end_time:
            # 每次请求最多1000条数据
            limit = min(1000, int((end_time - current_time).total_seconds() / 60) + 1)

            df = self.collect(
                symbol=symbol,
                interval=interval,
                limit=limit,
                startTime=int(current_time.timestamp() * 1000),
                endTime=int(end_time.timestamp() * 1000),
            )

            if df is not None:
                all_data.append(df)
                current_time = df.index[-1] + timedelta(minutes=1)
            else:
                break

            time.sleep(0.1)  # 避免API限制

        if all_data:
            return pd.concat(all_data).sort_index()
        return pd.DataFrame()

    def get_latest_kline(
        self, symbol: str, interval: str = "1m"
    ) -> pd.DataFrame | None:
        """获取最新K线数据"""
        df = self.collect(symbol=symbol, interval=interval, limit=1)
        return df

    def get_data_history(self) -> list[dict[str, Any]]:
        """获取数据收集历史"""
        return self.data_history.copy()
