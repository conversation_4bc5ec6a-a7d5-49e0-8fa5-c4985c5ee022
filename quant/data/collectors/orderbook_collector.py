"""
Orderbook数据收集器
"""

import time
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd
import requests

from .kline_collector import BaseCollector


class OrderbookCollector(BaseCollector):
    """订单簿数据收集器"""

    def __init__(self, name: str = "OrderbookCollector", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.api_endpoint = config.get("api_endpoint", "") if config else ""
        self.api_key = config.get("api_key", "") if config else ""
        self.timeout = config.get("timeout", 30) if config else 30
        self.depth = config.get("depth", 100) if config else 100  # 订单簿深度

    def collect(self, symbol: str, **kwargs) -> pd.DataFrame | None:
        """
        收集订单簿数据

        Args:
            symbol: 交易标的
            **kwargs: 其他参数

        Returns:
            订单簿数据DataFrame
        """
        try:
            # 构建API请求参数
            params = {"symbol": symbol, "limit": self.depth}
            params.update(kwargs)

            # 发送请求
            if self.api_endpoint:
                response = requests.get(
                    self.api_endpoint,
                    params=params,
                    timeout=self.timeout,
                    headers={"X-MBX-APIKEY": self.api_key} if self.api_key else {},
                )
                response.raise_for_status()
                data = response.json()
            else:
                # 如果没有API端点，生成模拟数据
                data = self._generate_mock_orderbook_data(symbol)

            # 转换为DataFrame
            df = self._parse_orderbook_data(data)

            if df is not None:
                self.data_history.append(
                    {
                        "symbol": symbol,
                        "timestamp": datetime.now(),
                        "bids_count": len(data.get("bids", [])),
                        "asks_count": len(data.get("asks", [])),
                    }
                )

            return df

        except Exception as e:
            print(f"Error collecting orderbook data for {symbol}: {e}")
            return None

    def _parse_orderbook_data(self, data: dict) -> pd.DataFrame:
        """解析订单簿数据"""
        if not data:
            return None

        # 解析买单
        bids_data = []
        for bid in data.get("bids", []):
            bids_data.append(
                {
                    "side": "bid",
                    "price": float(bid[0]),
                    "quantity": float(bid[1]),
                    "total": float(bid[0]) * float(bid[1]),
                }
            )

        # 解析卖单
        asks_data = []
        for ask in data.get("asks", []):
            asks_data.append(
                {
                    "side": "ask",
                    "price": float(ask[0]),
                    "quantity": float(ask[1]),
                    "total": float(ask[0]) * float(ask[1]),
                }
            )

        # 合并数据
        all_data = bids_data + asks_data
        df = pd.DataFrame(all_data)

        # 添加时间戳
        df["timestamp"] = datetime.now()

        # 按价格排序
        df = df.sort_values(["side", "price"], ascending=[False, False])

        return df

    def _generate_mock_orderbook_data(self, symbol: str) -> dict[str, Any]:
        """生成模拟订单簿数据"""
        base_price = 50000

        # 生成买单
        bids = []
        for i in range(self.depth):
            price = base_price - i * 10
            quantity = np.random.uniform(0.1, 10)
            bids.append([str(price), str(quantity)])

        # 生成卖单
        asks = []
        for i in range(self.depth):
            price = base_price + i * 10
            quantity = np.random.uniform(0.1, 10)
            asks.append([str(price), str(quantity)])

        return {
            "symbol": symbol,
            "bids": bids,
            "asks": asks,
            "timestamp": int(datetime.now().timestamp() * 1000),
        }

    def get_spread(self, symbol: str) -> float | None:
        """获取买卖价差"""
        df = self.collect(symbol=symbol)

        if df is not None and not df.empty:
            best_bid = df[df["side"] == "bid"]["price"].max()
            best_ask = df[df["side"] == "ask"]["price"].min()

            if best_bid and best_ask:
                return best_ask - best_bid

        return None

    def get_orderbook_imbalance(self, symbol: str, depth: int = 10) -> float | None:
        """
        计算订单簿不平衡度

        Args:
            symbol: 交易标的
            depth: 计算深度

        Returns:
            不平衡度 (-1到1之间)
        """
        df = self.collect(symbol=symbol)

        if df is None or df.empty:
            return None

        # 获取前depth层买单和卖单
        top_bids = df[df["side"] == "bid"].head(depth)
        top_asks = df[df["side"] == "ask"].head(depth)

        if top_bids.empty or top_asks.empty:
            return None

        # 计算买单和卖单的总价值
        bid_value = top_bids["total"].sum()
        ask_value = top_asks["total"].sum()

        # 计算不平衡度
        total_value = bid_value + ask_value
        if total_value > 0:
            imbalance = (bid_value - ask_value) / total_value
            return imbalance

        return None

    def get_liquidity_metrics(self, symbol: str) -> dict[str, Any]:
        """
        获取流动性指标

        Args:
            symbol: 交易标的

        Returns:
            流动性指标
        """
        df = self.collect(symbol=symbol)

        if df is None or df.empty:
            return {}

        # 分离买单和卖单
        bids = df[df["side"] == "bid"]
        asks = df[df["side"] == "ask"]

        if bids.empty or asks.empty:
            return {}

        # 计算流动性指标
        spread = asks["price"].min() - bids["price"].max()
        mid_price = (asks["price"].min() + bids["price"].max()) / 2

        # 计算不同深度累计数量
        cum_bids = bids["quantity"].cumsum()
        cum_asks = asks["quantity"].cumsum()

        # 计算市场深度
        depth_1pct = self._calculate_depth_at_percentage(bids, asks, 0.01)
        depth_5pct = self._calculate_depth_at_percentage(bids, asks, 0.05)

        return {
            "spread": spread,
            "spread_percentage": spread / mid_price * 100 if mid_price > 0 else 0,
            "mid_price": mid_price,
            "best_bid": bids["price"].max(),
            "best_ask": asks["price"].min(),
            "bid_volume": bids["quantity"].sum(),
            "ask_volume": asks["quantity"].sum(),
            "total_volume": bids["quantity"].sum() + asks["quantity"].sum(),
            "bid_count": len(bids),
            "ask_count": len(asks),
            "depth_1pct": depth_1pct,
            "depth_5pct": depth_5pct,
            "orderbook_imbalance": self.get_orderbook_imbalance(symbol),
        }

    def _calculate_depth_at_percentage(
        self, bids: pd.DataFrame, asks: pd.DataFrame, percentage: float
    ) -> dict[str, float]:
        """计算指定百分比深度的流动性"""
        if bids.empty or asks.empty:
            return {}

        mid_price = (asks["price"].min() + bids["price"].max()) / 2

        # 计算价格范围
        price_range = mid_price * percentage

        # 筛选范围内的订单
        bid_range = bids[
            (bids["price"] >= mid_price - price_range) & (bids["price"] <= mid_price)
        ]
        ask_range = asks[
            (asks["price"] >= mid_price) & (asks["price"] <= mid_price + price_range)
        ]

        return {
            "bid_depth": bid_range["quantity"].sum(),
            "ask_depth": ask_range["quantity"].sum(),
            "total_depth": bid_range["quantity"].sum() + ask_range["quantity"].sum(),
        }

    def monitor_orderbook_changes(
        self, symbol: str, interval: float = 1.0, duration: int = 60
    ) -> pd.DataFrame:
        """
        监控订单簿变化

        Args:
            symbol: 交易标的
            interval: 采样间隔（秒）
            duration: 监控时长（秒）

        Returns:
            订单簿变化数据
        """
        changes_data = []
        start_time = time.time()

        while time.time() - start_time < duration:
            current_time = datetime.now()

            # 收集当前订单簿
            df = self.collect(symbol=symbol)

            if df is not None and not df.empty:
                # 计算指标
                metrics = self.get_liquidity_metrics(symbol)
                metrics["timestamp"] = current_time
                changes_data.append(metrics)

            time.sleep(interval)

        if changes_data:
            return pd.DataFrame(changes_data).set_index("timestamp")
        return pd.DataFrame()

    def get_orderbook_snapshot(self, symbol: str, levels: int = 10) -> dict[str, Any]:
        """
        获取订单簿快照

        Args:
            symbol: 交易标的
            levels: 显示层级

        Returns:
            订单簿快照
        """
        df = self.collect(symbol=symbol)

        if df is None or df.empty:
            return {}

        # 获取前n层买单和卖单
        top_bids = df[df["side"] == "bid"].head(levels)
        top_asks = df[df["side"] == "ask"].head(levels)

        snapshot = {
            "symbol": symbol,
            "timestamp": datetime.now(),
            "bids": top_bids[["price", "quantity"]].to_dict("records"),
            "asks": top_asks[["price", "quantity"]].to_dict("records"),
            "summary": self.get_liquidity_metrics(symbol),
        }

        return snapshot
