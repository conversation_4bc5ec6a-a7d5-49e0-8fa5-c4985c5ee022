"""
Data Aggregator
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

import numpy as np
import pandas as pd

from .cleaner import BaseProcessor


class DataAggregator(BaseProcessor):
    """数据聚合器"""

    def __init__(self, name: str = "DataAggregator", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.default_aggregation = (
            config.get("default_aggregation", "mean") if config else "mean"
        )
        self.time_columns = config.get("time_columns", []) if config else []
        self.categorical_columns = (
            config.get("categorical_columns", []) if config else []
        )
        self.numeric_columns = config.get("numeric_columns", []) if config else []

    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        聚合数据

        Args:
            data: 原始数据

        Returns:
            聚合后的数据
        """
        if data.empty:
            return data

        processed_data = data.copy()
        process_info = {
            "timestamp": datetime.now(),
            "original_shape": data.shape,
            "success": True,
        }

        try:
            # 检查是否需要聚合
            if self._needs_aggregation(processed_data):
                processed_data = self._aggregate_data(processed_data)
                process_info["aggregated"] = True
            else:
                process_info["aggregated"] = False
                process_info["message"] = "No aggregation needed"

            process_info["final_shape"] = processed_data.shape

        except Exception as e:
            process_info["success"] = False
            process_info["error"] = str(e)
            print(f"Error in data aggregation: {e}")

        self.processing_history.append(process_info)
        return processed_data

    def _needs_aggregation(self, data: pd.DataFrame) -> bool:
        """检查是否需要聚合"""
        # 如果没有时间索引，可能需要按其他列聚合
        if not isinstance(data.index, pd.DatetimeIndex):
            return len(self.categorical_columns) > 0

        # 如果数据量很大，可能需要时间聚合
        if len(data) > 10000:
            return True

        return False

    def _aggregate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """执行数据聚合"""
        # 如果有时间索引，进行时间聚合
        if isinstance(data.index, pd.DatetimeIndex):
            return self._aggregate_by_time(data)

        # 如果有分类列，进行分组聚合
        elif self.categorical_columns:
            return self._aggregate_by_categories(data)

        # 默认返回原数据
        return data

    def _aggregate_by_time(self, data: pd.DataFrame) -> pd.DataFrame:
        """按时间聚合"""
        # 确定聚合频率
        freq = self._determine_time_frequency(data)

        if freq is None:
            return data

        # 获取数值列
        numeric_cols = self._get_numeric_columns(data)

        if not numeric_cols:
            return data

        # 创建聚合字典
        agg_dict = self._create_aggregation_dict(numeric_cols)

        # 执行重采样聚合
        aggregated_data = data.resample(freq).agg(agg_dict)

        # 展平多级列索引
        if isinstance(aggregated_data.columns, pd.MultiIndex):
            aggregated_data.columns = [
                "_".join(col).strip() for col in aggregated_data.columns.values
            ]

        # 移除空值
        aggregated_data = aggregated_data.dropna()

        return aggregated_data

    def _determine_time_frequency(self, data: pd.DataFrame) -> str | None:
        """确定时间聚合频率"""
        if len(data) < 2:
            return None

        # 计算时间间隔
        time_diffs = data.index.to_series().diff().dropna()

        if len(time_diffs) == 0:
            return None

        median_diff = time_diffs.median()

        # 根据时间间隔确定聚合频率
        if median_diff <= timedelta(minutes=1):
            return "5T"  # 5分钟
        elif median_diff <= timedelta(minutes=5):
            return "15T"  # 15分钟
        elif median_diff <= timedelta(minutes=15):
            return "1H"  # 1小时
        elif median_diff <= timedelta(hours=1):
            return "4H"  # 4小时
        elif median_diff <= timedelta(hours=4):
            return "1D"  # 1天
        elif median_diff <= timedelta(days=1):
            return "1W"  # 1周
        else:
            return "1M"  # 1月

    def _aggregate_by_categories(self, data: pd.DataFrame) -> pd.DataFrame:
        """按分类列聚合"""
        if not self.categorical_columns:
            return data

        # 获取数值列
        numeric_cols = self._get_numeric_columns(data)

        if not numeric_cols:
            return data

        # 创建聚合字典
        agg_dict = self._create_aggregation_dict(numeric_cols)

        # 执行分组聚合
        grouped_data = data.groupby(self.categorical_columns).agg(agg_dict)

        # 展平多级列索引
        if isinstance(grouped_data.columns, pd.MultiIndex):
            grouped_data.columns = [
                "_".join(col).strip() for col in grouped_data.columns.values
            ]

        return grouped_data.reset_index()

    def _get_numeric_columns(self, data: pd.DataFrame) -> list[str]:
        """获取数值列"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()

        # 如果指定了数值列，只使用指定的列
        if self.numeric_columns:
            numeric_columns = [
                col for col in numeric_columns if col in self.numeric_columns
            ]

        return numeric_columns

    def _create_aggregation_dict(self, columns: list[str]) -> dict[str, list[str]]:
        """创建聚合字典"""
        agg_dict = {}

        for col in columns:
            # 默认使用多种聚合函数
            agg_dict[col] = ["mean", "std", "min", "max", "sum"]

        return agg_dict

    def aggregate_custom(
        self,
        data: pd.DataFrame,
        group_by: str | list[str],
        agg_functions: dict[str, str | list[str]],
    ) -> pd.DataFrame:
        """
        自定义聚合

        Args:
            data: 原始数据
            group_by: 分组列
            agg_functions: 聚合函数字典

        Returns:
            聚合后的数据
        """
        if data.empty:
            return data

        try:
            # 执行聚合
            aggregated_data = data.groupby(group_by).agg(agg_functions)

            # 展平多级列索引
            if isinstance(aggregated_data.columns, pd.MultiIndex):
                aggregated_data.columns = [
                    "_".join(col).strip() for col in aggregated_data.columns.values
                ]

            return aggregated_data.reset_index()

        except Exception as e:
            print(f"Error in custom aggregation: {e}")
            return data

    def rolling_aggregate(
        self,
        data: pd.DataFrame,
        window: int | str,
        agg_functions: dict[str, str | list[str]],
    ) -> pd.DataFrame:
        """
        滚动聚合

        Args:
            data: 原始数据
            window: 窗口大小
            agg_functions: 聚合函数字典

        Returns:
            滚动聚合后的数据
        """
        if data.empty:
            return data

        try:
            # 确保数据是时间序列
            if not isinstance(data.index, pd.DatetimeIndex):
                raise ValueError(
                    "Data must have a datetime index for rolling aggregation"
                )

            # 执行滚动聚合
            result_data = data.copy()

            for col, funcs in agg_functions.items():
                if col in data.columns:
                    if isinstance(funcs, str):
                        result_data[f"{col}_rolling_{funcs}"] = (
                            data[col].rolling(window).agg(funcs)
                        )
                    elif isinstance(funcs, list):
                        for func in funcs:
                            result_data[f"{col}_rolling_{func}"] = (
                                data[col].rolling(window).agg(func)
                            )

            return result_data

        except Exception as e:
            print(f"Error in rolling aggregation: {e}")
            return data

    def expanding_aggregate(
        self, data: pd.DataFrame, agg_functions: dict[str, str | list[str]]
    ) -> pd.DataFrame:
        """
        扩展聚合

        Args:
            data: 原始数据
            agg_functions: 聚合函数字典

        Returns:
            扩展聚合后的数据
        """
        if data.empty:
            return data

        try:
            # 确保数据是时间序列
            if not isinstance(data.index, pd.DatetimeIndex):
                raise ValueError(
                    "Data must have a datetime index for expanding aggregation"
                )

            # 执行扩展聚合
            result_data = data.copy()

            for col, funcs in agg_functions.items():
                if col in data.columns:
                    if isinstance(funcs, str):
                        result_data[f"{col}_expanding_{funcs}"] = (
                            data[col].expanding().agg(funcs)
                        )
                    elif isinstance(funcs, list):
                        for func in funcs:
                            result_data[f"{col}_expanding_{func}"] = (
                                data[col].expanding().agg(func)
                            )

            return result_data

        except Exception as e:
            print(f"Error in expanding aggregation: {e}")
            return data

    def pivot_aggregate(
        self,
        data: pd.DataFrame,
        index: str,
        columns: str,
        values: str,
        aggfunc: str = "mean",
    ) -> pd.DataFrame:
        """
        透视聚合

        Args:
            data: 原始数据
            index: 索引列
            columns: 列列
            values: 值列
            aggfunc: 聚合函数

        Returns:
            透视聚合后的数据
        """
        if data.empty:
            return data

        try:
            # 创建透视表
            pivot_data = data.pivot_table(
                index=index,
                columns=columns,
                values=values,
                aggfunc=aggfunc,
                fill_value=0,
            )

            return pivot_data

        except Exception as e:
            print(f"Error in pivot aggregation: {e}")
            return data

    def get_aggregation_summary(self, data: pd.DataFrame) -> dict[str, Any]:
        """获取聚合摘要"""
        summary = {
            "timestamp": datetime.now(),
            "data_shape": data.shape,
            "column_types": {},
            "suggested_aggregations": {},
        }

        # 分析列类型
        for col in data.columns:
            col_type = str(data[col].dtype)
            summary["column_types"][col] = col_type

            # 建议聚合方法
            if "int" in col_type or "float" in col_type:
                summary["suggested_aggregations"][col] = [
                    "mean",
                    "std",
                    "min",
                    "max",
                    "sum",
                ]
            elif "object" in col_type:
                summary["suggested_aggregations"][col] = ["count", "nunique"]
            elif "datetime" in col_type:
                summary["suggested_aggregations"][col] = ["count", "first", "last"]

        # 如果有时间索引，建议时间聚合
        if isinstance(data.index, pd.DatetimeIndex):
            time_range = data.index.max() - data.index.min()
            if time_range > timedelta(days=1):
                summary["suggested_time_aggregation"] = "daily"
            elif time_range > timedelta(hours=1):
                summary["suggested_time_aggregation"] = "hourly"
            else:
                summary["suggested_time_aggregation"] = "minute"

        return summary
