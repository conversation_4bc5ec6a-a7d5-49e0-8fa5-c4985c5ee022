"""
Data Cleaner
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd


class BaseProcessor(ABC):
    """数据处理器基类"""

    def __init__(self, name: str, config: dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.processing_history = []

    @abstractmethod
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理数据"""
        pass

    def get_processing_stats(self) -> dict[str, Any]:
        """获取处理统计"""
        return {
            "name": self.name,
            "processing_count": len(self.processing_history),
            "last_processed": (
                self.processing_history[-1] if self.processing_history else None
            ),
        }


class DataCleaner(BaseProcessor):
    """数据清洗器"""

    def __init__(self, name: str = "DataCleaner", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.remove_outliers = config.get("remove_outliers", True) if config else True
        self.fill_missing = config.get("fill_missing", True) if config else True
        self.outlier_method = (
            config.get("outlier_method", "zscore") if config else "zscore"
        )
        self.outlier_threshold = config.get("outlier_threshold", 3.0) if config else 3.0

    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据

        Args:
            data: 原始数据

        Returns:
            清洗后的数据
        """
        if data.empty:
            return data

        processed_data = data.copy()
        original_shape = processed_data.shape

        # 记录处理开始
        process_info = {
            "timestamp": datetime.now(),
            "original_shape": original_shape,
            "steps": [],
        }

        try:
            # 1. 处理缺失值
            if self.fill_missing:
                processed_data = self._handle_missing_values(processed_data)
                process_info["steps"].append("filled_missing_values")

            # 2. 处理异常值
            if self.remove_outliers:
                processed_data = self._handle_outliers(processed_data)
                process_info["steps"].append("removed_outliers")

            # 3. 处理重复数据
            processed_data = self._handle_duplicates(processed_data)
            process_info["steps"].append("removed_duplicates")

            # 4. 数据类型转换
            processed_data = self._convert_data_types(processed_data)
            process_info["steps"].append("converted_data_types")

            # 5. 时间序列处理
            if isinstance(processed_data.index, pd.DatetimeIndex):
                processed_data = self._handle_time_series(processed_data)
                process_info["steps"].append("processed_time_series")

            process_info["final_shape"] = processed_data.shape
            process_info["success"] = True

        except Exception as e:
            process_info["success"] = False
            process_info["error"] = str(e)
            print(f"Error in data cleaning: {e}")

        self.processing_history.append(process_info)
        return processed_data

    def _handle_missing_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 检查缺失值
        missing_info = data.isnull().sum()

        if missing_info.sum() == 0:
            return data

        # 对不同类型的列使用不同的填充策略
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        non_numeric_columns = data.select_dtypes(exclude=[np.number]).columns

        # 数值列：使用前向填充或中位数填充
        for col in numeric_columns:
            if data[col].isnull().sum() > 0:
                # 如果是时间序列，优先使用前向填充
                if isinstance(data.index, pd.DatetimeIndex):
                    data[col] = data[col].fillna(method="ffill")

                # 如果仍有缺失值，使用中位数填充
                if data[col].isnull().sum() > 0:
                    median_value = data[col].median()
                    data[col] = data[col].fillna(median_value)

        # 非数值列：使用前向填充或众数填充
        for col in non_numeric_columns:
            if data[col].isnull().sum() > 0:
                if isinstance(data.index, pd.DatetimeIndex):
                    data[col] = data[col].fillna(method="ffill")

                if data[col].isnull().sum() > 0:
                    mode_value = data[col].mode()
                    if len(mode_value) > 0:
                        data[col] = data[col].fillna(mode_value[0])

        return data

    def _handle_outliers(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理异常值"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            if self.outlier_method == "zscore":
                data = self._remove_outliers_zscore(data, col)
            elif self.outlier_method == "iqr":
                data = self._remove_outliers_iqr(data, col)

        return data

    def _remove_outliers_zscore(self, data: pd.DataFrame, column: str) -> pd.DataFrame:
        """使用Z-score方法移除异常值"""
        z_scores = np.abs((data[column] - data[column].mean()) / data[column].std())
        return data[z_scores <= self.outlier_threshold]

    def _remove_outliers_iqr(self, data: pd.DataFrame, column: str) -> pd.DataFrame:
        """使用IQR方法移除异常值"""
        Q1 = data[column].quantile(0.25)
        Q3 = data[column].quantile(0.75)
        IQR = Q3 - Q1

        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        return data[(data[column] >= lower_bound) & (data[column] <= upper_bound)]

    def _handle_duplicates(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理重复数据"""
        # 检查完全重复的行
        duplicates = data.duplicated()

        if duplicates.sum() > 0:
            data = data.drop_duplicates()

        # 如果有时间索引，检查时间戳重复
        if isinstance(data.index, pd.DatetimeIndex):
            time_duplicates = data.index.duplicated()
            if time_duplicates.sum() > 0:
                data = data[~time_duplicates]

        return data

    def _convert_data_types(self, data: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型"""
        # 尝试将看起来像数字的列转换为数值类型
        for col in data.columns:
            if data[col].dtype == "object":
                try:
                    data[col] = pd.to_numeric(data[col], errors="ignore")
                except:
                    pass

        return data

    def _handle_time_series(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理时间序列数据"""
        # 确保时间索引是有序的
        if not data.index.is_monotonic_increasing:
            data = data.sort_index()

        # 检查时间间隔是否一致
        if len(data) > 1:
            time_diffs = data.index.to_series().diff().dropna()
            if len(time_diffs.unique()) > 1:
                # 时间间隔不一致，考虑重采样
                pass

        return data

    def get_data_quality_report(self, data: pd.DataFrame) -> dict[str, Any]:
        """生成数据质量报告"""
        report = {
            "timestamp": datetime.now(),
            "data_shape": data.shape,
            "columns": {},
            "overall_quality": {},
        }

        # 列级别统计
        for col in data.columns:
            col_info = {
                "dtype": str(data[col].dtype),
                "null_count": data[col].isnull().sum(),
                "null_percentage": data[col].isnull().sum() / len(data) * 100,
                "unique_count": data[col].nunique(),
                "duplicate_count": data[col].duplicated().sum(),
            }

            # 数值列的额外统计
            if pd.api.types.is_numeric_dtype(data[col]):
                col_info.update(
                    {
                        "mean": data[col].mean(),
                        "std": data[col].std(),
                        "min": data[col].min(),
                        "max": data[col].max(),
                        "outliers_count": self._count_outliers(data[col]),
                    }
                )

            report["columns"][col] = col_info

        # 整体质量统计
        report["overall_quality"] = {
            "total_nulls": data.isnull().sum().sum(),
            "total_duplicates": data.duplicated().sum(),
            "completeness": (
                1 - data.isnull().sum().sum() / (data.shape[0] * data.shape[1])
            )
            * 100,
            "uniqueness": (1 - data.duplicated().sum() / len(data)) * 100,
        }

        return report

    def _count_outliers(self, series: pd.Series) -> int:
        """统计异常值数量"""
        if self.outlier_method == "zscore":
            z_scores = np.abs((series - series.mean()) / series.std())
            return (z_scores > self.outlier_threshold).sum()
        elif self.outlier_method == "iqr":
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            return ((series < lower_bound) | (series > upper_bound)).sum()
        return 0

    def validate_data_schema(
        self,
        data: pd.DataFrame,
        required_columns: list[str] = None,
        expected_dtypes: dict[str, str] = None,
    ) -> dict[str, Any]:
        """验证数据模式"""
        validation_result = {
            "timestamp": datetime.now(),
            "is_valid": True,
            "errors": [],
            "warnings": [],
        }

        # 检查必需列
        if required_columns:
            missing_columns = set(required_columns) - set(data.columns)
            if missing_columns:
                validation_result["is_valid"] = False
                validation_result["errors"].append(
                    f"Missing required columns: {missing_columns}"
                )

        # 检查数据类型
        if expected_dtypes:
            for col, expected_type in expected_dtypes.items():
                if col in data.columns:
                    actual_type = str(data[col].dtype)
                    if expected_type not in actual_type:
                        validation_result["warnings"].append(
                            f"Column '{col}' has dtype '{actual_type}', expected '{expected_type}'"
                        )

        # 检查数据完整性
        if data.empty:
            validation_result["is_valid"] = False
            validation_result["errors"].append("Data is empty")

        return validation_result
