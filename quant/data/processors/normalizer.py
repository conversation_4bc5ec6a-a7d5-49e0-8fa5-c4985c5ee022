"""
Data Normalizer
"""

import os
import pickle
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.preprocessing import MinMaxScaler, RobustScaler, StandardScaler

from .cleaner import BaseProcessor


class DataNormalizer(BaseProcessor):
    """数据标准化器"""

    def __init__(self, name: str = "DataNormalizer", config: dict[str, Any] = None):
        super().__init__(name, config)
        self.scalers = {}
        self.pca_transformers = {}
        self.normalization_method = (
            config.get("normalization_method", "standard") if config else "standard"
        )
        self.feature_columns = config.get("feature_columns", []) if config else []

    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        标准化数据

        Args:
            data: 原始数据

        Returns:
            标准化后的数据
        """
        if data.empty:
            return data

        processed_data = data.copy()
        process_info = {
            "timestamp": datetime.now(),
            "original_shape": data.shape,
            "method": self.normalization_method,
            "success": True,
        }

        try:
            # 选择要标准化的列
            numeric_columns = self._get_numeric_columns(processed_data)

            if not numeric_columns:
                process_info["message"] = "No numeric columns to normalize"
                self.processing_history.append(process_info)
                return processed_data

            # 根据方法进行标准化
            if self.normalization_method == "standard":
                processed_data = self._standard_normalize(
                    processed_data, numeric_columns
                )
            elif self.normalization_method == "minmax":
                processed_data = self._minmax_normalize(processed_data, numeric_columns)
            elif self.normalization_method == "robust":
                processed_data = self._robust_normalize(processed_data, numeric_columns)
            elif self.normalization_method == "pca":
                processed_data = self._pca_transform(processed_data, numeric_columns)

            process_info["final_shape"] = processed_data.shape
            process_info["normalized_columns"] = numeric_columns

        except Exception as e:
            process_info["success"] = False
            process_info["error"] = str(e)
            print(f"Error in data normalization: {e}")

        self.processing_history.append(process_info)
        return processed_data

    def _get_numeric_columns(self, data: pd.DataFrame) -> list[str]:
        """获取数值列"""
        numeric_columns = data.select_dtypes(include=[np.number]).columns.tolist()

        # 如果指定了特征列，只使用指定的列
        if self.feature_columns:
            numeric_columns = [
                col for col in numeric_columns if col in self.feature_columns
            ]

        return numeric_columns

    def _standard_normalize(
        self, data: pd.DataFrame, columns: list[str]
    ) -> pd.DataFrame:
        """标准化 (Z-score)"""
        for col in columns:
            if col not in self.scalers:
                self.scalers[col] = StandardScaler()
                data[col] = self.scalers[col].fit_transform(data[[col]])
            else:
                data[col] = self.scalers[col].transform(data[[col]])

        return data

    def _minmax_normalize(self, data: pd.DataFrame, columns: list[str]) -> pd.DataFrame:
        """最小-最大标准化"""
        for col in columns:
            if col not in self.scalers:
                self.scalers[col] = MinMaxScaler()
                data[col] = self.scalers[col].fit_transform(data[[col]])
            else:
                data[col] = self.scalers[col].transform(data[[col]])

        return data

    def _robust_normalize(self, data: pd.DataFrame, columns: list[str]) -> pd.DataFrame:
        """鲁棒标准化"""
        for col in columns:
            if col not in self.scalers:
                self.scalers[col] = RobustScaler()
                data[col] = self.scalers[col].fit_transform(data[[col]])
            else:
                data[col] = self.scalers[col].transform(data[[col]])

        return data

    def _pca_transform(self, data: pd.DataFrame, columns: list[str]) -> pd.DataFrame:
        """PCA降维"""
        # 选择PCA列
        pca_key = "_".join(columns)

        if pca_key not in self.pca_transformers:
            # 确定PCA组件数量
            n_components = min(len(columns), len(data))
            n_components = min(n_components, self.config.get("pca_components", 0.95))

            if isinstance(n_components, float) and 0 < n_components < 1:
                # 解释方差比例
                self.pca_transformers[pca_key] = PCA(n_components=n_components)
            else:
                # 固定组件数量
                self.pca_transformers[pca_key] = PCA(n_components=n_components)

            # 执行PCA
            pca_result = self.pca_transformers[pca_key].fit_transform(data[columns])

            # 创建PCA特征列
            for i in range(pca_result.shape[1]):
                data[f"pca_{i+1}"] = pca_result[:, i]

            # 移除原始列
            data = data.drop(columns=columns)

        else:
            # 使用已有的PCA转换器
            pca_result = self.pca_transformers[pca_key].transform(data[columns])

            # 创建PCA特征列
            for i in range(pca_result.shape[1]):
                data[f"pca_{i+1}"] = pca_result[:, i]

            # 移除原始列
            data = data.drop(columns=columns)

        return data

    def inverse_transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        反向转换标准化数据

        Args:
            data: 标准化后的数据

        Returns:
            原始数据
        """
        if data.empty:
            return data

        result_data = data.copy()

        # 反向转换每个列
        for col, scaler in self.scalers.items():
            if col in result_data.columns:
                result_data[col] = scaler.inverse_transform(result_data[[col]])

        return result_data

    def save_scalers(self, filepath: str):
        """保存标准化器"""
        scaler_data = {
            "scalers": self.scalers,
            "pca_transformers": self.pca_transformers,
            "config": self.config,
            "normalization_method": self.normalization_method,
            "feature_columns": self.feature_columns,
        }

        with open(filepath, "wb") as f:
            pickle.dump(scaler_data, f)

    def load_scalers(self, filepath: str):
        """加载标准化器"""
        if os.path.exists(filepath):
            with open(filepath, "rb") as f:
                scaler_data = pickle.load(f)

            self.scalers = scaler_data["scalers"]
            self.pca_transformers = scaler_data["pca_transformers"]
            self.config = scaler_data["config"]
            self.normalization_method = scaler_data["normalization_method"]
            self.feature_columns = scaler_data["feature_columns"]

    def get_normalization_info(self) -> dict[str, Any]:
        """获取标准化信息"""
        info = {
            "method": self.normalization_method,
            "scalers_count": len(self.scalers),
            "pca_transformers_count": len(self.pca_transformers),
            "feature_columns": self.feature_columns,
            "processing_history_count": len(self.processing_history),
        }

        # 添加每个标准化器的信息
        scaler_info = {}
        for col, scaler in self.scalers.items():
            if hasattr(scaler, "mean_"):
                scaler_info[col] = {
                    "type": type(scaler).__name__,
                    "mean": scaler.mean_[0] if hasattr(scaler, "mean_") else None,
                    "scale": scaler.scale_[0] if hasattr(scaler, "scale_") else None,
                }

        info["scaler_details"] = scaler_info

        # 添加PCA信息
        pca_info = {}
        for key, pca in self.pca_transformers.items():
            pca_info[key] = {
                "n_components": pca.n_components_,
                "explained_variance_ratio": pca.explained_variance_ratio_.tolist(),
            }

        info["pca_details"] = pca_info

        return info

    def set_normalization_method(self, method: str):
        """设置标准化方法"""
        valid_methods = ["standard", "minmax", "robust", "pca"]
        if method in valid_methods:
            self.normalization_method = method
        else:
            raise ValueError(
                f"Invalid normalization method: {method}. Valid methods: {valid_methods}"
            )

    def reset(self):
        """重置标准化器"""
        self.scalers = {}
        self.pca_transformers = {}
        self.processing_history = []
