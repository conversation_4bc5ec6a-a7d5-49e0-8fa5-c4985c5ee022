"""
Confidence Scoring Backtesting Module

Provides backtesting capabilities for confidence scoring strategies and performance evaluation.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
import json
from dataclasses import dataclass

from quant.confidence_scorer import ConfidenceScorer, ConfidenceScore
from quant.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BacktestResult:
    """Results from confidence scoring backtest."""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    confidence_level_performance: Dict[str, Dict[str, float]]
    market_regime_performance: Dict[str, Dict[str, float]]
    equity_curve: List[Tuple[datetime, float]]
    trades: List[Dict[str, Any]]


class ConfidenceScoringBacktester:
    """Backtests confidence scoring strategies on historical data."""
    
    def __init__(self, initial_capital: float = 10000.0):
        """Initialize backtester with starting capital."""
        self.initial_capital = initial_capital
        self.logger = logger
        self.confidence_scorer = ConfidenceScorer()
        
        # Performance tracking
        self.equity_curve = []
        self.trades = []
        self.current_equity = initial_capital
        
        # Backtest parameters
        self.min_confidence_threshold = 0.6
        self.position_size = 0.02  # 2% of capital per trade
        self.max_positions = 3
        self.stop_loss_pct = 0.02  # 2% stop loss
        self.take_profit_pct = 0.04  # 4% take profit
    
    def run_backtest(self, market_data: pd.DataFrame, 
                    start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None,
                    strategy: str = "standard") -> BacktestResult:
        """
        Run backtest on historical market data.
        
        Args:
            market_data: Historical OHLCV data
            start_date: Optional start date for backtest
            end_date: Optional end date for backtest
            strategy: Strategy type ("standard", "adaptive", "conservative", "aggressive")
            
        Returns:
            BacktestResult object with performance metrics
        """
        try:
            # Filter data by date range
            if start_date or end_date:
                if start_date:
                    market_data = market_data[market_data.index >= start_date]
                if end_date:
                    market_data = market_data[market_data.index <= end_date]
            
            self.logger.info(f"Running backtest from {market_data.index[0]} to {market_data.index[-1]}")
            self.logger.info(f"Strategy: {strategy}, Initial capital: ${self.initial_capital:,.2f}")
            
            # Reset state
            self.reset_backtest_state()
            
            # Generate signals and simulate trades
            self._simulate_trading(market_data, strategy)
            
            # Calculate performance metrics
            result = self._calculate_performance_metrics()
            
            self.logger.info(f"Backtest completed. Win rate: {result.win_rate:.2f}%, "
                           f"Total P&L: ${result.total_pnl:,.2f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            raise
    
    def reset_backtest_state(self):
        """Reset backtest state variables."""
        self.equity_curve = [(datetime.now(), self.initial_capital)]
        self.trades = []
        self.current_equity = self.initial_capital
        self.open_positions = []
    
    def _simulate_trading(self, market_data: pd.DataFrame, strategy: str):
        """Simulate trading based on confidence scoring."""
        
        # Use rolling window for analysis
        window_size = 100  # Look back window for confidence calculation
        
        for i in range(window_size, len(market_data)):
            current_time = market_data.index[i]
            current_data = market_data.iloc[i-window_size:i]
            
            # Calculate confidence score
            if strategy == "adaptive":
                confidence_score = self.confidence_scorer.calculate_confidence_with_adaptation(
                    current_data, self.trades[-50:] if self.trades else None
                )
            else:
                confidence_score = self.confidence_scorer.calculate_confidence(current_data)
            
            # Generate trading signal based on strategy
            signal = self._generate_trading_signal(confidence_score, strategy)
            
            if signal and self._should_enter_trade(current_time):
                # Execute trade
                self._execute_trade(signal, current_time, market_data.iloc[i])
            
            # Update existing positions
            self._update_positions(current_time, market_data.iloc[i])
            
            # Update equity curve
            self.equity_curve.append((current_time, self.current_equity))
    
    def _generate_trading_signal(self, confidence_score: ConfidenceScore, strategy: str) -> Optional[Dict[str, Any]]:
        """Generate trading signal based on confidence score and strategy."""
        
        if confidence_score.overall_confidence < self.min_confidence_threshold:
            return None
        
        # Determine direction based on trend and momentum
        bullish_bias = (confidence_score.trend_score + confidence_score.momentum_score) / 2
        
        # Adjust threshold based on strategy
        if strategy == "conservative":
            threshold = 0.7
        elif strategy == "aggressive":
            threshold = 0.55
        else:  # standard
            threshold = 0.6
        
        if bullish_bias > threshold:
            direction = "LONG"
        elif bullish_bias < (1 - threshold):
            direction = "SHORT"
        else:
            return None
        
        # Calculate position size based on confidence
        position_size = self._calculate_position_size(confidence_score.overall_confidence, strategy)
        
        return {
            "direction": direction,
            "confidence_score": confidence_score.overall_confidence,
            "confidence_breakdown": {
                "trend_score": confidence_score.trend_score,
                "momentum_score": confidence_score.momentum_score,
                "volatility_score": confidence_score.volatility_score,
                "volume_score": confidence_score.volume_score,
                "market_regime_score": confidence_score.market_regime_score,
                "market_regime": confidence_score.calculation_details.get('market_regime', 'unknown')
            },
            "position_size": position_size,
            "signal_strength": self.confidence_scorer.get_signal_strength(confidence_score.overall_confidence).value
        }
    
    def _should_enter_trade(self, current_time: datetime) -> bool:
        """Check if we should enter a new trade."""
        # Check maximum positions
        if len(self.open_positions) >= self.max_positions:
            return False
        
        # Check time since last trade (minimum 1 hour)
        if self.trades:
            last_trade_time = self.trades[-1].get('exit_time', self.trades[-1]['entry_time'])
            time_diff = current_time - last_trade_time
            if time_diff < timedelta(hours=1):
                return False
        
        return True
    
    def _calculate_position_size(self, confidence_score: float, strategy: str) -> float:
        """Calculate position size based on confidence and strategy."""
        base_size = self.current_equity * self.position_size
        
        # Adjust based on confidence
        if strategy == "aggressive":
            multiplier = 1.0 + (confidence_score - 0.5) * 2
        elif strategy == "conservative":
            multiplier = 0.5 + confidence_score * 0.5
        else:  # standard
            multiplier = 0.7 + confidence_score * 0.6
        
        return base_size * multiplier
    
    def _execute_trade(self, signal: Dict[str, Any], entry_time: datetime, market_data: pd.Series):
        """Execute a trade."""
        position_size = signal['position_size']
        direction = signal['direction']
        entry_price = market_data['close']
        
        # Calculate stop loss and take profit prices
        if direction == "LONG":
            stop_loss_price = entry_price * (1 - self.stop_loss_pct)
            take_profit_price = entry_price * (1 + self.take_profit_pct)
        else:  # SHORT
            stop_loss_price = entry_price * (1 + self.stop_loss_pct)
            take_profit_price = entry_price * (1 - self.take_profit_pct)
        
        position = {
            "entry_time": entry_time,
            "direction": direction,
            "entry_price": entry_price,
            "position_size": position_size,
            "stop_loss": stop_loss_price,
            "take_profit": take_profit_price,
            "confidence_score": signal['confidence_score'],
            "confidence_breakdown": signal['confidence_breakdown'],
            "signal_strength": signal['signal_strength'],
            "status": "OPEN"
        }
        
        self.open_positions.append(position)
        
        self.logger.debug(f"Entered {direction} position at {entry_price:.2f}, "
                         f"size: ${position_size:.2f}, confidence: {signal['confidence_score']:.3f}")
    
    def _update_positions(self, current_time: datetime, market_data: pd.Series):
        """Update and exit positions based on stop loss/take profit."""
        current_price = market_data['close']
        
        positions_to_close = []
        
        for position in self.open_positions:
            should_close = False
            exit_reason = ""
            exit_price = current_price
            
            # Check stop loss
            if position['direction'] == "LONG" and current_price <= position['stop_loss']:
                should_close = True
                exit_reason = "STOP_LOSS"
                exit_price = position['stop_loss']
            elif position['direction'] == "SHORT" and current_price >= position['stop_loss']:
                should_close = True
                exit_reason = "STOP_LOSS"
                exit_price = position['stop_loss']
            
            # Check take profit
            elif position['direction'] == "LONG" and current_price >= position['take_profit']:
                should_close = True
                exit_reason = "TAKE_PROFIT"
                exit_price = position['take_profit']
            elif position['direction'] == "SHORT" and current_price <= position['take_profit']:
                should_close = True
                exit_reason = "TAKE_PROFIT"
                exit_price = position['take_profit']
            
            # Close position if conditions met
            if should_close:
                position['exit_time'] = current_time
                position['exit_price'] = exit_price
                position['exit_reason'] = exit_reason
                position['status'] = "CLOSED"
                
                # Calculate P&L
                if position['direction'] == "LONG":
                    pnl = (exit_price - position['entry_price']) * (position['position_size'] / position['entry_price'])
                else:  # SHORT
                    pnl = (position['entry_price'] - exit_price) * (position['position_size'] / position['entry_price'])
                
                position['pnl'] = pnl
                position['status'] = "WIN" if pnl > 0 else "LOSS"
                
                # Update equity
                self.current_equity += pnl
                
                # Add to trades list
                self.trades.append(position)
                positions_to_close.append(position)
                
                self.logger.debug(f"Closed {position['direction']} position at {exit_price:.2f}, "
                               f"P&L: ${pnl:.2f}, reason: {exit_reason}")
        
        # Remove closed positions from open positions
        for position in positions_to_close:
            self.open_positions.remove(position)
    
    def _calculate_performance_metrics(self) -> BacktestResult:
        """Calculate comprehensive performance metrics."""
        
        if not self.trades:
            return BacktestResult(
                total_trades=0, winning_trades=0, losing_trades=0, win_rate=0.0,
                total_pnl=0.0, avg_win=0.0, avg_loss=0.0, profit_factor=0.0,
                max_drawdown=0.0, sharpe_ratio=0.0,
                confidence_level_performance={}, market_regime_performance={},
                equity_curve=self.equity_curve, trades=[]
            )
        
        # Basic metrics
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t['pnl'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_pnl = sum(t['pnl'] for t in self.trades)
        
        wins = [t['pnl'] for t in self.trades if t['pnl'] > 0]
        losses = [t['pnl'] for t in self.trades if t['pnl'] < 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else 0
        
        # Calculate max drawdown
        equity_values = [eq[1] for eq in self.equity_curve]
        max_drawdown = self._calculate_max_drawdown(equity_values)
        
        # Calculate Sharpe ratio (simplified, using daily returns)
        if len(equity_values) > 1:
            returns = np.diff(equity_values) / equity_values[:-1]
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Performance by confidence level
        confidence_performance = self._analyze_confidence_level_performance()
        
        # Performance by market regime
        regime_performance = self._analyze_market_regime_performance()
        
        return BacktestResult(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            total_pnl=total_pnl,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            confidence_level_performance=confidence_performance,
            market_regime_performance=regime_performance,
            equity_curve=self.equity_curve,
            trades=self.trades
        )
    
    def _calculate_max_drawdown(self, equity_values: List[float]) -> float:
        """Calculate maximum drawdown from equity curve."""
        if not equity_values:
            return 0
        
        peak = equity_values[0]
        max_drawdown = 0
        
        for value in equity_values[1:]:
            if value > peak:
                peak = value
            
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        return max_drawdown
    
    def _analyze_confidence_level_performance(self) -> Dict[str, Dict[str, float]]:
        """Analyze performance by confidence level."""
        confidence_levels = {
            "low": (0.0, 0.6),
            "medium": (0.6, 0.8),
            "high": (0.8, 1.0)
        }
        
        performance = {}
        
        for level, (min_conf, max_conf) in confidence_levels.items():
            level_trades = [t for t in self.trades if min_conf <= t['confidence_score'] < max_conf]
            
            if level_trades:
                wins = [t for t in level_trades if t['pnl'] > 0]
                win_rate = (len(wins) / len(level_trades) * 100) if level_trades else 0
                avg_pnl = np.mean([t['pnl'] for t in level_trades]) if level_trades else 0
                
                performance[level] = {
                    "trade_count": len(level_trades),
                    "win_rate": round(win_rate, 2),
                    "avg_pnl": round(avg_pnl, 2)
                }
            else:
                performance[level] = {
                    "trade_count": 0,
                    "win_rate": 0.0,
                    "avg_pnl": 0.0
                }
        
        return performance
    
    def _analyze_market_regime_performance(self) -> Dict[str, Dict[str, float]]:
        """Analyze performance by market regime."""
        regimes = {}
        
        for trade in self.trades:
            regime = trade.get('confidence_breakdown', {}).get('market_regime', 'unknown')
            if regime not in regimes:
                regimes[regime] = []
            regimes[regime].append(trade)
        
        performance = {}
        
        for regime, regime_trades in regimes.items():
            if regime_trades:
                wins = [t for t in regime_trades if t['pnl'] > 0]
                win_rate = (len(wins) / len(regime_trades) * 100) if regime_trades else 0
                avg_pnl = np.mean([t['pnl'] for t in regime_trades]) if regime_trades else 0
                
                performance[regime] = {
                    "trade_count": len(regime_trades),
                    "win_rate": round(win_rate, 2),
                    "avg_pnl": round(avg_pnl, 2)
                }
        
        return performance
    
    def compare_strategies(self, market_data: pd.DataFrame, 
                         strategies: List[str] = None) -> Dict[str, BacktestResult]:
        """
        Compare multiple strategies on the same market data.
        
        Args:
            market_data: Historical OHLCV data
            strategies: List of strategies to compare
            
        Returns:
            Dictionary mapping strategy names to BacktestResult objects
        """
        if strategies is None:
            strategies = ["standard", "conservative", "aggressive", "adaptive"]
        
        results = {}
        
        for strategy in strategies:
            self.logger.info(f"Testing strategy: {strategy}")
            result = self.run_backtest(market_data, strategy=strategy)
            results[strategy] = result
        
        return results
    
    def generate_backtest_report(self, result: BacktestResult) -> str:
        """Generate a detailed backtest report."""
        report = f"""
Confidence Scoring Backtest Report
====================================

Performance Summary:
- Total Trades: {result.total_trades}
- Winning Trades: {result.winning_trades}
- Losing Trades: {result.losing_trades}
- Win Rate: {result.win_rate:.2f}%
- Total P&L: ${result.total_pnl:,.2f}
- Average Win: ${result.avg_win:,.2f}
- Average Loss: ${result.avg_loss:,.2f}
- Profit Factor: {result.profit_factor:.2f}
- Max Drawdown: {result.max_drawdown:.2f}%
- Sharpe Ratio: {result.sharpe_ratio:.2f}

Performance by Confidence Level:
"""
        
        for level, perf in result.confidence_level_performance.items():
            report += f"- {level.title()}: {perf['trade_count']} trades, {perf['win_rate']}% win rate, ${perf['avg_pnl']:.2f} avg P&L\n"
        
        report += "\nPerformance by Market Regime:\n"
        for regime, perf in result.market_regime_performance.items():
            report += f"- {regime.title()}: {perf['trade_count']} trades, {perf['win_rate']}% win rate, ${perf['avg_pnl']:.2f} avg P&L\n"
        
        return report