from quant import const


class Event:

    def __init__(
        self,
        platform,
        channels,
        symbols,
        ticker_update_callback=None,
        orderbook_update_callback=None,
        kline_update_callback=None,
        trade_update_callback=None,
        order_update_callback=None,
        position_update_callback=None,
        asset_update_callback=None,
    ):

        self._platform = platform
        self._channels = channels
        self._symbols = symbols
        self._ticker_update_callback = ticker_update_callback
        self._orderbook_update_callback = orderbook_update_callback
        self._kline_update_callback = kline_update_callback
        self._trade_update_callback = trade_update_callback
        self._order_update_callback = order_update_callback
        self._position_update_callback = position_update_callback
        self._asset_update_callback = asset_update_callback

        self._initialize_event()

    def _initialize_event(self):
        if self._platform == const.BINANCE_SPOT:
            if (
                const.TICKER in self._channels
                or const.TRADE in self._channels
                or const.ORDERBOOK in self._channels
                or const.KLINE in str(self._channels)
            ):
                from quant.platform.binance_spot import BinanceSpotPublic

                BinanceSpotPublic(
                    channels=self._channels,
                    symbols=self._symbols,
                    ticker_callback=self._ticker_update_callback,
                    orderbook_callback=self._orderbook_update_callback,
                    trade_callback=self._trade_update_callback,
                    kline_callback=self._kline_update_callback,
                )
            if (
                const.ORDER in self._channels
                or const.ASSET in self._channels
                or const.POSITION in self._channels
            ):
                from quant.platform.binance_spot import BinanceSpotPrivate

                BinanceSpotPrivate(
                    symbols=self._symbols,
                    order_callback=self._order_update_callback,
                    asset_callback=self._asset_update_callback,
                )

        elif self._platform == const.BINANCE_SWAP:
            if (
                const.TICKER in self._channels
                or const.TRADE in self._channels
                or const.ORDERBOOK in self._channels
                or const.KLINE in str(self._channels)
            ):
                from quant.platform.binance_swap import BinanceSwapPublic

                BinanceSwapPublic(
                    channels=self._channels,
                    symbols=self._symbols,
                    ticker_callback=self._ticker_update_callback,
                    orderbook_callback=self._orderbook_update_callback,
                    trade_callback=self._trade_update_callback,
                    kline_callback=self._kline_update_callback,
                )
            if (
                const.ORDER in self._channels
                or const.ASSET in self._channels
                or const.POSITION in self._channels
            ):
                from quant.platform.binance_swap import BinanceSwapPrivate

                BinanceSwapPrivate(
                    symbols=self._symbols,
                    order_callback=self._order_update_callback,
                    asset_callback=self._asset_update_callback,
                    position_callback=self._position_update_callback,
                )

        elif self._platform == const.OKX_SPOT:
            if (
                const.TICKER in self._channels
                or const.TRADE in self._channels
                or const.ORDERBOOK in self._channels
                or const.KLINE in str(self._channels)
            ):
                from quant.platform.okx_spot import OkxSpotPublic

                OkxSpotPublic(
                    channels=self._channels,
                    symbols=self._symbols,
                    ticker_callback=self._ticker_update_callback,
                    orderbook_callback=self._orderbook_update_callback,
                    trade_callback=self._trade_update_callback,
                    kline_callback=self._kline_update_callback,
                )
            if (
                const.ORDER in self._channels
                or const.ASSET in self._channels
                or const.POSITION in self._channels
            ):
                from quant.platform.okx_spot import OkxSpotPrivate

                OkxSpotPrivate(
                    symbols=self._symbols,
                    order_callback=self._order_update_callback,
                    asset_callback=self._asset_update_callback,
                )

        elif self._platform == const.OKX_SWAP:
            if (
                const.TICKER in self._channels
                or const.TRADE in self._channels
                or const.ORDERBOOK in self._channels
                or const.KLINE in str(self._channels)
            ):
                from quant.platform.okx_swap import OkxSwapPublic

                OkxSwapPublic(
                    channels=self._channels,
                    symbols=self._symbols,
                    ticker_callback=self._ticker_update_callback,
                    orderbook_callback=self._orderbook_update_callback,
                    trade_callback=self._trade_update_callback,
                    kline_callback=self._kline_update_callback,
                )
            if (
                const.ORDER in self._channels
                or const.ASSET in self._channels
                or const.POSITION in self._channels
            ):
                from quant.platform.okx_swap import OkxSwapPrivate

                OkxSwapPrivate(
                    symbols=self._symbols,
                    order_callback=self._order_update_callback,
                    asset_callback=self._asset_update_callback,
                    position_callback=self._position_update_callback,
                )
