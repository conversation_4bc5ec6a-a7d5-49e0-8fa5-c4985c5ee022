{"timestamp": "2025-08-13T09:54:42.800945", "level": "INFO", "logger": "rotation-test", "message": "line 0269 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800977", "level": "INFO", "logger": "rotation-test", "message": "line 0270 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800998", "level": "INFO", "logger": "rotation-test", "message": "line 0271 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801016", "level": "INFO", "logger": "rotation-test", "message": "line 0272 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801035", "level": "INFO", "logger": "rotation-test", "message": "line 0273 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801054", "level": "INFO", "logger": "rotation-test", "message": "line 0274 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801074", "level": "INFO", "logger": "rotation-test", "message": "line 0275 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801092", "level": "INFO", "logger": "rotation-test", "message": "line 0276 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801111", "level": "INFO", "logger": "rotation-test", "message": "line 0277 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801130", "level": "INFO", "logger": "rotation-test", "message": "line 0278 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801149", "level": "INFO", "logger": "rotation-test", "message": "line 0279 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801171", "level": "INFO", "logger": "rotation-test", "message": "line 0280 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801192", "level": "INFO", "logger": "rotation-test", "message": "line 0281 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801211", "level": "INFO", "logger": "rotation-test", "message": "line 0282 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801234", "level": "INFO", "logger": "rotation-test", "message": "line 0283 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
