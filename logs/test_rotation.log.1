{"timestamp": "2025-08-13T09:54:42.801583", "level": "INFO", "logger": "rotation-test", "message": "line 0284 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801615", "level": "INFO", "logger": "rotation-test", "message": "line 0285 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801636", "level": "INFO", "logger": "rotation-test", "message": "line 0286 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801658", "level": "INFO", "logger": "rotation-test", "message": "line 0287 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801678", "level": "INFO", "logger": "rotation-test", "message": "line 0288 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801697", "level": "INFO", "logger": "rotation-test", "message": "line 0289 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801717", "level": "INFO", "logger": "rotation-test", "message": "line 0290 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801737", "level": "INFO", "logger": "rotation-test", "message": "line 0291 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801757", "level": "INFO", "logger": "rotation-test", "message": "line 0292 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801778", "level": "INFO", "logger": "rotation-test", "message": "line 0293 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801798", "level": "INFO", "logger": "rotation-test", "message": "line 0294 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801820", "level": "INFO", "logger": "rotation-test", "message": "line 0295 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801840", "level": "INFO", "logger": "rotation-test", "message": "line 0296 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801862", "level": "INFO", "logger": "rotation-test", "message": "line 0297 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.801882", "level": "INFO", "logger": "rotation-test", "message": "line 0298 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
