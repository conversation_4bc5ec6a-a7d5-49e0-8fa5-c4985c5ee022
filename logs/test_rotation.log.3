{"timestamp": "2025-08-13T09:54:42.800336", "level": "INFO", "logger": "rotation-test", "message": "line 0254 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800369", "level": "INFO", "logger": "rotation-test", "message": "line 0255 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800388", "level": "INFO", "logger": "rotation-test", "message": "line 0256 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800407", "level": "INFO", "logger": "rotation-test", "message": "line 0257 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800426", "level": "INFO", "logger": "rotation-test", "message": "line 0258 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800445", "level": "INFO", "logger": "rotation-test", "message": "line 0259 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800464", "level": "INFO", "logger": "rotation-test", "message": "line 0260 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800482", "level": "INFO", "logger": "rotation-test", "message": "line 0261 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800501", "level": "INFO", "logger": "rotation-test", "message": "line 0262 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800519", "level": "INFO", "logger": "rotation-test", "message": "line 0263 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800538", "level": "INFO", "logger": "rotation-test", "message": "line 0264 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800556", "level": "INFO", "logger": "rotation-test", "message": "line 0265 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800576", "level": "INFO", "logger": "rotation-test", "message": "line 0266 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800595", "level": "INFO", "logger": "rotation-test", "message": "line 0267 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
{"timestamp": "2025-08-13T09:54:42.800614", "level": "INFO", "logger": "rotation-test", "message": "line 0268 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "module": "<stdin>", "function": "<module>", "line": 32}
