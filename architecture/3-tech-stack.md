# 技术栈 (<PERSON> Stack)

| 分类                    | 技术            | 建议版本     | 用途              |
| :---------------------- | :-------------- | :----------- | :---------------- |
| **语言/运行环境**       | Python          | ~3.11        | 核心开发语言      |
| **数据处理**            | Pandas          | ~2.2         | K线数据处理与分析 |
| **技术指标**            | Pandas TA       | ~0.3.14b     | 计算所有技术指标  |
| **API/WebSocket客户端** | python-binance  | ~1.0.19      | 连接币安API       |
| **任务调度**            | APScheduler     | ~3.10        | 触发定时任务      |
| **数据库**              | SQLite          | (Python内置) | 存储交易历史      |
| **数据库接口**          | SQLAlchemy Core | ~2.0         | 数据库交互        |
| **HTTP请求**            | Requests        | ~2.31        | 发送钉钉通知      |