# 技术架构文档 - v1.5 (最终版)

**项目名称**: 币安事件合约交易信号决策系统
**版本**: 1.5
**日期**: 2025年8月2日
**架构师**: <PERSON> (Architect)

---

## Sections

- [1. 引言 (Introduction)](./1-introduction.md)
- [2. 高层架构 (High Level Architecture)](./2-high-level-architecture.md)
- [3. 技术栈 (Tech Stack)](./3-tech-stack.md)
- [4. 数据模型 (Data Models)](./4-data-models.md)
- [5. 组件 (Components)](./5-components.md)
- [6. 外部API (External APIs)](./6-external-apis.md)
- [7. 核心工作流 (Core Workflows)](./7-core-workflows.md)
- [8. 数据库模式 (Database Schema)](./8-database-schema.md)
- [9. 源代码树 (Source Tree)](./9-source-tree.md)
- [10. 基础设施和部署 (Infrastructure and Deployment)](./10-infrastructure-and-deployment.md)
- [11. 错误处理策略 (Error Handling Strategy)](./11-error-handling-strategy.md)
- [12. 编码标准 (Coding Standards)](./12-coding-standards.md)
- [13. 测试策略与标准 (Test Strategy and Standards)](./13-test-strategy-and-standards.md)
- [14. 安全 (Security)](./14-security.md)
- [15. 清单检查结果报告 (Checklist Results Report)](./15-checklist-results-report.md)
- [16. 下一步 (Next Steps)](./16-next-steps.md)