### 产品需求文档 (PRD) - 最终版 v1.0

**项目名称**: 币安事件合约交易信号决策系统
**版本**: 1.0
**日期**: 2025年8月2日
**产品经理**: <PERSON> (BMad-Method)

---

#### 1. 目标和背景上下文 (Goals and Background Context)

* **目标 (Goals)**
    * 将一个复杂的、自适应的技术分析策略完全自动化，用于币安BTC/USDT事件合约。
    * 构建一个从信号生成到绩效复盘的无人干预的全功能闭环系统。
    * 通过实盘运行，验证该交易策略在真实市场上能否实现长期稳定的正向收益。
* **背景上下文 (Background Context)**
    * 本项目旨在解决在币安事件合约中，因人工操作无法精确、持续执行复杂策略而导致的效率低下和决策不一致问题。本系统通过整合市场状态识别、信号置信度评分和内置风险过滤器，旨在填补这一空白，将一个理论上优秀的策略构想转化为一个稳定、可靠且可验证的自动化交易实体。
* **变更日志 (Change Log)**
    | 日期         | 版本 | 描述                        | 作者      |
    | :----------- | :--- | :-------------------------- | :-------- |
    | 2025年8月2日 | 1.0  | 经过多轮讨论，最终版PRD定稿 | John (PM) |

---

#### 2. 需求 (Requirements)

* **功能性需求 (Functional Requirements)**
    * **FR1-FR9**: (核心策略逻辑) 系统必须实现基于30分钟K线、多周期共振、K线形态、多指标过滤、市场状态识别、置信度评分和趋势强度过滤的完整信号生成逻辑。
    * **FR10**: (动态资金) 系统信号需包含基于每日胜率动态计算出的建议投注额（5/20/50 USDT）。
    * **FR11-FR12**: (钉钉通知) 系统必须能实时发送包含特定关键词的钉钉通知。
    * **FR13-FR14**: (自动结算) 系统必须能自动监控10分钟合约到期，并判断、记录交易结果。
    * **FR15**: (富文本历史) 所有交易历史必须持久化存储，并包含完整的决策依据（入场分钟数、市场状态、分数、形态、指标确认列表等）。
    * **FR16-FR17**: (胜率与导出) 系统必须能统计每日胜率（每日零点重置），并能自动将前一日的富文本历史导出为`.jsonl`文件。
* **非功能性需求 (Non-Functional Requirements)**
    * **NFR1**: (性能) 信号生成全流程必须在1分钟内完成。系统不负责下单。
    * **NFR2**: (可靠性) 核心逻辑Uptime > 99.9%。
    * **NFR3**: (可维护性) 遵循约定目录结构；优先复用现有模块，必要时可新建。
    * **NFR4**: (通知性能) 钉钉通知延迟 < 2秒。
    * **NFR5**: (测试) MVP阶段不要求编写自动化测试。

---

#### 3. 技术假设 (Technical Assumptions)

* **实现与部署模式**: **Python 脚本化运行**。整个系统通过 `python3 main.py` 启动，避免复杂的Web框架和容器化技术。
* **服务架构**: **单体应用 (Monolith)** 脚本。
* **进程守护与可靠性**: 为确保高可靠性，脚本必须由操作系统内置的服务管理器守护。在 **Linux** 环境下使用 **`systemd`**；在 **macOS** 环境下使用 **`launchd`**。
* **数据库**: 优先考虑轻量级数据库 **SQLite**。

---

#### 4. 史诗列表 (Epic List)

* **史诗 1: 项目基石与核心数据管道**: 搭建应用基础结构，并实现稳定、低延迟的数据获取（WebSocket为主，REST为辅）。
* **史诗 2: 信号生成与策略实现**: 构建决策系统的大脑，实现完整的多层次技术分析与动态决策逻辑，输出带有置信度分数的信号。
* **史诗 3: 执行、分析与反馈闭环**: 完成操作闭环，实现钉钉通知、富文本交易记录、自动结算、每日胜率统计、动态资金管理集成和每日历史自动导出。

---

#### 5. 史诗详情 (Epic Details)

* (注：此处为每个史诗下详细的用户故事和验收标准。这些内容在我们之前的讨论中已逐一确认，并将作为开发阶段的直接输入。)

---

#### 6. 下一步 (Next Steps)

**PRD移交**:
这份PRD文档现已完成，正式移交给项目架构师（Architect）。

**致架构师的提示 (Architect Prompt)**:
“请基于这份已获批准的PRD v1.0，为‘币安事件合约交易信号决策系统’创建一份详细的**技术架构文档**。请重点关注：
1.  **模块设计**: 基于Python脚本化运行模式，设计各个核心模块（数据、指标、策略、调度、通知、数据库等）的具体实现方案和交互接口。
2.  **数据库Schema**: 为`trade_history`富文本记录表设计详细的字段结构。
3.  **部署规范**: 提供`systemd`或`launchd`的推荐配置文件模板。
4.  **编码标准**: 确立项目遵循的核心编码规范和目录结构。”