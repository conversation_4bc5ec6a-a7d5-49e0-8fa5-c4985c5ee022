### 技术架构文档 - v1.5 (最终版)

**项目名称**: 币安事件合约交易信号决策系统
**版本**: 1.5
**日期**: 2025年8月2日
**架构师**: <PERSON> (Architect)

---

#### 1. 引言 (Introduction)

本文档概述了 **币安事件合约交易信号决策系统** 的整体项目架构。其主要目标是为AI驱动的开发提供指导性蓝图，确保技术选型、组件交互和代码结构的一致性。本项目为纯后端服务，不涉及前端架构。根据PRD的“部署简单性”要求，本项目将不使用任何复杂的启动模板或Web框架，将被实现为一个独立的Python脚本应用。

---

#### 2. 高层架构 (High Level Architecture)

* **技术摘要**: 本系统将作为一个单一的、持久运行的Python进程（单体应用）来实现，并封装在一个单一的代码仓库（Monorepo）中。它将通过WebSocket与币安建立实时数据流，通过内置的调度器执行一个核心的、事件驱动的分析循环。所有交易历史和分析数据将存储在本地的SQLite数据库中，并通过独立的模块向钉钉发送通知。
* **架构风格**: 采用**事件驱动**的单体脚本应用架构。
* **架构与设计模式**:
    * **调度器模式 (Scheduler Pattern)**
    * **观察者模式 (Observer Pattern)**
    * **存储库模式 (Repository Pattern)**
    * **策略模式 (Strategy Pattern)**

---

#### 3. 技术栈 (Tech Stack)

| 分类                    | 技术            | 建议版本     | 用途              |
| :---------------------- | :-------------- | :----------- | :---------------- |
| **语言/运行环境**       | Python          | ~3.11        | 核心开发语言      |
| **数据处理**            | Pandas          | ~2.2         | K线数据处理与分析 |
| **技术指标**            | Pandas TA       | ~0.3.14b     | 计算所有技术指标  |
| **API/WebSocket客户端** | python-binance  | ~1.0.19      | 连接币安API       |
| **任务调度**            | APScheduler     | ~3.10        | 触发定时任务      |
| **数据库**              | SQLite          | (Python内置) | 存储交易历史      |
| **数据库接口**          | SQLAlchemy Core | ~2.0         | 数据库交互        |
| **HTTP请求**            | Requests        | ~2.31        | 发送钉钉通知      |

---

#### 4. 数据模型 (Data Models)

* **TradeHistory (交易历史记录)**: 核心数据实体，记录交易信号及完整的决策依据（如信号时间、方向、价格、状态、结果、置信度、触发模式、确认指标等）。

---

#### 5. 组件 (Components)

1.  **主调度器 (Scheduler)**: 应用的“心脏”，管理所有定时任务，包括30分钟分析、10分钟结算以及5分钟的“自我修复的对账任务”。
2.  **配置管理器 (ConfigManager)**: 加载并提供对 `config.json` 的访问。
3.  **币安客户端 (BinanceClient)**: 封装所有与币安的通信（WebSocket/REST）。
4.  **数据库管理器 (DatabaseManager)**: 封装所有与SQLite的交互。
5.  **分析引擎 (AnalysisEngine)**: 系统的“大脑”，执行所有策略逻辑。
6.  **通知管理器 (Notifier)**: 格式化并发送钉钉通知。
7.  **结算检查器 (SettlementChecker)**: 负责检查交易结果和执行对账。

---

#### 6. 外部API (External APIs)

* **Binance API**: 用于获取实时/历史数据和价格查询。
* **DingTalk Custom Bot API**: 用于发送信号通知，使用预定义的V3 markdown消息模板。

---

#### 7. 核心工作流 (Core Workflows)

* 采用Mermaid时序图定义了从“30分钟分析触发”到“10分钟后结算完成”的完整组件交互流程，并包含了“自我修复的对账任务”以确保健壮性。

---

#### 8. 数据库模式 (Database Schema)

* 提供了`TradeHistory`表的完整SQL DDL，包括所有字段、数据类型、约束，并为`status`和`signal_timestamp`字段创建了复合索引以优化查询性能。

---

#### 9. 源代码树 (Source Tree)

* 定义了完整的项目目录结构，将核心逻辑封装在`quant/`包中，并将策略相关模块放在`quant/strategies/`下，以实现高内聚和低耦合。

---

#### 10. 基础设施和部署 (Infrastructure and Deployment)

* **策略**: **通过自动化脚本进行部署 (Deployment via Automated Script)**。
* **流程**: 通过执行`scripts/deploy.sh`脚本，自动完成拉取代码、更新依赖和重启应用服务。
* **环境**: 定义了“开发”（本地）和“生产”（Linux VPS）两个环境。

---

#### 11. 错误处理策略 (Error Handling Strategy)

* 采用自定义异常、结构化JSON日志、外部API调用重试机制，并让致命错误导致程序优雅退出，由进程守护系统重启，以确保系统稳定。

---

#### 12. 编码标准 (Coding Standards)

* 强制使用`Black`和`Ruff`进行代码格式化和检查，并定义了清晰的命名约定和5条必须遵守的关键开发规则。

---

#### 13. 测试策略与标准 (Test Strategy and Standards)

* 根据PRD，MVP阶段采用“通过实际运行进行验证”的理念，不编写自动化测试。

---

#### 14. 安全 (Security)

* 核心要求是通过`.gitignore`和严格的文件权限保护`config.json`中的API密钥，严禁硬编码凭证。

---

#### 15. 清单检查结果报告 (Checklist Results Report)

* 架构自检结果为 **高就绪度 (High Readiness)**，可以进入开发阶段。

---

#### 16. 下一步 (Next Steps)

* **开发启动**: 从 **史诗 1: 项目基石与核心数据管道** 开始，实现 **用户故事 1.1: 项目初始化**。