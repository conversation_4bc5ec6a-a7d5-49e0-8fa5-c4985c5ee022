### 项目简报 (最终版 v1.0)：币安事件合约交易信号决策系统

**文档版本**: 1.0
**创建日期**: 2025年8月2日
**分析师**: Mary (BMad-Method)

---

#### 1. 执行摘要 (Executive Summary)

* **产品概念**: 一个针对币安BTC/USDT事件合约的自动化交易信号系统，旨在实现从市场数据分析、策略决策、信号生成到结果追踪的全流程闭环。
* **解决的核心问题**: 为特定周期（10分钟到期）和规则（30分钟K线分析）的事件合约，提供一套系统化、自动化且具备概率优势的交易决策方案，替代人工判断或非系统化交易带来的不一致性和效率低下问题。
* **目标市场/用户**: 量化交易策略开发者/执行者（即您本人）。
* **核心价值主张**: 通过一个高度集成的自动化系统，在币安事件合约严格的10分钟到期和30分钟分析周期约束下，实现并验证一套复杂的、以概率优势为核心的交易策略。其核心价值在于：
    * **策略一致性**: 严格执行预设的多层次技术分析规则，杜绝人工交易中的情绪干扰和执行偏差。
    * **高置信度信号**: 综合运用多时间周期（1分钟至1天）共振、K线形态识别及多种技术指标（RSI, MACD, 布林带等）作为过滤器，旨在生成经过交叉验证的高可信度交易信号。
    * **入场时机优化**: 在30分钟K线周期的前15分钟内，主动寻找技术上的超买/超卖区或关键支撑/阻力位作为入场点，以获取更优的风险回报比。
    * **闭环绩效追踪**: 实现从信号生成、钉钉实时推送、自动结算检查到交易历史复盘的完整闭环，并通过动态资金管理规则（基于胜率调整投注额）对策略表现进行实时反馈和调整。

---

#### 2. 问题陈述 (Problem Statement)

* **执行复杂性**: 在10分钟到期的短周期内，依赖人工持续、精确地执行一套包含多时间周期、多指标、多形态的复杂分析策略，几乎是不可能的。
* **决策僵化**: 简单的自动化脚本往往使用固定参数，无法适应市场的动态变化（如趋势市与震荡市的切换），导致策略时效性差。
* **指标陷阱风险**: 传统指标在极端或强趋势行情中容易“钝化”或产生欺骗性信号，若无相应风控机制，极易陷入“逆势摸顶/抄底”的陷阱，导致连续且重大的亏损。

---

#### 3. 拟议解决方案 (Proposed Solution)

* **核心概念**: 构建一个全自动、闭环的量化交易系统，该系统不仅能执行交易信号，更能基于市场状态进行**动态、自适应**的决策。
* **关键系统逻辑与架构**:
    1.  **动态分析框架**: 系统将内置一个**市场状态（Market Regime）识别模块**。它会利用`ADX`、`ATR`等指标，自动将市场划分为“强趋势市”或“区间震荡市”。后续所有的信号生成逻辑和指标参数门槛，都将根据当前的市场状态进行自适应调整。
    2.  **置信度评分引擎**: 取代简单的“买/卖”判断，系统将对每一个潜在的交易机会，通过K线形态、多周期共振、指标确认数量等维度，进行综合计算，得出一个**“置信度分数”**。此分数将直接决定后续的行动，并与动态资金管理模块联动。
    3.  **趋势强度过滤器 (内置风控)**: 为主动规避在强趋势行情中“逆势交易”的巨大风险，系统将强制启用趋势强度过滤器。当`ADX`指标显示市场处于强趋势状态时，系统将**自动屏蔽所有与主趋势相反的交易信号**。
* **高层愿景**: 打造一个不仅能“执行”，更能“思考”和“适应”的智能化交易决策系统，以在特定规则的金融衍生品市场中，获得长期、稳定的概率优势。

---

#### 4. 目标与成功指标 (Goals & Success Metrics)

* **业务目标**
    * **策略自动化**: 将一个复杂的、自适应的技术分析策略完全自动化。
    * **系统闭环**: 构建一个从信号生成到绩效复盘的无人干预的全功能闭环系统。
    * **盈利验证**: 通过实盘运行，验证该交易策略在真实市场上能否实现长期稳定的正向收益。
* **关键绩效指标 (KPIs)**
    * **每日胜率 (Daily Win Rate)**: `(当日盈利次数 / 当日总交易次数) * 100%`。**目标: > 65%**。
    * **信号频率 (Signal Frequency)**: **目标: 2次/小时** (严格对应每30分钟K线一次)。
    * **系统正常运行时间 (System Uptime)**: **目标: > 99.9%**。
    * **通知延迟 (Notification Latency)**: **目标: < 2秒**。

---

#### 5. MVP范围 (MVP Scope)

* **核心功能 (Must Have)**
    1.  **信号生成器**: 包含市场状态识别、置信度评分和趋势强度过滤器。
    2.  **动态资金管理**: 实现基于胜率的5/20/50 USDT动态投注额调整。
    3.  **钉钉通知模块**: 发送实时交易信号。
    4.  **信号结算检查器**: 自动监控和结算到期合约。
    5.  **交易历史跟踪器**: 持久化存储交易记录，并支持导出。
* **MVP范围之外 (Out of Scope)**
    * 除BTC/USDT外的其他交易对。
    * 除10分钟外的其他周期事件合约。
    * 更复杂的资金管理模型（如马丁格尔策略）。
    * 用于数据分析的图形化用户界面(GUI)。
    * 完整的历史数据回测框架（建议作为V2功能）。

---

#### 6. 技术考量 (Technical Considerations)

* **代码组织**: 所有策略类文件必须放置在 `quant/strategies/` 目录下。
* **系统集成**: 必须复用项目中已有的配置管理 (`config.json`)、数据库连接、日志记录和错误处理机制。
* **架构风格**: 采用模块化设计，将信号生成、通知、结算、历史追踪等功能解耦，便于独立维护和测试。

---

#### 7. 约束与假设 (Constraints & Assumptions)

* **核心约束**
    * **时间约束**: 信号分析、生成、执行、到期必须在同一根30分钟K线内完成。
    * **频率约束**: 每根30分钟K线只生成一次信号。
    * **合约约束**: 盈利80%，亏损100%，平局返还。
* **核心假设**
    * **环境假设**: 假定项目已存在可复用的基础设施（如数据库连接、日志框架）。
    * **API假设**: 假定币安的API是稳定、可靠的，并且能提供所有需要的K线数据和下单接口。

---

#### 8. 风险与开放问题 (Risks & Open Questions)

* **主要风险**
    * **策略风险**: 本文档中定义的交易策略在真实市场中可能无法盈利。
    * **执行风险**: 网络延迟或API性能问题可能导致无法在最佳时机入场。
    * **API依赖风险**: 币安API的变更、费率限制或停机将直接影响本系统。
* **待解决的关键问题**
    * 系统的核心开发语言和框架是什么？（例如：Python with CCXT, Node.js?）
    * 当币安API不可用或返回错误时，系统的容错和重试机制是怎样的？
    * 在正式投入实盘资金前，是否有手动的模拟盘验证阶段？

---

#### 9. 后续步骤 (Next Steps)

* **第一步**: 项目所有者（您）正式批准此项目简报。
* **第二步**: 将此简报交由项目架构师或核心开发人员，用于创建详细的**技术架构文档**和**产品需求文档(PRD)**。
* **第三步**: 基于架构文档，进行具体的开发任务拆解和排期。