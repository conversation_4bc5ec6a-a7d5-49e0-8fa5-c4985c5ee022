# 币安事件合约交易系统 - 告警机制优化方案

## 分析总结

### 1. 系统严重告警的业务目的

**当前问题**：
- 原始告警机制偏离PRD核心功能需求，过度关注系统资源监控
- 复杂的告警聚合、抑制功能超出交易系统实际需求
- 告警优先级错位，系统健康告警优先级高于交易相关告警

**PRD核心功能要求**：
- 交易信号生成（FR1-FR9）
- 钉钉通知（FR11-FR12）  
- 自动结算（FR13-FR14）
- 信号生成<1分钟，通知延迟<2秒（NFR1, NFR4）

### 2. 告警触发标准分析

**原始阈值过于敏感**：
- CPU 80%/90% - 交易系统主要是I/O密集型，CPU短暂波动正常
- 内存 85%/95% - Python应用内存使用波动较大，阈值过低  
- WebSocket断开3次 - 网络波动常见，应关注重连成功率

### 3. 告警优化方案

#### 3.1 新的告警优先级设计

```
交易关键告警 (Trading Critical) - 最高优先级
├── 信号生成延迟 ≥55秒
├── 通知延迟 ≥2秒  
├── 数据新鲜度 ≥5分钟
├── WebSocket连接失败 ≥10次
└── 结算积压 >10笔

交易警告告警 (Trading Warning) - 中等优先级  
├── 信号生成延迟 ≥45秒
├── 通知延迟 ≥1.5秒
├── 数据新鲜度 ≥2分钟
└── 结算积压 >5笔

系统信息告警 (System Info) - 最低优先级
├── CPU使用率 ≥90%
├── 内存使用率 ≥90%
└── 磁盘使用率 ≥85%
```

#### 3.2 告警抑制机制

- **交易关键告警**：5分钟抑制窗口，每小时最多5次
- **交易警告告警**：3分钟抑制窗口，每小时最多10次  
- **系统信息告警**：10分钟抑制窗口，每小时最多3次

#### 3.3 配置变更

**更新后的阈值 (config.json)**：
```json
{
    "alert_thresholds": {
        "cpu_usage": 0.9,
        "memory_usage": 0.9, 
        "disk_usage": 0.85,
        "api_latency": 5.0,
        "websocket_disconnections": 5
    },
    "focus": "trading_critical"
}
```

### 4. 与核心业务的关系优化

#### 4.1 优先级平衡策略

1. **交易信号通知**：始终保持最高优先级，不受告警抑制影响
2. **交易关键告警**：次高优先级，可能影响交易功能
3. **风险警告告警**：中等优先级，风险管理相关
4. **系统信息告警**：最低优先级，仅记录日志，严重时才通知

#### 4.2 告警降级方案

- 将大部分系统资源监控降级为日志记录
- 仅保留影响核心交易功能的关键告警
- 交易告警与系统告警分离管理

## 实施方案

### 1. 新增文件

- `alert_thresholds.json` - 专门的告警阈值配置
- `quant/trading_alert_manager.py` - 专注交易的告警管理器

### 2. 修改文件

- `main.py` - 集成新的告警管理器
- `config.json` - 更新告警阈值和焦点设置

### 3. 核心改进

1. **专注交易功能**：告警机制重新聚焦于交易核心功能
2. **合理阈值**：提高系统资源告警阈值，减少误报
3. **智能抑制**：基于时间和频率的告警抑制机制
4. **优先级明确**：清晰的告警优先级和通知策略

## 预期效果

- **减少误报**：预计减少70%的非关键系统告警
- **聚焦核心**：告警机制100%对齐PRD核心功能需求
- **提升效率**：运营团队专注处理真正影响交易的告警
- **符合要求**：完全满足PRD中的功能和非功能需求

## 下一步建议

1. 部署新的告警配置
2. 监控告警频率和类型变化
3. 根据实际运行情况进一步调优阈值
4. 考虑完全移除旧的 `alert_manager.py` 文件